# Database Schema Refactoring Guide

This guide provides step-by-step instructions for refactoring database schemas to use shared audit fields and implement proper uniqueness constraints with soft delete support.

## 📋 Overview

We've standardized our audit field pattern to use shared `auditFields` from `common-fields.schema.ts` and implemented consistent soft delete handling across all schemas.

## ✅ Successfully Refactored Schemas

- ✅ `accounts.schema.ts` - Updated unique constraints to use `isDeleted`
- ✅ `allowance-types.schema.ts` - Full refactor with shared audit fields and name uniqueness

## 🔄 Refactoring Checklist

### Phase 1: Import Updates
- [ ] Remove unused imports (`timestamp`, `users` if using shared audit fields)
- [ ] Add `import { sql } from 'drizzle-orm'` for WHERE clauses
- [ ] Add `import { auditFields } from './common-fields.schema'`

### Phase 2: Schema Field Updates
- [ ] Replace individual audit field definitions with `...auditFields`
- [ ] Ensure all business-specific fields are properly defined
- [ ] Add appropriate indexes for new unique constraints

### Phase 3: Constraint Updates
- [ ] Update existing unique constraints to use `${t.isDeleted} = false`
- [ ] Add name uniqueness where appropriate
- [ ] Ensure all constraints respect soft delete pattern

## 📝 Step-by-Step Instructions

### Step 1: Update Imports

**Before:**
```typescript
import {
  timestamp,
  pgTable,
  text,
  uuid,
  // ... other imports
} from 'drizzle-orm/pg-core';
import { isNull } from 'drizzle-orm';
import { users } from './users.schema';
```

**After:**
```typescript
import {
  pgTable,
  text,
  uuid,
  // ... other imports (remove timestamp if not used elsewhere)
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { auditFields } from './common-fields.schema';
// Remove users import if only used for audit fields
```

### Step 2: Replace Individual Audit Fields

**Before:**
```typescript
export const myTable = pgTable('my_table', {
  id: uuid('id').defaultRandom().primaryKey(),
  // ... business fields
  createdBy: uuid('created_by').notNull().references(() => users.id),
  updatedBy: uuid('updated_by').references(() => users.id),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  isDeleted: boolean('is_deleted').default(false).notNull(),
});
```

**After:**
```typescript
export const myTable = pgTable('my_table', {
  id: uuid('id').defaultRandom().primaryKey(),
  businessId: uuid('business_id').notNull().references(() => business.id),
  
  // ... business fields
  
  // Audit fields
  ...auditFields,
});
```

### Step 3: Update Unique Constraints

**Before:**
```typescript
(t) => ({
  uniqueBusinessCode: uniqueIndex('my_table_business_code_unique')
    .on(t.businessId, t.code)
    .where(isNull(t.deletedAt)), // Old pattern
})
```

**After:**
```typescript
(t) => ({
  // Indexes for performance
  businessIdIndex: index('my_table_business_id_index').on(t.businessId),
  codeIndex: index('my_table_code_index').on(t.code),
  nameIndex: index('my_table_name_index').on(t.name), // Add if applicable
  
  // Unique constraints
  uniqueBusinessCode: uniqueIndex('my_table_business_code_unique')
    .on(t.businessId, t.code)
    .where(sql`${t.isDeleted} = false`), // New pattern
  uniqueBusinessName: uniqueIndex('my_table_business_name_unique')
    .on(t.businessId, t.name)
    .where(sql`${t.isDeleted} = false`), // Add name uniqueness if applicable
})
```

## 🎯 Current Audit Fields Pattern

Our standardized `auditFields` includes:

```typescript
export const auditFields = {
  createdBy: uuid('created_by').notNull().references(() => users.id),
  updatedBy: uuid('updated_by').references(() => users.id),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  isDeleted: boolean('is_deleted').default(false).notNull(),
} as const;
```

## 🔍 Common Patterns to Implement

### 1. Name + Code Uniqueness Pattern
Most business entities should have both name and code uniqueness:

```typescript
// Unique constraints
uniqueBusinessCode: uniqueIndex('table_business_code_unique')
  .on(t.businessId, t.code)
  .where(sql`${t.isDeleted} = false`),
uniqueBusinessName: uniqueIndex('table_business_name_unique')
  .on(t.businessId, t.name)
  .where(sql`${t.isDeleted} = false`),
```

### 2. Performance Indexes Pattern
Add indexes for commonly queried fields:

```typescript
// Indexes for performance
businessIdIndex: index('table_business_id_index').on(t.businessId),
codeIndex: index('table_code_index').on(t.code),
nameIndex: index('table_name_index').on(t.name),
statusIndex: index('table_status_index').on(t.status), // if applicable
```

### 3. Conditional Unique Constraints
For nullable fields that should be unique when present:

```typescript
uniqueBusinessEmail: uniqueIndex('table_business_email_unique')
  .on(t.businessId, t.email)
  .where(sql`${t.email} IS NOT NULL AND ${t.isDeleted} = false`),
```

## 🚀 Example: Complete Refactor

### Before (Individual Audit Fields)
```typescript
export const categories = pgTable('categories', {
  id: uuid('id').defaultRandom().primaryKey(),
  businessId: uuid('business_id').notNull().references(() => business.id),
  categoryName: text('category_name').notNull(),
  categoryCode: text('category_code').notNull(),
  description: text('description'),
  isActive: boolean('is_active').default(true).notNull(),
  
  // Individual audit fields
  createdBy: uuid('created_by').notNull().references(() => users.id),
  updatedBy: uuid('updated_by').references(() => users.id),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  isDeleted: boolean('is_deleted').default(false).notNull(),
}, (t) => ({
  uniqueBusinessCode: uniqueIndex('categories_business_code_unique')
    .on(t.businessId, t.categoryCode)
    .where(isNull(t.deletedAt)), // Wrong field reference
}));
```

### After (Shared Audit Fields)
```typescript
export const categories = pgTable('categories', {
  id: uuid('id').defaultRandom().primaryKey(),
  businessId: uuid('business_id').notNull().references(() => business.id),
  categoryName: text('category_name').notNull(),
  categoryCode: text('category_code').notNull(),
  description: text('description'),
  isActive: boolean('is_active').default(true).notNull(),
  
  // Shared audit fields
  ...auditFields,
}, (t) => ({
  // Indexes for performance
  businessIdIndex: index('categories_business_id_index').on(t.businessId),
  categoryCodeIndex: index('categories_category_code_index').on(t.categoryCode),
  categoryNameIndex: index('categories_category_name_index').on(t.categoryName),
  isActiveIndex: index('categories_is_active_index').on(t.isActive),
  
  // Unique constraints
  uniqueBusinessCategoryCode: uniqueIndex('categories_business_category_code_unique')
    .on(t.businessId, t.categoryCode)
    .where(sql`${t.isDeleted} = false`),
  uniqueBusinessCategoryName: uniqueIndex('categories_business_category_name_unique')
    .on(t.businessId, t.categoryName)
    .where(sql`${t.isDeleted} = false`),
}));
```

## ⚠️ Common Pitfalls to Avoid

1. **Wrong field reference in constraints:**
   - ❌ `.where(isNull(t.deletedAt))` (when using `isDeleted` pattern)
   - ✅ `.where(sql\`${t.isDeleted} = false\`)`

2. **Missing imports:**
   - ❌ Forgetting to import `sql` from drizzle-orm
   - ❌ Forgetting to import `auditFields`

3. **Inconsistent naming:**
   - ❌ Mixed naming conventions for constraints
   - ✅ Use consistent pattern: `table_business_field_unique`

4. **Missing performance indexes:**
   - ❌ Only adding unique constraints without performance indexes
   - ✅ Add indexes for commonly queried fields

## 📋 Schemas That Need Refactoring

Run this command to find schemas that might need refactoring:

```bash
# Find schemas with individual audit fields
grep -r "createdBy.*references.*users" src/app/drizzle/schema/

# Find schemas using old deletedAt pattern
grep -r "deletedAt" src/app/drizzle/schema/

# Find schemas using isNull instead of sql pattern
grep -r "isNull" src/app/drizzle/schema/
```

## ✅ Benefits of This Pattern

1. **Consistency**: All schemas use the same audit field structure
2. **Maintainability**: Changes to audit fields only need to be made in one place
3. **Performance**: Proper indexing for common query patterns
4. **Data Integrity**: Unique constraints that respect soft deletes
5. **Scalability**: Pattern can be easily applied to new schemas

## 🔄 Next Steps

1. Identify schemas that need refactoring using the grep commands above
2. Apply the refactoring pattern to each schema
3. Test the changes to ensure no breaking changes
4. Update any service layer code that might reference the old field names
5. Run database migrations if needed

---

**Note**: Always test schema changes thoroughly in a development environment before applying to production. Consider the impact on existing queries and services that might reference the old audit field structure. 