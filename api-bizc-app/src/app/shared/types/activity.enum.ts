/**
 * Activity-related enums used across the application
 */

/**
 * Activity Type
 * Used to categorize the type of activity being performed
 */
export enum ActivityType {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  RESTORE = 'RESTORE',
  STATUS_CHANGE = 'STATUS_CHANGE',
  BULK_UPDATE = 'BULK_UPDATE',
  BULK_DELETE = 'BULK_DELETE',
}

/**
 * Entity Type
 * Used to identify the type of entity being acted upon
 */
export enum EntityType {
  DESIGNATION = 'DESIGNATION',
  USER = 'USER',
  BUSINESS = 'BUSINESS',
  ASSET = 'ASSET',
  CATEGORY = 'CATEGORY',
  PRODUCT = 'PRODUCT',
  SERVICE = 'SERVICE',
  CUSTOMER = 'CUSTOMER',
  ORDER = 'ORDER',
  INVOICE = 'INVOICE',
  PAYMENT = 'PAYMENT',
  STAFF = 'STAFF',
  DEPARTMENT = 'DEPARTMENT',
  LOCATION = 'LOCATION',
  SUPPLIER = 'SUPPLIER',
  VEHICLE = 'VEHICLE',
  RESERVATION = 'RESERVATION',
  CAMPAIGN = 'CAMPAIGN',
  TEMPLATE = 'TEMPLATE',
  WORK_ORDER = 'WORK_ORDER',
  PROJECT = 'PROJECT',
  TASK = 'TASK',
  EXPENSE = 'EXPENSE',
  LEAD = 'LEAD',
  ESTIMATE = 'ESTIMATE',
  MEETING = 'MEETING',
  // Add more entity types as your system grows
}

/**
 * Activity Log Name
 * Used to identify the type of activity in the log
 */
export enum ActivityLogName {
  LOGIN = 'login',
  LOGOUT = 'logout',
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  VIEW = 'view',
  OTHER = 'other',

  // Meal Period activities
  MEAL_PERIOD_CREATED = 'meal_period_created',
  MEAL_PERIOD_UPDATED = 'meal_period_updated',
  MEAL_PERIOD_DELETED = 'meal_period_deleted',

  // Customer Group activities
  CUSTOMER_GROUP_CREATED = 'customer_group_created',
  CUSTOMER_GROUP_UPDATED = 'customer_group_updated',
  CUSTOMER_GROUP_DELETED = 'customer_group_deleted',

  // Business Settings activities
  BUSINESS_ACCOUNTING_SETTINGS_UPDATED = 'business_accounting_settings_updated',

  // Performance Review activities
  PERFORMANCE_REVIEW_CREATED = 'performance_review_created',
  PERFORMANCE_REVIEW_UPDATED = 'performance_review_updated',
  PERFORMANCE_REVIEW_DELETED = 'performance_review_deleted',
  PERFORMANCE_REVIEW_BULK_DELETED = 'performance_review_bulk_deleted',

  // Time Slot activities
  CREATE_TIME_SLOT = 'create_time_slot',
  UPDATE_TIME_SLOT = 'update_time_slot',
  DELETE_TIME_SLOT = 'delete_time_slot',

  // Table Reservation activities
  TABLE_RESERVATION_CREATED = 'table_reservation_created',
  TABLE_RESERVATION_UPDATED = 'table_reservation_updated',
  TABLE_RESERVATION_DELETED = 'table_reservation_deleted',
  TABLE_RESERVATION_STATUS_UPDATED = 'table_reservation_status_updated',
  TABLE_RESERVATION_CANCELLED = 'table_reservation_cancelled',
  TABLE_RESERVATION_CONFIRMED = 'table_reservation_confirmed',
  TABLE_RESERVATION_COMPLETED = 'table_reservation_completed',
  TABLE_RESERVATION_NO_SHOW = 'table_reservation_no_show',

  // Package activities
  PACKAGE_CREATED = 'package_created',
  PACKAGE_UPDATED = 'package_updated',
  PACKAGE_DELETED = 'package_deleted',
  PACKAGE_BULK_CREATED = 'package_bulk_created',
  PACKAGE_BULK_DELETED = 'package_bulk_deleted',
  PACKAGE_STATUS_UPDATED = 'package_status_updated',
  PACKAGE_POSITIONS_UPDATED = 'package_positions_updated',

  // Work Order activities
  WORK_ORDER_CREATED = 'work_order_created',
  WORK_ORDER_UPDATED = 'work_order_updated',
  WORK_ORDER_DELETED = 'work_order_deleted',
  WORK_ORDER_BULK_CREATED = 'work_order_bulk_created',
  WORK_ORDER_BULK_DELETED = 'work_order_bulk_deleted',

  // Event Space Reservation activities
  EVENT_SPACE_RESERVATION_CREATED = 'event_space_reservation_created',
  EVENT_SPACE_RESERVATION_UPDATED = 'event_space_reservation_updated',
  EVENT_SPACE_RESERVATION_DELETED = 'event_space_reservation_deleted',
  EVENT_SPACE_RESERVATION_BULK_CREATED = 'event_space_reservation_bulk_created',
  EVENT_SPACE_RESERVATION_BULK_DELETED = 'event_space_reservation_bulk_deleted',
  EVENT_SPACE_RESERVATION_BULK_STATUS_UPDATED = 'event_space_reservation_bulk_status_updated',

  // Vehicle Reservation activities
  VEHICLE_RESERVATION_CREATED = 'vehicle_reservation_created',
  VEHICLE_RESERVATION_UPDATED = 'vehicle_reservation_updated',
  VEHICLE_RESERVATION_DELETED = 'vehicle_reservation_deleted',
  VEHICLE_RESERVATION_BULK_CREATED = 'vehicle_reservation_bulk_created',
  VEHICLE_RESERVATION_BULK_DELETED = 'vehicle_reservation_bulk_deleted',
  VEHICLE_RESERVATION_BULK_STATUS_UPDATED = 'vehicle_reservation_bulk_status_updated',
}
