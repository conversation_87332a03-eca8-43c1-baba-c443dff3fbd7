import {
  Injectable,
  UnauthorizedException,
  ConflictException,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { Inject } from '@nestjs/common';
import {
  eq,
  and,
  isNull,
  ilike,
  gte,
  lte,
  inArray,
  asc,
  desc,
  or,
} from 'drizzle-orm';
import { DRIZZLE_ORM } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import {
  payrollRuns,
  PayrollRunStatus,
} from '../drizzle/schema/payroll-run.schema';
import { users } from '../drizzle/schema/users.schema';
import { CreatePayrollRunDto } from './dto/create-payroll-run.dto';
import { UpdatePayrollRunDto } from './dto/update-payroll-run.dto';
import { PayrollRunDto } from './dto/payroll-run.dto';
import { PayrollRunListDto } from './dto/payroll-run-list.dto';
import { PayrollRunSlimDto } from './dto/payroll-run-slim.dto';
import { ActivityLogService } from '../activity-log/activity-log.service';

@Injectable()
export class PayrollRunsService {
  constructor(
    @Inject(DRIZZLE_ORM) private readonly db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createPayrollRunDto: CreatePayrollRunDto,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a payroll run with the same run code already exists for this business
      const existingPayrollRun = await this.db
        .select()
        .from(payrollRuns)
        .where(
          and(
            eq(payrollRuns.businessId, businessId),
            ilike(payrollRuns.runCode, createPayrollRunDto.runCode),
            isNull(payrollRuns.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (existingPayrollRun) {
        throw new ConflictException(
          `Payroll run with code "${createPayrollRunDto.runCode}" already exists`,
        );
      }

      // Create the payroll run
      const [newPayrollRun] = await this.db
        .insert(payrollRuns)
        .values({
          businessId,
          createdBy: userId,
          updatedBy: userId,
          runCode: createPayrollRunDto.runCode,
          runType: createPayrollRunDto.runType,
          payrollMonth: createPayrollRunDto.payrollMonth,
          payrollYear: createPayrollRunDto.payrollYear,
          payPeriodStart: createPayrollRunDto.payPeriodStart,
          payPeriodEnd: createPayrollRunDto.payPeriodEnd,
          paymentDate: createPayrollRunDto.paymentDate,
          totalEmployees: createPayrollRunDto.totalEmployees || 0,
          totalGrossPay: createPayrollRunDto.totalGrossPay || '0.00',
          totalDeductions: createPayrollRunDto.totalDeductions || '0.00',
          totalNetPay: createPayrollRunDto.totalNetPay || '0.00',
          runStatus: createPayrollRunDto.runStatus || PayrollRunStatus.DRAFT,
          notes: createPayrollRunDto.notes,
        })
        .returning({ id: payrollRuns.id });

      // Log the activity
      await this.activityLogService.log({
        userId,
        businessId,
        action: 'CREATE',
        resource: 'PAYROLL_RUN',
        resourceId: newPayrollRun.id,
        details: `Created payroll run: ${createPayrollRunDto.runCode}`,
      });

      return { id: newPayrollRun.id };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create payroll run: ${error.message}`,
      );
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createPayrollRunDto: CreatePayrollRunDto,
  ): Promise<{ id: string }> {
    return this.create(userId, businessId, createPayrollRunDto);
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
  ): Promise<{
    data: PayrollRunDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      isNull(payrollRuns.deletedAt),
      eq(payrollRuns.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(payrollRuns.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(lte(payrollRuns.createdAt, toDate));
      }
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: payrollRuns.id })
      .from(payrollRuns)
      .where(and(...whereConditions));

    const total = totalResult.length;
    const totalPages = Math.ceil(total / limit);

    // Get paginated results with user information
    const results = await this.db
      .select({
        id: payrollRuns.id,
        businessId: payrollRuns.businessId,
        runCode: payrollRuns.runCode,
        runType: payrollRuns.runType,
        payrollMonth: payrollRuns.payrollMonth,
        payrollYear: payrollRuns.payrollYear,
        payPeriodStart: payrollRuns.payPeriodStart,
        payPeriodEnd: payrollRuns.payPeriodEnd,
        paymentDate: payrollRuns.paymentDate,
        totalEmployees: payrollRuns.totalEmployees,
        totalGrossPay: payrollRuns.totalGrossPay,
        totalDeductions: payrollRuns.totalDeductions,
        totalNetPay: payrollRuns.totalNetPay,
        runStatus: payrollRuns.runStatus,
        notes: payrollRuns.notes,
        createdAt: payrollRuns.createdAt,
        updatedAt: payrollRuns.updatedAt,
        createdByName: users.name,
        updatedByName: users.name,
      })
      .from(payrollRuns)
      .leftJoin(users, eq(payrollRuns.createdBy, users.id))
      .where(and(...whereConditions))
      .orderBy(desc(payrollRuns.createdAt))
      .limit(limit)
      .offset(offset);

    const data: PayrollRunDto[] = results.map((result) => ({
      id: result.id,
      businessId: result.businessId,
      runCode: result.runCode,
      runType: result.runType,
      payrollMonth: result.payrollMonth,
      payrollYear: result.payrollYear,
      payPeriodStart: result.payPeriodStart,
      payPeriodEnd: result.payPeriodEnd,
      paymentDate: result.paymentDate,
      totalEmployees: result.totalEmployees,
      totalGrossPay: result.totalGrossPay,
      totalDeductions: result.totalDeductions,
      totalNetPay: result.totalNetPay,
      runStatus: result.runStatus,
      notes: result.notes,
      createdBy: result.createdByName || 'Unknown',
      updatedBy: result.updatedByName || 'Unknown',
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
    }));

    return {
      data,
      meta: { total, page, totalPages },
    };
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<PayrollRunSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const results = await this.db
      .select({
        id: payrollRuns.id,
        runCode: payrollRuns.runCode,
        runType: payrollRuns.runType,
        payrollMonth: payrollRuns.payrollMonth,
        payrollYear: payrollRuns.payrollYear,
        runStatus: payrollRuns.runStatus,
      })
      .from(payrollRuns)
      .where(
        and(
          isNull(payrollRuns.deletedAt),
          eq(payrollRuns.businessId, businessId),
        ),
      )
      .orderBy(desc(payrollRuns.createdAt));

    return results.map((result) => ({
      id: result.id,
      runCode: result.runCode,
      runType: result.runType,
      payrollMonth: result.payrollMonth,
      payrollYear: result.payrollYear,
      runStatus: result.runStatus,
    }));
  }

  async findOne(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<PayrollRunDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const result = await this.db
      .select({
        id: payrollRuns.id,
        businessId: payrollRuns.businessId,
        runCode: payrollRuns.runCode,
        runType: payrollRuns.runType,
        payrollMonth: payrollRuns.payrollMonth,
        payrollYear: payrollRuns.payrollYear,
        payPeriodStart: payrollRuns.payPeriodStart,
        payPeriodEnd: payrollRuns.payPeriodEnd,
        paymentDate: payrollRuns.paymentDate,
        totalEmployees: payrollRuns.totalEmployees,
        totalGrossPay: payrollRuns.totalGrossPay,
        totalDeductions: payrollRuns.totalDeductions,
        totalNetPay: payrollRuns.totalNetPay,
        runStatus: payrollRuns.runStatus,
        notes: payrollRuns.notes,
        createdAt: payrollRuns.createdAt,
        updatedAt: payrollRuns.updatedAt,
        createdByName: users.name,
        updatedByName: users.name,
      })
      .from(payrollRuns)
      .leftJoin(users, eq(payrollRuns.createdBy, users.id))
      .where(
        and(
          eq(payrollRuns.id, id),
          eq(payrollRuns.businessId, businessId),
          isNull(payrollRuns.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!result) {
      throw new NotFoundException('Payroll run not found');
    }

    return {
      id: result.id,
      businessId: result.businessId,
      runCode: result.runCode,
      runType: result.runType,
      payrollMonth: result.payrollMonth,
      payrollYear: result.payrollYear,
      payPeriodStart: result.payPeriodStart,
      payPeriodEnd: result.payPeriodEnd,
      paymentDate: result.paymentDate,
      totalEmployees: result.totalEmployees,
      totalGrossPay: result.totalGrossPay,
      totalDeductions: result.totalDeductions,
      totalNetPay: result.totalNetPay,
      runStatus: result.runStatus,
      notes: result.notes,
      createdBy: result.createdByName || 'Unknown',
      updatedBy: result.updatedByName || 'Unknown',
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
    };
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updatePayrollRunDto: UpdatePayrollRunDto,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if payroll run exists
      const existingPayrollRun = await this.db
        .select()
        .from(payrollRuns)
        .where(
          and(
            eq(payrollRuns.id, id),
            eq(payrollRuns.businessId, businessId),
            isNull(payrollRuns.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!existingPayrollRun) {
        throw new NotFoundException('Payroll run not found');
      }

      // Check for run code conflicts if updating run code
      if (
        updatePayrollRunDto.runCode &&
        updatePayrollRunDto.runCode !== existingPayrollRun.runCode
      ) {
        const conflictingPayrollRun = await this.db
          .select()
          .from(payrollRuns)
          .where(
            and(
              eq(payrollRuns.businessId, businessId),
              ilike(payrollRuns.runCode, updatePayrollRunDto.runCode),
              isNull(payrollRuns.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (conflictingPayrollRun) {
          throw new ConflictException(
            `Payroll run with code "${updatePayrollRunDto.runCode}" already exists`,
          );
        }
      }

      // Update the payroll run
      await this.db
        .update(payrollRuns)
        .set({
          ...updatePayrollRunDto,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(payrollRuns.id, id));

      // Log the activity
      await this.activityLogService.log({
        userId,
        businessId,
        action: 'UPDATE',
        resource: 'PAYROLL_RUN',
        resourceId: id,
        details: `Updated payroll run: ${updatePayrollRunDto.runCode || existingPayrollRun.runCode}`,
      });

      return { id };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update payroll run: ${error.message}`,
      );
    }
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updatePayrollRunDto: UpdatePayrollRunDto,
  ): Promise<{ id: string }> {
    return this.update(userId, businessId, id, updatePayrollRunDto);
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<{ message: string; deletedId: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if payroll run exists
      const existingPayrollRun = await this.db
        .select()
        .from(payrollRuns)
        .where(
          and(
            eq(payrollRuns.id, id),
            eq(payrollRuns.businessId, businessId),
            isNull(payrollRuns.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!existingPayrollRun) {
        throw new NotFoundException('Payroll run not found');
      }

      // Soft delete the payroll run
      await this.db
        .update(payrollRuns)
        .set({
          deletedAt: new Date(),
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(payrollRuns.id, id));

      // Log the activity
      await this.activityLogService.log({
        userId,
        businessId,
        action: 'DELETE',
        resource: 'PAYROLL_RUN',
        resourceId: id,
        details: `Deleted payroll run: ${existingPayrollRun.runCode}`,
      });

      return {
        message: 'Payroll run deleted successfully',
        deletedId: id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete payroll run: ${error.message}`,
      );
    }
  }

  async checkRunCodeAvailability(
    userId: string,
    businessId: string | null,
    runCode: string,
    excludeId?: string,
  ): Promise<{ available: boolean; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const whereConditions = [
      eq(payrollRuns.businessId, businessId),
      ilike(payrollRuns.runCode, runCode),
      isNull(payrollRuns.deletedAt),
    ];

    if (excludeId) {
      whereConditions.push(eq(payrollRuns.id, excludeId));
    }

    const existingPayrollRun = await this.db
      .select()
      .from(payrollRuns)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    const available = !existingPayrollRun;

    return {
      available,
      message: available
        ? 'Run code is available'
        : 'Run code is already in use',
    };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    payrollRunIds: string[],
  ): Promise<{
    deleted: number;
    message: string;
    deletedIds: string[];
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!payrollRunIds || payrollRunIds.length === 0) {
        throw new BadRequestException(
          'No payroll run IDs provided for deletion',
        );
      }

      // Get all payroll runs that exist and belong to the business
      const existingPayrollRuns = await this.db
        .select({
          id: payrollRuns.id,
          runCode: payrollRuns.runCode,
          businessId: payrollRuns.businessId,
        })
        .from(payrollRuns)
        .where(
          and(
            inArray(payrollRuns.id, payrollRunIds),
            eq(payrollRuns.businessId, businessId),
            isNull(payrollRuns.deletedAt),
          ),
        );

      if (existingPayrollRuns.length === 0) {
        throw new NotFoundException('No valid payroll runs found for deletion');
      }

      const validIds = existingPayrollRuns.map((pr) => pr.id);

      // Soft delete all valid payroll runs
      await this.db
        .update(payrollRuns)
        .set({
          deletedAt: new Date(),
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(inArray(payrollRuns.id, validIds));

      // Log activities for each deleted payroll run
      for (const payrollRun of existingPayrollRuns) {
        await this.activityLogService.log({
          userId,
          businessId,
          action: 'DELETE',
          resource: 'PAYROLL_RUN',
          resourceId: payrollRun.id,
          details: `Bulk deleted payroll run: ${payrollRun.runCode}`,
        });
      }

      return {
        deleted: validIds.length,
        message: `Successfully deleted ${validIds.length} payroll runs`,
        deletedIds: validIds,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete payroll runs: ${error.message}`,
      );
    }
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createPayrollRunsDto: CreatePayrollRunDto[],
  ): Promise<PayrollRunDto[]> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!createPayrollRunsDto || createPayrollRunsDto.length === 0) {
        throw new BadRequestException('No payroll runs provided for creation');
      }

      // Check for duplicate run codes within the batch
      const runCodes = createPayrollRunsDto.map((dto) =>
        dto.runCode.toLowerCase(),
      );
      const duplicateRunCodes = runCodes.filter(
        (code, index) => runCodes.indexOf(code) !== index,
      );

      if (duplicateRunCodes.length > 0) {
        throw new ConflictException(
          `Duplicate run codes found in batch: ${duplicateRunCodes.join(', ')}`,
        );
      }

      // Check for existing run codes in database
      const existingPayrollRuns = await this.db
        .select({ runCode: payrollRuns.runCode })
        .from(payrollRuns)
        .where(
          and(
            eq(payrollRuns.businessId, businessId),
            inArray(
              payrollRuns.runCode,
              createPayrollRunsDto.map((dto) => dto.runCode),
            ),
            isNull(payrollRuns.deletedAt),
          ),
        );

      if (existingPayrollRuns.length > 0) {
        const existingCodes = existingPayrollRuns.map((pr) => pr.runCode);
        throw new ConflictException(
          `Payroll runs with these codes already exist: ${existingCodes.join(', ')}`,
        );
      }

      // Create all payroll runs
      const payrollRunsToCreate = createPayrollRunsDto.map((dto) => ({
        businessId,
        createdBy: userId,
        updatedBy: userId,
        runCode: dto.runCode,
        runType: dto.runType,
        payrollMonth: dto.payrollMonth,
        payrollYear: dto.payrollYear,
        payPeriodStart: dto.payPeriodStart,
        payPeriodEnd: dto.payPeriodEnd,
        paymentDate: dto.paymentDate,
        totalEmployees: dto.totalEmployees || 0,
        totalGrossPay: dto.totalGrossPay || '0.00',
        totalDeductions: dto.totalDeductions || '0.00',
        totalNetPay: dto.totalNetPay || '0.00',
        runStatus: dto.runStatus || PayrollRunStatus.DRAFT,
        notes: dto.notes,
      }));

      const createdPayrollRuns = await this.db
        .insert(payrollRuns)
        .values(payrollRunsToCreate)
        .returning({
          id: payrollRuns.id,
          businessId: payrollRuns.businessId,
          runCode: payrollRuns.runCode,
          runType: payrollRuns.runType,
          payrollMonth: payrollRuns.payrollMonth,
          payrollYear: payrollRuns.payrollYear,
          payPeriodStart: payrollRuns.payPeriodStart,
          payPeriodEnd: payrollRuns.payPeriodEnd,
          paymentDate: payrollRuns.paymentDate,
          totalEmployees: payrollRuns.totalEmployees,
          totalGrossPay: payrollRuns.totalGrossPay,
          totalDeductions: payrollRuns.totalDeductions,
          totalNetPay: payrollRuns.totalNetPay,
          runStatus: payrollRuns.runStatus,
          notes: payrollRuns.notes,
          createdAt: payrollRuns.createdAt,
          updatedAt: payrollRuns.updatedAt,
        });

      // Log activities for each created payroll run
      for (const payrollRun of createdPayrollRuns) {
        await this.activityLogService.log({
          userId,
          businessId,
          action: 'CREATE',
          resource: 'PAYROLL_RUN',
          resourceId: payrollRun.id,
          details: `Bulk created payroll run: ${payrollRun.runCode}`,
        });
      }

      // Transform to DTOs
      const payrollRunDtos: PayrollRunDto[] = createdPayrollRuns.map((pr) => ({
        id: pr.id,
        businessId: pr.businessId,
        runCode: pr.runCode,
        runType: pr.runType,
        payrollMonth: pr.payrollMonth,
        payrollYear: pr.payrollYear,
        payPeriodStart: pr.payPeriodStart,
        payPeriodEnd: pr.payPeriodEnd,
        paymentDate: pr.paymentDate,
        totalEmployees: pr.totalEmployees,
        totalGrossPay: pr.totalGrossPay,
        totalDeductions: pr.totalDeductions,
        totalNetPay: pr.totalNetPay,
        runStatus: pr.runStatus,
        notes: pr.notes,
        createdBy: 'System', // We don't have user info in bulk create return
        updatedBy: 'System',
        createdAt: pr.createdAt,
        updatedAt: pr.updatedAt,
      }));

      return payrollRunDtos;
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk create payroll runs: ${error.message}`,
      );
    }
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createPayrollRunsDto: CreatePayrollRunDto[],
  ): Promise<{ ids: string[] }> {
    const payrollRuns = await this.bulkCreate(
      userId,
      businessId,
      createPayrollRunsDto,
    );
    return { ids: payrollRuns.map((payrollRun) => payrollRun.id) };
  }

  async bulkUpdatePayrollRunStatus(
    userId: string,
    businessId: string | null,
    payrollRunIds: string[],
    status: PayrollRunStatus,
  ): Promise<{
    updated: number;
    updatedIds: string[];
    failed: Array<{ payrollRunId: string; error: string }>;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!payrollRunIds || payrollRunIds.length === 0) {
        throw new BadRequestException(
          'No payroll run IDs provided for status update',
        );
      }

      // Get all payroll runs that exist and belong to the business
      const existingPayrollRuns = await this.db
        .select({
          id: payrollRuns.id,
          runCode: payrollRuns.runCode,
          businessId: payrollRuns.businessId,
        })
        .from(payrollRuns)
        .where(
          and(
            inArray(payrollRuns.id, payrollRunIds),
            eq(payrollRuns.businessId, businessId),
            isNull(payrollRuns.deletedAt),
          ),
        );

      const validIds = existingPayrollRuns.map((pr) => pr.id);
      const failed: Array<{ payrollRunId: string; error: string }> = [];

      // Find IDs that don't exist
      const invalidIds = payrollRunIds.filter((id) => !validIds.includes(id));
      invalidIds.forEach((id) => {
        failed.push({
          payrollRunId: id,
          error: 'Payroll run not found or does not belong to this business',
        });
      });

      let updatedCount = 0;
      const updatedIds: string[] = [];

      if (validIds.length > 0) {
        // Update status for all valid payroll runs
        await this.db
          .update(payrollRuns)
          .set({
            runStatus: status,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(inArray(payrollRuns.id, validIds));

        updatedCount = validIds.length;
        updatedIds.push(...validIds);

        // Log activities for each updated payroll run
        for (const payrollRun of existingPayrollRuns) {
          await this.activityLogService.log({
            userId,
            businessId,
            action: 'UPDATE',
            resource: 'PAYROLL_RUN',
            resourceId: payrollRun.id,
            details: `Bulk updated payroll run status to ${status}: ${payrollRun.runCode}`,
          });
        }
      }

      return { updated: updatedCount, updatedIds, failed };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update payroll run status: ${error.message}`,
      );
    }
  }
}
