import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsUUID, ArrayMinSize } from 'class-validator';

export class BulkDeletePayrollRunDto {
  @ApiProperty({
    description: 'Array of payroll run IDs to delete',
    example: [
      'b47b8c3a-5d67-4e8f-9a1b-2c3d4e5f6g7h',
      'c58c9d4b-6e78-5f9g-ab2c-3d4e5f6g7h8i',
    ],
    type: [String],
    minItems: 1,
  })
  @IsArray()
  @ArrayMinSize(1, { message: 'At least one payroll run ID must be provided' })
  @IsUUID('4', { each: true, message: 'Each ID must be a valid UUID' })
  payrollRunIds: string[];
}
