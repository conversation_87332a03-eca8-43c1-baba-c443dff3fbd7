import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
  IsEnum,
  IsNumber,
  Min,
  Max,
  IsDateString,
  IsDecimal,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { PayrollRunType, PayrollRunStatus } from '../../drizzle/schema/payroll-run.schema';

export class CreatePayrollRunDto {
  @ApiProperty({ description: 'Unique run identifier', maxLength: 191 })
  @IsNotEmpty()
  @IsString()
  @MaxLength(191)
  runCode: string;

  @ApiPropertyOptional({
    enum: PayrollRunType,
    enumName: 'PayrollRunType',
    description: 'Type of payroll run',
    default: PayrollRunType.REGULAR,
  })
  @IsOptional()
  @IsEnum(PayrollRunType, {
    message: 'Run type must be a valid PayrollRunType',
  })
  runType?: PayrollRunType;

  @ApiProperty({ description: 'Payroll month (e.g., "January", "February")', maxLength: 191 })
  @IsNotEmpty()
  @IsString()
  @MaxLength(191)
  payrollMonth: string;

  @ApiProperty({ description: 'Payroll year', example: 2024 })
  @IsNotEmpty()
  @IsNumber()
  @Min(2000)
  @Max(2100)
  payrollYear: number;

  @ApiProperty({ description: 'Pay period start date', example: '2024-01-01' })
  @IsNotEmpty()
  @IsDateString()
  payPeriodStart: string;

  @ApiProperty({ description: 'Pay period end date', example: '2024-01-31' })
  @IsNotEmpty()
  @IsDateString()
  payPeriodEnd: string;

  @ApiProperty({ description: 'Payment date', example: '2024-02-05' })
  @IsNotEmpty()
  @IsDateString()
  paymentDate: string;

  @ApiPropertyOptional({
    description: 'Total number of employees',
    default: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  totalEmployees?: number;

  @ApiPropertyOptional({
    description: 'Total gross pay amount',
    example: '50000.00',
    default: '0.00',
  })
  @IsOptional()
  @IsDecimal({ decimal_digits: '2' })
  totalGrossPay?: string;

  @ApiPropertyOptional({
    description: 'Total deductions amount',
    example: '5000.00',
    default: '0.00',
  })
  @IsOptional()
  @IsDecimal({ decimal_digits: '2' })
  totalDeductions?: string;

  @ApiPropertyOptional({
    description: 'Total net pay amount',
    example: '45000.00',
    default: '0.00',
  })
  @IsOptional()
  @IsDecimal({ decimal_digits: '2' })
  totalNetPay?: string;

  @ApiPropertyOptional({
    enum: PayrollRunStatus,
    enumName: 'PayrollRunStatus',
    description: 'Payroll run status',
    default: PayrollRunStatus.DRAFT,
  })
  @IsOptional()
  @IsEnum(PayrollRunStatus, {
    message: 'Run status must be a valid PayrollRunStatus',
  })
  runStatus?: PayrollRunStatus;

  @ApiPropertyOptional({
    description: 'Additional notes',
  })
  @IsOptional()
  @IsString()
  notes?: string;
}
