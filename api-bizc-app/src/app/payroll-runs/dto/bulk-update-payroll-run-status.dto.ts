import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsUUID,
  ArrayMinSize,
} from 'class-validator';
import { PayrollRunStatus } from '../../drizzle/schema/payroll-run.schema';

export class BulkUpdatePayrollRunStatusItemDto {
  @ApiProperty({
    description: 'Payroll run ID to update',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsUUID('4', { message: 'Payroll run ID must be a valid UUID' })
  @IsNotEmpty({ message: 'Payroll run ID is required' })
  payrollRunId: string;
}

export class BulkUpdatePayrollRunStatusDto {
  @ApiProperty({
    description: 'Array of payroll run IDs to update',
    type: [String],
    example: [
      '550e8400-e29b-41d4-a716-************',
      '550e8400-e29b-41d4-a716-************',
    ],
    minItems: 1,
  })
  @IsArray()
  @ArrayMinSize(1, { message: 'At least one payroll run ID must be provided' })
  @IsUUID('4', { each: true, message: 'Each ID must be a valid UUID' })
  payrollRunIds: string[];

  @ApiProperty({
    description: 'Status to apply to all payroll runs',
    enum: PayrollRunStatus,
    enumName: 'PayrollRunStatus',
    example: PayrollRunStatus.APPROVED,
  })
  @IsEnum(PayrollRunStatus, { message: 'Status must be a valid PayrollRunStatus' })
  @IsNotEmpty({ message: 'Status is required' })
  status: PayrollRunStatus;
}
