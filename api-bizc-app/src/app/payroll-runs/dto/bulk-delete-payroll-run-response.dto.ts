import { ApiProperty } from '@nestjs/swagger';

export class BulkDeletePayrollRunResponseDto {
  @ApiProperty({
    example: 5,
    description: 'Number of payroll runs successfully deleted',
  })
  deleted: number;

  @ApiProperty({
    example: 'Successfully deleted 5 payroll runs',
    description: 'Success message',
  })
  message: string;

  @ApiProperty({
    type: [String],
    example: [
      '550e8400-e29b-41d4-a716-446655440000',
      '550e8400-e29b-41d4-a716-446655440001',
    ],
    description: 'Array of deleted payroll run IDs',
  })
  deletedIds: string[];
}
