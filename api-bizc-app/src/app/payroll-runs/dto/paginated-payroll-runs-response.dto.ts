import { ApiProperty } from '@nestjs/swagger';
import { PayrollRunListDto } from './payroll-run-list.dto';

export class PaginatedPayrollRunsResponseDto {
  @ApiProperty({
    type: [PayrollRunListDto],
    description: 'Array of payroll runs',
  })
  data: PayrollRunListDto[];

  @ApiProperty({
    description: 'Pagination metadata',
    example: {
      total: 100,
      page: 1,
      totalPages: 10,
    },
  })
  meta: {
    total: number;
    page: number;
    totalPages: number;
  };
}
