import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PayrollRunType, PayrollRunStatus } from '../../drizzle/schema/payroll-run.schema';

export class PayrollRunListDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Payroll run ID',
  })
  id: string;

  @ApiProperty({ example: 'PR-2024-01', description: 'Run code' })
  runCode: string;

  @ApiProperty({
    example: PayrollRunType.REGULAR,
    enum: PayrollRunType,
    enumName: 'PayrollRunType',
    description: 'Type of payroll run',
  })
  runType: PayrollRunType;

  @ApiProperty({ example: 'January', description: 'Payroll month' })
  payrollMonth: string;

  @ApiProperty({ example: 2024, description: 'Payroll year' })
  payrollYear: number;

  @ApiProperty({ example: '2024-01-01', description: 'Pay period start date' })
  payPeriodStart: string;

  @ApiProperty({ example: '2024-01-31', description: 'Pay period end date' })
  payPeriodEnd: string;

  @ApiProperty({ example: '2024-02-05', description: 'Payment date' })
  paymentDate: string;

  @ApiProperty({ example: 50, description: 'Total number of employees' })
  totalEmployees: number;

  @ApiProperty({ example: '50000.00', description: 'Total gross pay amount' })
  totalGrossPay: string;

  @ApiProperty({ example: '5000.00', description: 'Total deductions amount' })
  totalDeductions: string;

  @ApiProperty({ example: '45000.00', description: 'Total net pay amount' })
  totalNetPay: string;

  @ApiProperty({
    example: PayrollRunStatus.DRAFT,
    enum: PayrollRunStatus,
    enumName: 'PayrollRunStatus',
    description: 'Payroll run status',
  })
  runStatus: PayrollRunStatus;

  @ApiPropertyOptional({
    example: 'Monthly payroll for January 2024',
    description: 'Additional notes',
    nullable: true,
  })
  notes?: string;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Creation timestamp',
  })
  createdAt: Date;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Last update timestamp',
    required: false,
    nullable: true,
  })
  updatedAt?: Date;
}
