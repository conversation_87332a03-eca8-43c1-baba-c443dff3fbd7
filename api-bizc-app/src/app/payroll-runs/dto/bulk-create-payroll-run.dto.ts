import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsArray } from 'class-validator';
import { Transform } from 'class-transformer';
import { CreatePayrollRunDto } from './create-payroll-run.dto';

export class BulkCreatePayrollRunDto {
  @ApiProperty({
    description: 'JSON string containing array of payroll run objects',
    example:
      '[{"runCode":"PR-2024-01","payrollMonth":"January","payrollYear":2024,"payPeriodStart":"2024-01-01","payPeriodEnd":"2024-01-31","paymentDate":"2024-02-05"},{"runCode":"PR-2024-02","payrollMonth":"February","payrollYear":2024,"payPeriodStart":"2024-02-01","payPeriodEnd":"2024-02-29","paymentDate":"2024-03-05"}]',
  })
  @IsNotEmpty({ message: 'Payroll runs are required' })
  @Transform(({ value }) => {
    try {
      return typeof value === 'string' ? JSON.parse(value) : value;
    } catch {
      return value;
    }
  })
  @IsArray({ message: 'Payroll runs must be an array' })
  payrollRuns: CreatePayrollRunDto[];
}
