import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
  ApiBody,
} from '@nestjs/swagger';
import { PayrollRunsService } from './payroll-runs.service';
import { CreatePayrollRunDto } from './dto/create-payroll-run.dto';
import { UpdatePayrollRunDto } from './dto/update-payroll-run.dto';
import { BulkCreatePayrollRunDto } from './dto/bulk-create-payroll-run.dto';
import { BulkDeletePayrollRunDto } from './dto/bulk-delete-payroll-run.dto';
import { BulkUpdatePayrollRunStatusDto } from './dto/bulk-update-payroll-run-status.dto';
import { PayrollRunIdResponseDto } from './dto/payroll-run-id-response.dto';
import { BulkPayrollRunIdsResponseDto } from './dto/bulk-payroll-run-ids-response.dto';
import { BulkDeletePayrollRunResponseDto } from './dto/bulk-delete-payroll-run-response.dto';
import { PayrollRunCodeAvailabilityResponseDto } from './dto/check-payroll-run-code.dto';
import { DeletePayrollRunResponseDto } from './dto/delete-payroll-run-response.dto';
import { PaginatedPayrollRunsResponseDto } from './dto/paginated-payroll-runs-response.dto';
import { PayrollRunDto } from './dto/payroll-run.dto';
import { PayrollRunSlimDto } from './dto/payroll-run-slim.dto';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { Permission } from '../shared/types/permission.enum';

@ApiTags('payroll-runs')
@Controller('payroll-runs')
@UseGuards(PermissionsGuard)
export class PayrollRunsController {
  constructor(private readonly payrollRunsService: PayrollRunsService) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.PAYROLL_RUN_CREATE)
  @ApiOperation({
    summary: 'Create a new payroll run',
    description: 'Creates a new payroll run for the active business',
  })
  @ApiBody({
    description: 'Payroll run creation data',
    type: CreatePayrollRunDto,
  })
  @ApiResponse({
    status: 201,
    description: 'The payroll run has been successfully created',
    type: PayrollRunIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data provided',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Payroll run code already exists',
  })
  create(
    @Request() req,
    @Body() createPayrollRunDto: CreatePayrollRunDto,
  ): Promise<PayrollRunIdResponseDto> {
    return this.payrollRunsService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createPayrollRunDto,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PAYROLL_RUN_CREATE)
  @ApiOperation({
    summary: 'Bulk create payroll runs',
    description: 'Creates multiple payroll runs at once',
  })
  @ApiBody({
    description: 'Array of payroll run objects to create',
    type: BulkCreatePayrollRunDto,
  })
  @ApiResponse({
    status: 201,
    description: 'The payroll runs have been successfully created',
    type: BulkPayrollRunIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or duplicate run codes',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Payroll run codes already exist',
  })
  bulkCreate(
    @Request() req,
    @Body() bulkCreatePayrollRunDto: BulkCreatePayrollRunDto,
  ): Promise<BulkPayrollRunIdsResponseDto> {
    return this.payrollRunsService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreatePayrollRunDto.payrollRuns,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.PAYROLL_RUN_READ)
  @ApiOperation({
    summary: 'Get all payroll runs for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Start date for filtering (ISO 8601)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'to',
    description: 'End date for filtering (ISO 8601)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'runCode',
    description: 'Filter by run code',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'runType',
    description: 'Filter by run type',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'payrollMonth',
    description: 'Filter by payroll month',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'payrollYear',
    description: 'Filter by payroll year',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'runStatus',
    description: 'Filter by run status',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'filters',
    description: 'Additional filters',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for filters (and/or)',
    required: false,
    enum: ['and', 'or'],
  })
  @ApiQuery({
    name: 'sort',
    description: 'Sort field and direction (field:asc/desc)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'List of payroll runs retrieved successfully',
    type: PaginatedPayrollRunsResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('runCode') runCode?: string,
    @Query('runType') runType?: string,
    @Query('payrollMonth') payrollMonth?: string,
    @Query('payrollYear') payrollYear?: string,
    @Query('runStatus') runStatus?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedPayrollRunsResponseDto> {
    const pageNum = page ? parseInt(page, 10) : 1;
    const limitNum = limit ? parseInt(limit, 10) : 10;

    return this.payrollRunsService.findAllOptimized(
      req.user.id,
      req.user.activeBusinessId,
      pageNum,
      limitNum,
      from,
      to,
      runCode,
      runType,
      payrollMonth,
      payrollYear,
      runStatus,
      filters,
      joinOperator,
      sort,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PAYROLL_RUN_READ)
  @ApiOperation({
    summary: 'Get slim list of payroll runs',
    description:
      'Returns a simplified list of payroll runs with essential fields only',
  })
  @ApiResponse({
    status: 200,
    description: 'Slim list of payroll runs retrieved successfully',
    type: [PayrollRunSlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req): Promise<PayrollRunSlimDto[]> {
    return this.payrollRunsService.findAllSlim(
      req.user.id,
      req.user.activeBusinessId,
    );
  }

  @Get('check-run-code/:runCode')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PAYROLL_RUN_READ)
  @ApiOperation({
    summary: 'Check if run code is available',
    description: 'Checks if a run code is available for use',
  })
  @ApiParam({
    name: 'runCode',
    description: 'Run code to check',
    type: String,
  })
  @ApiQuery({
    name: 'excludeId',
    description: 'Payroll run ID to exclude from check (for updates)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Run code availability checked successfully',
    type: PayrollRunCodeAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkRunCodeAvailability(
    @Request() req,
    @Param('runCode') runCode: string,
    @Query('excludeId') excludeId?: string,
  ): Promise<PayrollRunCodeAvailabilityResponseDto> {
    return this.payrollRunsService.checkRunCodeAvailability(
      req.user.id,
      req.user.activeBusinessId,
      runCode,
      excludeId,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PAYROLL_RUN_READ)
  @ApiOperation({
    summary: 'Get a payroll run by ID',
    description: 'Retrieves a specific payroll run by its ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Payroll run ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Payroll run retrieved successfully',
    type: PayrollRunDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Payroll run not found',
  })
  findOne(@Request() req, @Param('id') id: string): Promise<PayrollRunDto> {
    return this.payrollRunsService.findOne(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PAYROLL_RUN_UPDATE)
  @ApiOperation({
    summary: 'Update a payroll run',
    description: 'Updates an existing payroll run',
  })
  @ApiParam({
    name: 'id',
    description: 'Payroll run ID',
    type: String,
  })
  @ApiBody({
    description: 'Payroll run update data',
    type: UpdatePayrollRunDto,
  })
  @ApiResponse({
    status: 200,
    description: 'The payroll run has been successfully updated',
    type: PayrollRunIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data provided',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Payroll run not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Run code already exists',
  })
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() updatePayrollRunDto: UpdatePayrollRunDto,
  ): Promise<PayrollRunIdResponseDto> {
    return this.payrollRunsService.updateAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updatePayrollRunDto,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PAYROLL_RUN_DELETE)
  @ApiOperation({ summary: 'Bulk delete payroll runs' })
  @ApiBody({
    description: 'Array of payroll run IDs to delete',
    type: BulkDeletePayrollRunDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Payroll runs deleted successfully',
    type: BulkDeletePayrollRunResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - No IDs provided',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'No valid payroll runs found for deletion',
  })
  bulkDelete(
    @Request() req,
    @Body() bulkDeletePayrollRunDto: BulkDeletePayrollRunDto,
  ): Promise<BulkDeletePayrollRunResponseDto> {
    return this.payrollRunsService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeletePayrollRunDto.payrollRunIds,
    );
  }

  @Patch('bulk/status')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PAYROLL_RUN_UPDATE)
  @ApiOperation({
    summary: 'Bulk update payroll run status',
    description: 'Updates the status of multiple payroll runs at once',
  })
  @ApiBody({
    description: 'Bulk status update data',
    type: BulkUpdatePayrollRunStatusDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Payroll run statuses updated successfully',
    schema: {
      type: 'object',
      properties: {
        updated: {
          type: 'number',
          example: 5,
          description: 'Number of payroll runs successfully updated',
        },
        updatedIds: {
          type: 'array',
          items: { type: 'string' },
          example: [
            '550e8400-e29b-41d4-a716-446655440000',
            '550e8400-e29b-41d4-a716-446655440001',
          ],
          description: 'Array of updated payroll run IDs',
        },
        failed: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              payrollRunId: { type: 'string' },
              error: { type: 'string' },
            },
          },
          example: [],
          description: 'Array of failed updates with error details',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - No IDs provided or invalid status',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  bulkUpdateStatus(
    @Request() req,
    @Body() bulkUpdateStatusDto: BulkUpdatePayrollRunStatusDto,
  ): Promise<{
    updated: number;
    updatedIds: string[];
    failed: Array<{ payrollRunId: string; error: string }>;
  }> {
    return this.payrollRunsService.bulkUpdatePayrollRunStatus(
      req.user.id,
      req.user.activeBusinessId,
      bulkUpdateStatusDto.payrollRunIds,
      bulkUpdateStatusDto.status,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PAYROLL_RUN_DELETE)
  @ApiOperation({
    summary: 'Delete a payroll run',
    description: 'Soft deletes a payroll run by its ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Payroll run ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Payroll run deleted successfully',
    type: DeletePayrollRunResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Payroll run not found',
  })
  remove(
    @Request() req,
    @Param('id') id: string,
  ): Promise<DeletePayrollRunResponseDto> {
    return this.payrollRunsService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }
}
