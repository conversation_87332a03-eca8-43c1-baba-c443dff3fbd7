import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  DeductionCalculationMethod,
  DeductionAppliesTo,
} from '../../shared/types';

export class DeductionTypeListDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Deduction type ID',
  })
  id: string;

  @ApiProperty({
    example: 'Income Tax',
    description: 'Deduction name',
  })
  deductionName: string;

  @ApiProperty({
    example: 'IT001',
    description: 'Deduction code',
  })
  deductionCode: string;

  @ApiProperty({
    example: DeductionCalculationMethod.PERCENTAGE,
    enum: DeductionCalculationMethod,
    enumName: 'DeductionCalculationMethod',
    description: 'Calculation method',
  })
  calculationMethod: DeductionCalculationMethod;

  @ApiPropertyOptional({
    example: 1000.0,
    description: 'Deduction amount',
    nullable: true,
  })
  amount?: number;

  @ApiProperty({
    example: false,
    description: 'Whether the deduction type is system defined',
  })
  isSystemDefined: boolean;

  @ApiProperty({
    example: true,
    description: 'Whether the deduction is active',
  })
  isActive: boolean;

  @ApiProperty({
    example: DeductionAppliesTo.GROSS,
    enum: DeductionAppliesTo,
    enumName: 'DeductionAppliesTo',
    description: 'What the deduction applies to',
  })
  appliesTo: DeductionAppliesTo;

  @ApiPropertyOptional({
    example: 'Monthly income tax deduction',
    description: 'Deduction description',
    nullable: true,
  })
  description?: string;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Creation timestamp',
  })
  createdAt: Date;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Last update timestamp',
  })
  updatedAt: Date;
}
