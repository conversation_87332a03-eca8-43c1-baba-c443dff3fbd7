import { Injectable, Inject, NotFoundException, Logger } from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { activityLogs } from '../drizzle/schema/activity-log.schema';
import { eq, and, desc, asc, gte, lte, count } from 'drizzle-orm';
import { CreateActivityLogDto } from './dto/create-activity-log.dto';
import { UpdateActivityLogDto } from './dto/update-activity-log.dto';
import { GetActivityLogsQueryDto } from './dto/get-activity-logs-query.dto';
import { ActivityLogDto } from './dto/activity-log.dto';
import { ActivityLogListDto } from './dto/activity-log-list.dto';
import { PaginatedActivityLogsResponseDto } from './dto/paginated-activity-logs-response.dto';
import { ActivityType, EntityType } from '../shared/types';

@Injectable()
export class ActivityLogService {
  private readonly logger = new Logger(ActivityLogService.name);

  constructor(@Inject(DRIZZLE) private db: DrizzleDB) {}

  /**
   * Create a new activity log entry
   */
  async create(
    createActivityLogDto: CreateActivityLogDto,
  ): Promise<ActivityLogDto> {
    try {
      const [result] = await this.db
        .insert(activityLogs)
        .values({
          entityType: createActivityLogDto.entityType,
          entityId: createActivityLogDto.entityId,
          businessId: createActivityLogDto.businessId,
          activityType: createActivityLogDto.activityType,
          userId: createActivityLogDto.userId,
          changes: createActivityLogDto.changes,
          metadata: createActivityLogDto.metadata,
          ipAddress: createActivityLogDto.ipAddress,
          userAgent: createActivityLogDto.userAgent,
        })
        .returning();

      return this.mapToActivityLogDto(result);
    } catch (error) {
      this.logger.error('Failed to create activity log', {
        error: error.message,
        data: createActivityLogDto,
      });
      throw error;
    }
  }

  /**
   * Get paginated activity logs with filtering
   */
  async findAll(
    businessId: string,
    query: GetActivityLogsQueryDto,
  ): Promise<PaginatedActivityLogsResponseDto> {
    const { page = 1, limit = 10, sort = 'createdAt:desc' } = query;
    const offset = (page - 1) * limit;

    // Build where conditions
    const conditions = [eq(activityLogs.businessId, businessId)];

    if (query.entityType) {
      conditions.push(eq(activityLogs.entityType, query.entityType));
    }

    if (query.entityId) {
      conditions.push(eq(activityLogs.entityId, query.entityId));
    }

    if (query.activityType) {
      conditions.push(eq(activityLogs.activityType, query.activityType));
    }

    if (query.userId) {
      conditions.push(eq(activityLogs.userId, query.userId));
    }

    if (query.fromDate) {
      conditions.push(gte(activityLogs.createdAt, new Date(query.fromDate)));
    }

    if (query.toDate) {
      conditions.push(lte(activityLogs.createdAt, new Date(query.toDate)));
    }

    // Parse sort parameter
    const [, sortDirection] = sort.split(':');
    const orderBy =
      sortDirection === 'asc'
        ? asc(activityLogs.createdAt)
        : desc(activityLogs.createdAt);

    try {
      // Get total count
      const [{ total }] = await this.db
        .select({ total: count() })
        .from(activityLogs)
        .where(and(...conditions));

      // Get paginated data
      const data = await this.db
        .select({
          id: activityLogs.id,
          entityType: activityLogs.entityType,
          entityId: activityLogs.entityId,
          activityType: activityLogs.activityType,
          userId: activityLogs.userId,
          ipAddress: activityLogs.ipAddress,
          createdAt: activityLogs.createdAt,
        })
        .from(activityLogs)
        .where(and(...conditions))
        .orderBy(orderBy)
        .limit(limit)
        .offset(offset);

      const totalPages = Math.ceil(total / limit);

      return {
        data: data.map((item) => this.mapToActivityLogListDto(item)),
        meta: {
          total,
          page,
          limit,
          totalPages,
        },
      };
    } catch (error) {
      this.logger.error('Failed to fetch activity logs', {
        error: error.message,
        businessId,
        query,
      });
      throw error;
    }
  }

  /**
   * Get a single activity log by ID
   */
  async findOne(id: string, businessId: string): Promise<ActivityLogDto> {
    try {
      const [result] = await this.db
        .select()
        .from(activityLogs)
        .where(
          and(eq(activityLogs.id, id), eq(activityLogs.businessId, businessId)),
        );

      if (!result) {
        throw new NotFoundException('Activity log not found');
      }

      return this.mapToActivityLogDto(result);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error('Failed to fetch activity log', {
        error: error.message,
        id,
        businessId,
      });
      throw error;
    }
  }

  /**
   * Update an activity log entry (limited fields)
   */
  async update(
    id: string,
    businessId: string,
    updateActivityLogDto: UpdateActivityLogDto,
  ): Promise<ActivityLogDto> {
    try {
      const [result] = await this.db
        .update(activityLogs)
        .set({
          metadata: updateActivityLogDto.metadata,
          // Only allow updating metadata for audit logs
        })
        .where(
          and(eq(activityLogs.id, id), eq(activityLogs.businessId, businessId)),
        )
        .returning();

      if (!result) {
        throw new NotFoundException('Activity log not found');
      }

      return this.mapToActivityLogDto(result);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error('Failed to update activity log', {
        error: error.message,
        id,
        businessId,
        data: updateActivityLogDto,
      });
      throw error;
    }
  }

  /**
   * Delete an activity log entry (hard delete - use carefully)
   */
  async remove(id: string, businessId: string): Promise<ActivityLogDto> {
    try {
      const [result] = await this.db
        .delete(activityLogs)
        .where(
          and(eq(activityLogs.id, id), eq(activityLogs.businessId, businessId)),
        )
        .returning();

      if (!result) {
        throw new NotFoundException('Activity log not found');
      }

      return this.mapToActivityLogDto(result);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error('Failed to delete activity log', {
        error: error.message,
        id,
        businessId,
      });
      throw error;
    }
  }

  /**
   * Utility method to log activities easily
   */
  async logActivity(
    entityType: EntityType,
    entityId: string,
    businessId: string,
    activityType: ActivityType,
    userId: string,
    options?: {
      changes?: Record<string, any>;
      metadata?: Record<string, any>;
      ipAddress?: string;
      userAgent?: string;
    },
  ): Promise<ActivityLogDto> {
    return this.create({
      entityType,
      entityId,
      businessId,
      activityType,
      userId,
      changes: options?.changes,
      metadata: options?.metadata,
      ipAddress: options?.ipAddress,
      userAgent: options?.userAgent,
    });
  }

  /**
   * Bulk log multiple activities (for bulk operations)
   */
  async logBulkActivity(
    activities: Array<{
      entityType: EntityType;
      entityId: string;
      businessId: string;
      activityType: ActivityType;
      userId: string;
      changes?: Record<string, any>;
      metadata?: Record<string, any>;
    }>,
    commonOptions?: {
      ipAddress?: string;
      userAgent?: string;
    },
  ): Promise<ActivityLogDto[]> {
    try {
      const values = activities.map((activity) => ({
        entityType: activity.entityType,
        entityId: activity.entityId,
        businessId: activity.businessId,
        activityType: activity.activityType,
        userId: activity.userId,
        changes: activity.changes,
        metadata: activity.metadata,
        ipAddress: commonOptions?.ipAddress,
        userAgent: commonOptions?.userAgent,
      }));

      const results = await this.db
        .insert(activityLogs)
        .values(values)
        .returning();

      return results.map((item) => this.mapToActivityLogDto(item));
    } catch (error) {
      this.logger.error('Failed to log bulk activities', {
        error: error.message,
        count: activities.length,
      });
      throw error;
    }
  }

  /**
   * Get activity counts by entity type
   */
  async getActivityCounts(
    businessId: string,
  ): Promise<Record<EntityType, number>> {
    try {
      const results = await this.db
        .select({
          entityType: activityLogs.entityType,
          count: count(),
        })
        .from(activityLogs)
        .where(eq(activityLogs.businessId, businessId))
        .groupBy(activityLogs.entityType);

      const counts: Record<string, number> = {};

      // Initialize all entity types with 0
      Object.values(EntityType).forEach((entityType) => {
        counts[entityType] = 0;
      });

      // Update with actual counts
      results.forEach((result) => {
        counts[result.entityType] = result.count;
      });

      return counts as Record<EntityType, number>;
    } catch (error) {
      this.logger.error('Failed to get activity counts', {
        error: error.message,
        businessId,
      });
      throw error;
    }
  }

  /**
   * Private method to map database result to ActivityLogDto
   */
  private mapToActivityLogDto(row: any): ActivityLogDto {
    return {
      id: row.id,
      entityType: row.entityType,
      entityId: row.entityId,
      businessId: row.businessId,
      activityType: row.activityType,
      userId: row.userId,
      changes: row.changes,
      metadata: row.metadata,
      ipAddress: row.ipAddress,
      userAgent: row.userAgent,
      createdAt: row.createdAt,
    };
  }

  /**
   * Private method to map database result to ActivityLogListDto
   */
  private mapToActivityLogListDto(row: any): ActivityLogListDto {
    return {
      id: row.id,
      entityType: row.entityType,
      entityId: row.entityId,
      activityType: row.activityType,
      userId: row.userId,
      ipAddress: row.ipAddress,
      createdAt: row.createdAt,
    };
  }
}
