import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Req,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { ActivityLogService } from './activity-log.service';
import { CreateActivityLogDto } from './dto/create-activity-log.dto';
import { UpdateActivityLogDto } from './dto/update-activity-log.dto';
import { GetActivityLogsQueryDto } from './dto/get-activity-logs-query.dto';
import { ActivityLogDto } from './dto/activity-log.dto';
import { PaginatedActivityLogsResponseDto } from './dto/paginated-activity-logs-response.dto';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { Permission } from '../shared/types/permission.enum';
import { EntityType } from '../shared/types';

@ApiTags('Activity Logs')
@ApiBearerAuth()
@Controller('activity-logs')
@UseGuards(PermissionsGuard)
export class ActivityLogController {
  constructor(private readonly activityLogService: ActivityLogService) {}

  @Post()
  @RequirePermissions(Permission.ACTIVITY_LOG_CREATE)
  @ApiOperation({
    summary: 'Create a new activity log entry',
    description: 'Creates a new activity log entry for audit trail purposes',
  })
  @ApiResponse({
    status: 201,
    description: 'Activity log has been successfully created',
    type: ActivityLogDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async create(
    @Body() createActivityLogDto: CreateActivityLogDto,
  ): Promise<ActivityLogDto> {
    return this.activityLogService.create(createActivityLogDto);
  }

  @Get()
  @RequirePermissions(Permission.ACTIVITY_LOG_READ)
  @ApiOperation({
    summary: 'Get paginated activity logs',
    description:
      'Retrieves activity logs with pagination and filtering options',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved activity logs',
    type: PaginatedActivityLogsResponseDto,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async findAll(
    @Query() query: GetActivityLogsQueryDto,
    @Req() req: any,
  ): Promise<PaginatedActivityLogsResponseDto> {
    const businessId = req.user.activeBusinessId;
    return this.activityLogService.findAll(businessId, query);
  }

  @Get('counts')
  @RequirePermissions(Permission.ACTIVITY_LOG_READ)
  @ApiOperation({
    summary: 'Get activity counts by entity type',
    description: 'Retrieves count of activities grouped by entity type',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved activity counts',
    schema: {
      type: 'object',
      additionalProperties: {
        type: 'number',
      },
      example: {
        [EntityType.PRODUCT]: 145,
        [EntityType.CUSTOMER]: 89,
        [EntityType.ORDER]: 234,
      },
    },
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async getActivityCounts(
    @Req() req: any,
  ): Promise<Record<EntityType, number>> {
    const businessId = req.user.activeBusinessId;
    return this.activityLogService.getActivityCounts(businessId);
  }

  @Get(':id')
  @RequirePermissions(Permission.ACTIVITY_LOG_READ)
  @ApiOperation({
    summary: 'Get activity log by ID',
    description: 'Retrieves a specific activity log entry by its ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Activity log ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved activity log',
    type: ActivityLogDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Activity log not found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async findOne(
    @Param('id') id: string,
    @Req() req: any,
  ): Promise<ActivityLogDto> {
    const businessId = req.user.activeBusinessId;
    return this.activityLogService.findOne(id, businessId);
  }

  @Patch(':id')
  @RequirePermissions(Permission.ACTIVITY_LOG_CREATE)
  @ApiOperation({
    summary: 'Update activity log metadata',
    description:
      'Updates limited fields of an activity log entry (mainly metadata)',
  })
  @ApiParam({
    name: 'id',
    description: 'Activity log ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: 'Activity log has been successfully updated',
    type: ActivityLogDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Activity log not found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async update(
    @Param('id') id: string,
    @Body() updateActivityLogDto: UpdateActivityLogDto,
    @Req() req: any,
  ): Promise<ActivityLogDto> {
    const businessId = req.user.activeBusinessId;
    return this.activityLogService.update(id, businessId, updateActivityLogDto);
  }

  @Delete(':id')
  @RequirePermissions(Permission.ACTIVITY_LOG_DELETE)
  @ApiOperation({
    summary: 'Delete activity log',
    description: 'Permanently deletes an activity log entry (use with caution)',
  })
  @ApiParam({
    name: 'id',
    description: 'Activity log ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: 'Activity log has been successfully deleted',
    type: ActivityLogDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Activity log not found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async remove(
    @Param('id') id: string,
    @Req() req: any,
  ): Promise<ActivityLogDto> {
    const businessId = req.user.activeBusinessId;
    return this.activityLogService.remove(id, businessId);
  }
}
