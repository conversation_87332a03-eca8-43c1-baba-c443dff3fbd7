import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ActivityType, EntityType } from '../../shared/types';

export class ActivityLogDto {
  @ApiProperty({
    description: 'Unique identifier for the activity log',
    example: '550e8400-e29b-41d4-a716-************',
  })
  id: string;

  @ApiProperty({
    description: 'Type of entity being acted upon',
    enum: EntityType,
    example: EntityType.PRODUCT,
  })
  entityType: EntityType;

  @ApiProperty({
    description: 'UUID of the entity being acted upon',
    example: '550e8400-e29b-41d4-a716-************',
  })
  entityId: string;

  @ApiProperty({
    description: 'UUID of the business this activity belongs to',
    example: '550e8400-e29b-41d4-a716-************',
  })
  businessId: string;

  @ApiProperty({
    description: 'Type of activity performed',
    enum: ActivityType,
    example: ActivityType.CREATE,
  })
  activityType: ActivityType;

  @ApiProperty({
    description: 'UUID of the user who performed the activity',
    example: '550e8400-e29b-41d4-a716-************',
  })
  userId: string;

  @ApiPropertyOptional({
    description: 'Before/after values for tracking changes',
    type: 'object',
    example: {
      before: { name: 'Old Product Name', price: '10.00' },
      after: { name: 'New Product Name', price: '15.00' },
    },
  })
  changes?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Additional context and metadata for the activity',
    type: 'object',
    example: {
      source: 'web_dashboard',
      bulkOperation: false,
      affectedCount: 1,
    },
  })
  metadata?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'IP address of the user who performed the activity',
    example: '***********',
  })
  ipAddress?: string;

  @ApiPropertyOptional({
    description: 'User agent string from the client',
    example: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  })
  userAgent?: string;

  @ApiProperty({
    description: 'When the activity was performed',
    example: '2024-01-01T12:00:00.000Z',
  })
  createdAt: Date;
}
