import {
  <PERSON>E<PERSON>,
  <PERSON>NotEmpty,
  IsObject,
  IsOptional,
  IsString,
  IsU<PERSON><PERSON>,
  IsIP,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ActivityType, EntityType } from '../../shared/types';

export class CreateActivityLogDto {
  @ApiProperty({
    description: 'Type of entity being acted upon',
    enum: EntityType,
    example: EntityType.PRODUCT,
  })
  @IsNotEmpty()
  @IsEnum(EntityType)
  entityType: EntityType;

  @ApiProperty({
    description: 'UUID of the entity being acted upon',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsNotEmpty()
  @IsUUID()
  entityId: string;

  @ApiProperty({
    description: 'UUID of the business this activity belongs to',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsNotEmpty()
  @IsUUID()
  businessId: string;

  @ApiProperty({
    description: 'Type of activity performed',
    enum: ActivityType,
    example: ActivityType.CREATE,
  })
  @IsNotEmpty()
  @IsEnum(ActivityType)
  activityType: ActivityType;

  @ApiProperty({
    description: 'UUID of the user who performed the activity',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsNotEmpty()
  @IsUUID()
  userId: string;

  @ApiPropertyOptional({
    description: 'Before/after values for tracking changes',
    type: 'object',
    additionalProperties: true,
    example: {
      before: { name: 'Old Product Name', price: '10.00' },
      after: { name: 'New Product Name', price: '15.00' },
    },
  })
  @IsOptional()
  @IsObject()
  changes?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Additional context and metadata for the activity',
    type: 'object',
    additionalProperties: true,
    example: {
      source: 'web_dashboard',
      bulkOperation: false,
      affectedCount: 1,
    },
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'IP address of the user who performed the activity',
    example: '***********',
  })
  @IsOptional()
  @IsIP()
  ipAddress?: string;

  @ApiPropertyOptional({
    description: 'User agent string from the client',
    example: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  })
  @IsOptional()
  @IsString()
  userAgent?: string;
}
