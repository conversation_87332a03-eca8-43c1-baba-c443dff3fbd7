import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ActivityType, EntityType } from '../../shared/types';

export class ActivityLogListDto {
  @ApiProperty({
    description: 'Unique identifier for the activity log',
    example: '550e8400-e29b-41d4-a716-************',
  })
  id: string;

  @ApiProperty({
    description: 'Type of entity being acted upon',
    enum: EntityType,
    example: EntityType.PRODUCT,
  })
  entityType: EntityType;

  @ApiProperty({
    description: 'UUID of the entity being acted upon',
    example: '550e8400-e29b-41d4-a716-************',
  })
  entityId: string;

  @ApiProperty({
    description: 'Type of activity performed',
    enum: ActivityType,
    example: ActivityType.CREATE,
  })
  activityType: ActivityType;

  @ApiProperty({
    description: 'UUID of the user who performed the activity',
    example: '550e8400-e29b-41d4-a716-************',
  })
  userId: string;

  @ApiPropertyOptional({
    description: 'IP address of the user who performed the activity',
    example: '***********',
  })
  ipAddress?: string;

  @ApiProperty({
    description: 'When the activity was performed',
    example: '2024-01-01T12:00:00.000Z',
  })
  createdAt: Date;
}
