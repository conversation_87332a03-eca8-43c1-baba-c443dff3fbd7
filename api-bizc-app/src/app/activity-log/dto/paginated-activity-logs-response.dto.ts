import { ApiProperty } from '@nestjs/swagger';
import { ActivityLogListDto } from './activity-log-list.dto';

class PaginationMeta {
  @ApiProperty({
    description: 'Total number of activity logs',
    example: 150,
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 15,
  })
  totalPages: number;
}

export class PaginatedActivityLogsResponseDto {
  @ApiProperty({
    description: 'Array of activity logs',
    type: [ActivityLogListDto],
  })
  data: ActivityLogListDto[];

  @ApiProperty({
    description: 'Pagination metadata',
    type: PaginationMeta,
  })
  meta: PaginationMeta;
}
