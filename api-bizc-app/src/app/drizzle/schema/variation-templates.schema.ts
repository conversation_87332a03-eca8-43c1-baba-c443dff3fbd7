import { relations } from 'drizzle-orm';
import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { variationValueTemplates } from './variation-value-templates.schema';
import { business } from './business.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { StatusType } from '../../shared/types';

export const variationTemplateStatusEnum = pgEnum('variation_template_status', [
  StatusType.ACTIVE,
  StatusType.INACTIVE,
]);

export const variationTemplates = pgTable(
  'variation_templates',
  {
    ...createBaseEntityBusinessFields(business),
    name: text('name').notNull(),
    description: text('description'),
    status: variationTemplateStatusEnum('status')
      .default(StatusType.ACTIVE)
      .notNull(),
  },
  (t) => ({
    // Standard base entity indexes
    ...createBaseEntityBusinessIndexes(t, 'variation_templates'),
    nameIndex: index('variation_templates_name_index').on(t.name),

    // Optimized indexes for filtering and searching
    businessNameIndex: index('variation_templates_business_name_index')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessName: uniqueIndex('variation_templates_business_name_unique')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
  }),
);

export const variationTemplatesRelations = relations(
  variationTemplates,
  ({ many }) => ({
    variationValueTemplates: many(variationValueTemplates),
  }),
);

export type VariationTemplate = typeof variationTemplates.$inferSelect;
export type NewVariationTemplate = typeof variationTemplates.$inferInsert;
