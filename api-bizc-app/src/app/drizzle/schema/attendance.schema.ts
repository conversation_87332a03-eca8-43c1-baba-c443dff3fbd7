import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  date,
  time,
  decimal,
  integer,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import { staffMembers } from './staff.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { AttendanceStatus } from '../../shared/types';

// Database enum for attendance status
export const attendanceStatusEnum = pgEnum('attendance_status', [
  AttendanceStatus.PRESENT,
  AttendanceStatus.ABSENT,
  AttendanceStatus.LATE,
  AttendanceStatus.EARLY_OUT,
  AttendanceStatus.HALF_DAY,
  AttendanceStatus.OVERTIME,
  AttendanceStatus.HOLIDAY,
  AttendanceStatus.WEEKEND,
  AttendanceStatus.ON_LEAVE,
  AttendanceStatus.REMOTE,
  AttendanceStatus.ON_DUTY,
  AttendanceStatus.COMP_OFF,
]);

// Attendance table schema
export const attendance = pgTable(
  'attendance',
  {
    attendanceId: integer('attendance_id')
      .primaryKey()
      .generatedByDefaultAsIdentity(),
    ...createBaseEntityBusinessFields(business),
    staffId: uuid('staff_id')
      .notNull()
      .references(() => staffMembers.id),
    attendanceDate: date('attendance_date').notNull(),
    checkInTime: time('check_in_time'),
    checkOutTime: time('check_out_time'),
    hoursWorked: decimal('hours_worked', { precision: 5, scale: 2 }),
    status: attendanceStatusEnum('status')
      .default(AttendanceStatus.PRESENT)
      .notNull(),
    remarks: text('remarks'),
  },
  (t) => ({
    // Standard base entity indexes
    ...createBaseEntityBusinessIndexes(t, 'attendance'),

    // Entity-specific indexes
    attendanceIdIndex: index('attendance_attendance_id_index').on(
      t.attendanceId,
    ),
    staffIdIndex: index('attendance_staff_id_index').on(t.staffId),
    attendanceDateIndex: index('attendance_attendance_date_index').on(
      t.attendanceDate,
    ),
    statusIndex: index('attendance_status_index').on(t.status),

    // Composite indexes for common queries
    businessStaffDateIndex: index('attendance_business_staff_date_index')
      .on(t.businessId, t.staffId, t.attendanceDate)
      .where(sql`${t.isDeleted} = false`),
    businessDateIndex: index('attendance_business_date_index')
      .on(t.businessId, t.attendanceDate)
      .where(sql`${t.isDeleted} = false`),
    businessStatusIndex: index('attendance_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraint to prevent duplicate attendance records for the same staff member on the same date
    uniqueStaffDateBusiness: uniqueIndex(
      'attendance_staff_date_business_unique',
    )
      .on(t.staffId, t.attendanceDate, t.businessId)
      .where(sql`${t.isDeleted} = false`),
  }),
);
