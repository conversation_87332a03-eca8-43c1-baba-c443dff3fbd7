import {
  index,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uuid,
  uniqueIndex,
  jsonb,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import { staffMembers } from './staff.schema';
import { tasks } from './tasks.schema';
import { accommodationUnits } from './accommodation-units.schema';
import { recurringActivities } from './recurring-activities.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

// Housekeeping Type Enum
export enum HousekeepingType {
  CHECKOUT_CLEANING = 'checkout_cleaning',
  MAINTENANCE_CLEANING = 'maintenance_cleaning',
  DEEP_CLEANING = 'deep_cleaning',
  INSPECTION = 'inspection',
  ROOM_TURNOVER = 'room_turnover',
  PREVENTIVE_MAINTENANCE = 'preventive_maintenance',
  REPAIR = 'repair',
  INVENTORY_CHECK = 'inventory_check',
}

// Housekeeping Status Enum
export enum HousekeepingStatus {
  PENDING = 'pending',
  ASSIGNED = 'assigned',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  INSPECTED = 'inspected',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled',
}

export const housekeepingTypeEnum = pgEnum('housekeeping_type', [
  HousekeepingType.CHECKOUT_CLEANING,
  HousekeepingType.MAINTENANCE_CLEANING,
  HousekeepingType.DEEP_CLEANING,
  HousekeepingType.INSPECTION,
  HousekeepingType.ROOM_TURNOVER,
  HousekeepingType.PREVENTIVE_MAINTENANCE,
  HousekeepingType.REPAIR,
  HousekeepingType.INVENTORY_CHECK,
]);

export const housekeepingStatusEnum = pgEnum('housekeeping_status', [
  HousekeepingStatus.PENDING,
  HousekeepingStatus.ASSIGNED,
  HousekeepingStatus.IN_PROGRESS,
  HousekeepingStatus.COMPLETED,
  HousekeepingStatus.INSPECTED,
  HousekeepingStatus.APPROVED,
  HousekeepingStatus.REJECTED,
  HousekeepingStatus.CANCELLED,
]);

// Housekeeping table
export const housekeeping = pgTable(
  'housekeeping',
  {
    ...createBaseEntityBusinessFields(business),

    // Core housekeeping fields
    type: housekeepingTypeEnum('type').notNull(),
    status: housekeepingStatusEnum('status')
      .default(HousekeepingStatus.PENDING)
      .notNull(),

    // Actual timing
    actualStartTime: timestamp('actual_start_time'),
    actualEndTime: timestamp('actual_end_time'),

    // References
    accommodationUnitId: uuid('accommodation_unit_id')
      .notNull()
      .references(() => accommodationUnits.id),
    taskId: uuid('task_id').references(() => tasks.id), // Optional task reference

    // Recurring functionality
    recurringActivityId: uuid('recurring_activity_id').references(
      () => recurringActivities.id,
    ),
    parentHousekeepingId: uuid('parent_housekeeping_id').references(
      () => housekeeping.id,
    ), // Reference to original recurring housekeeping

    // Additional information
    checklist: jsonb('checklist').$type<{
      items: Array<{
        id: string;
        title: string;
        completed: boolean;
        notes?: string;
      }>;
    }>(),
    notes: text('notes'),
    issuesFound: text('issues_found'),

    // Quality control
    inspectedBy: uuid('inspected_by').references(() => staffMembers.id),
    inspectionNotes: text('inspection_notes'),
    inspectionDate: timestamp('inspection_date'),
    qualityScore: text('quality_score'), // Rating system (e.g., "excellent", "good", "fair", "poor")
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'housekeeping'),
    accommodationUnitIdIndex: index(
      'housekeeping_accommodation_unit_id_index',
    ).on(t.accommodationUnitId),
    taskIdIndex: index('housekeeping_task_id_index').on(t.taskId),
    typeIndex: index('housekeeping_type_index').on(t.type),
    statusIndex: index('housekeeping_status_index').on(t.status),
    inspectedByIndex: index('housekeeping_inspected_by_index').on(
      t.inspectedBy,
    ),

    // Recurring indexes
    recurringActivityIdIndex: index(
      'housekeeping_recurring_activity_id_index',
    ).on(t.recurringActivityId),
    parentHousekeepingIdIndex: index(
      'housekeeping_parent_housekeeping_id_index',
    ).on(t.parentHousekeepingId),

    // Date/time indexes
    actualStartTimeIndex: index('housekeeping_actual_start_time_index').on(
      t.actualStartTime,
    ),
    actualEndTimeIndex: index('housekeeping_actual_end_time_index').on(
      t.actualEndTime,
    ),
    inspectionDateIndex: index('housekeeping_inspection_date_index').on(
      t.inspectionDate,
    ),

    // Composite indexes for common queries with soft deletion support
    businessTypeIndex: index('housekeeping_business_type_index')
      .on(t.businessId, t.type)
      .where(sql`${t.isDeleted} = false`),
    businessUnitIndex: index('housekeeping_business_unit_index')
      .on(t.businessId, t.accommodationUnitId)
      .where(sql`${t.isDeleted} = false`),
    businessStatusIndex: index('housekeeping_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),

    // Recurring composite indexes
    businessRecurringIndex: index('housekeeping_business_recurring_index')
      .on(t.businessId, t.recurringActivityId)
      .where(sql`${t.isDeleted} = false`),
    businessParentIndex: index('housekeeping_business_parent_index')
      .on(t.businessId, t.parentHousekeepingId)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraint for preventing duplicate housekeeping entries
    uniqueBusinessUnitType: uniqueIndex(
      'housekeeping_business_unit_type_unique',
    )
      .on(t.businessId, t.accommodationUnitId, t.type)
      .where(sql`${t.isDeleted} = false`),
  }),
);
