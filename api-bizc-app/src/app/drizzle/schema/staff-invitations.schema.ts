import {
  index,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uuid,
} from 'drizzle-orm/pg-core';
import { business } from './business.schema';
import { staffMembers } from './staff.schema';
import { DocumentStatus } from '../../shared/types';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

export const invitationStatusEnum = pgEnum('invitation_status', [
  'PENDING',
  'ACCEPTED',
  'REJECTED',
  'EXPIRED',
]);

export const invitationDocumentStatusEnum = pgEnum(
  'invitation_document_status',
  [DocumentStatus.ACTIVE, DocumentStatus.ARCHIVED],
);

export const staffInvitations = pgTable(
  'staff_invitations',
  {
    ...createBaseEntityBusinessFields(business),
    staffMemberId: uuid('staff_member_id')
      .notNull()
      .references(() => staffMembers.id),
    status: invitationStatusEnum('status').default('PENDING').notNull(),
    role: text('role'),
    message: text('message'),
    expiresAt: timestamp('expires_at').notNull(),
    acceptedAt: timestamp('accepted_at'),
    documentStatus: invitationDocumentStatusEnum('document_status')
      .default(DocumentStatus.ACTIVE)
      .notNull(),
  },
  (t) => ({
    idIndex: index('staff_invitations_id_index').on(t.id),
    ...createBaseEntityBusinessIndexes(t, 'staff_invitations'),
    staffMemberIdIndex: index('staff_invitations_staff_member_id_index').on(
      t.staffMemberId,
    ),
  }),
);
