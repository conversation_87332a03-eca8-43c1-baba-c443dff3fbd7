import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
} from 'drizzle-orm/pg-core';
import { business } from './business.schema';
import { StatusType } from '../../shared/types';
import { createBaseEntityBusinessFields } from './common-fields.schema';

export const paymentAccountTypeStatusEnum = pgEnum(
  'payment_account_type_status',
  [StatusType.ACTIVE, StatusType.INACTIVE],
);

export const paymentAccountTypes = pgTable(
  'payment_account_types',
  {
    ...createBaseEntityBusinessFields(business),
    name: text('name').notNull(),
    parentAccountTypeId: uuid('parent_account_type_id').references(
      () => paymentAccountTypes.id,
    ),
    status: paymentAccountTypeStatusEnum('status')
      .default(StatusType.ACTIVE)
      .notNull(),
  },
  (t) => ({
    idIndex: index('payment_account_types_id_index').on(t.id),
    businessIdIndex: index('payment_account_types_business_id_index').on(
      t.businessId,
    ),
    nameIndex: index('payment_account_types_name_index').on(t.name),
    parentAccountTypeIdIndex: index('payment_account_types_parent_id_index').on(
      t.parentAccountTypeId,
    ),
    uniqueBusinessName: uniqueIndex(
      'payment_account_types_business_name_unique',
    ).on(t.businessId, t.name),
  }),
);
