import { index, pgTable, text, uuid, pgEnum } from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import { media } from './media.schema';
import { staffMembers } from './staff.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { StatusType } from '../../shared/types';

export const commentStatusEnum = pgEnum('comment_status', [
  StatusType.ACTIVE,
  StatusType.INACTIVE,
]);

export const comments = pgTable(
  'comments',
  {
    ...createBaseEntityBusinessFields(business),

    // Reference to any entity this comment belongs to
    referenceId: uuid('reference_id').notNull(),

    // Self-referencing foreign key for comment replies
    parentId: uuid('parent_id').references(() => comments.id),

    // Comment content
    content: text('content').notNull(),

    // Optional attachment
    attachmentId: uuid('attachment_id').references(() => media.id),

    // Author reference to staff member
    authorId: uuid('author_id')
      .notNull()
      .references(() => staffMembers.id),

    // Status field
    status: commentStatusEnum('status').default(StatusType.ACTIVE).notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'comments'),
    referenceIdIndex: index('comments_reference_id_index').on(t.referenceId),
    parentIdIndex: index('comments_parent_id_index').on(t.parentId),
    authorIdIndex: index('comments_author_id_index').on(t.authorId),
    attachmentIdIndex: index('comments_attachment_id_index').on(t.attachmentId),
    statusIndex: index('comments_status_index').on(t.status),

    // Composite indexes for common queries
    businessReferenceIndex: index('comments_business_reference_index')
      .on(t.businessId, t.referenceId)
      .where(sql`${t.isDeleted} = false`),
    businessAuthorIndex: index('comments_business_author_index')
      .on(t.businessId, t.authorId)
      .where(sql`${t.isDeleted} = false`),
    businessStatusIndex: index('comments_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
  }),
);
