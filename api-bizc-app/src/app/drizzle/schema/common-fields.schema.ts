import {
  boolean,
  text,
  timestamp,
  uuid,
  decimal,
  pgEnum,
  index,
} from 'drizzle-orm/pg-core';
import { users } from './users.schema';
import { taxes } from './taxes.schema';
import { TaxType } from '../../shared/types';
import { PaymentStatus } from '../../shared/types/common.enum';
import { DiscountType } from '../../shared/types/common.enum';

/**
 * Common reusable field definitions for schema consistency
 * These field sets can be spread into table definitions to ensure consistency
 * across entities that share similar data structures.
 */

/**
 * Personal name fields commonly used across entities like customers, leads, suppliers, staff
 * Includes title, first name, middle name, last name, and suffix
 */
export const personalNameFields = {
  title: text('title'),
  firstName: text('first_name'),
  middleName: text('middle_name'),
  lastName: text('last_name'),
  suffix: text('suffix'),
} as const;

/**
 * Contact information fields commonly used across entities
 * Includes email, phone numbers, fax, and website
 */
export const contactInfoFields = {
  email: text('email'),
  phoneNumber: text('phone_number'),
  mobileNumber: text('mobile_number'),
  fax: text('fax'),
  website: text('website'),
} as const;

/**
 * Extended contact information fields that includes additional contact methods
 * Includes all basic contact fields plus 'other' field for additional contact info
 */
export const extendedContactInfoFields = {
  ...contactInfoFields,
  other: text('other'),
} as const;

/**
 * Standard audit fields for tracking entity lifecycle
 * Includes created/updated/deleted by users and timestamps
 */
export const auditFields = {
  createdBy: uuid('created_by')
    .notNull()
    .references(() => users.id),
  updatedBy: uuid('updated_by').references(() => users.id),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  isDeleted: boolean('is_deleted').default(false).notNull(),
} as const;

/**
 * Basic entity fields that most entities need
 * Includes ID, business reference, and audit fields
 */
export const createBaseEntityBusinessFields = (businessReference: any) =>
  ({
    id: uuid('id').defaultRandom().primaryKey(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => businessReference.id),
    ...auditFields,
  }) as const;

/**
 * Standard indexes for base entity business fields
 * Creates consistent indexing patterns across all business entities
 * @param tableColumns - The table columns object (usually 't' parameter from pgTable)
 * @param tableName - The table name for index naming
 * @returns Object with standard indexes for base entity fields
 */
export const createBaseEntityBusinessIndexes = (
  tableColumns: any,
  tableName: string,
) => ({
  businessIdIndex: index(`${tableName}_business_id_index`).on(
    tableColumns.businessId,
  ),
  createdByIndex: index(`${tableName}_created_by_index`).on(
    tableColumns.createdBy,
  ),
  updatedByIndex: index(`${tableName}_updated_by_index`).on(
    tableColumns.updatedBy,
  ),
  createdAtIndex: index(`${tableName}_created_at_index`).on(
    tableColumns.createdAt,
  ),
  updatedAtIndex: index(`${tableName}_updated_at_index`).on(
    tableColumns.updatedAt,
  ),
  isDeletedIndex: index(`${tableName}_is_deleted_index`).on(
    tableColumns.isDeleted,
  ),
});

/**
 * Location allocation fields for entities that can be assigned to business locations
 * Includes flag for all locations allocation
 */
export const locationAllocationFields = {
  isAllocatedToAllLocations: boolean('is_allocated_to_all_locations')
    .default(false)
    .notNull(),
} as const;

/**
 * Additional information fields commonly used across entities
 * Includes notes, tags, and custom fields
 */
export const additionalInfoFields = {
  notes: text('notes'),
  tags: text('tags').array().default([]),
  customFields: text('custom_fields'), // JSON string for flexible custom fields
} as const;

/**
 * Recurring activity reference fields for entities that support recurring functionality
 * Includes reference to the recurring activities table
 */
export const recurringActivityFields = {
  recurringActivityId: uuid('recurring_activity_id'), // Optional reference to recurring_activities table
} as const;

/**
 * Tax Type Enum for reusable tax type fields
 */
export const taxTypeEnum = pgEnum('tax_type', [
  TaxType.INCLUSIVE,
  TaxType.EXCLUSIVE,
  TaxType.OUT_OF_SCOPE,
]);

/**
 * Payment Status Enum for reusable payment status fields
 */
export const paymentStatusEnum = pgEnum('payment_status', [
  PaymentStatus.PENDING,
  PaymentStatus.PARTIAL,
  PaymentStatus.PAID,
  PaymentStatus.REFUNDED,
  PaymentStatus.CANCELLED,
  PaymentStatus.PARTIALLY_REFUNDED,
  PaymentStatus.PARTIALLY_PAID,
  PaymentStatus.OVERDUE,
]);

/**
 * Discount Type Enum for reusable discount type fields
 */
export const discountTypeEnum = pgEnum('discount_type', [
  DiscountType.PERCENTAGE,
  DiscountType.FIXED_AMOUNT,
  DiscountType.VALUE,
]);

/**
 * Pricing fields commonly used across entities that involve pricing
 * Includes base price, tax type, and tax reference
 */
export const pricingFields = {
  basePrice: decimal('base_price', { precision: 15, scale: 2 }).notNull(),
  taxType: taxTypeEnum('tax_type').default(TaxType.INCLUSIVE).notNull(),
  taxId: uuid('tax_id')
    .notNull()
    .references(() => taxes.id),
} as const;
