import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  time,
  boolean,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { users } from './users.schema';
import { business } from './business.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import {
  RestaurantTimeSlotType,
  SlotStatus,
  DayOfWeek,
} from '../../shared/types/time-slot.enum';

// Restaurant Time Slot Type enum
export const restaurantTimeSlotTypeEnum = pgEnum('restaurant_time_slot_type', [
  RestaurantTimeSlotType.BREAKFAST,
  RestaurantTimeSlotType.BRUNCH,
  RestaurantTimeSlotType.LUNCH,
  RestaurantTimeSlotType.HAPPY_HOUR,
  RestaurantTimeSlotType.DINNER,
  RestaurantTimeSlotType.LATE_NIGHT,
]);

// Slot Status enum
export const slotStatusEnum = pgEnum('slot_status', [
  SlotStatus.AVAILABLE,
  SlotStatus.BLOCKED,
]);

// Day of Week enum
export const dayOfWeekEnum = pgEnum('day_of_week', [
  DayOfWeek.MONDAY,
  DayOfWeek.TUESDAY,
  DayOfWeek.WEDNESDAY,
  DayOfWeek.THURSDAY,
  DayOfWeek.FRIDAY,
  DayOfWeek.SATURDAY,
  DayOfWeek.SUNDAY,
]);

// Restaurant Time Slots Schema
export const restaurantTimeSlots = pgTable(
  'restaurant_time_slots',
  {
    ...createBaseEntityBusinessFields(business),
    name: text('name').notNull(),
    timeSlotType: restaurantTimeSlotTypeEnum('time_slot_type').notNull(),
    startTime: time('start_time').notNull(),
    endTime: time('end_time').notNull(),
    status: slotStatusEnum('status').default(SlotStatus.AVAILABLE).notNull(),
    isActive: boolean('is_active').default(true).notNull(),
    description: text('description'),

    // Days of the week this slot is available
    availableDays: dayOfWeekEnum('available_days').array().notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'restaurant_time_slots'),
    nameIndex: index('restaurant_time_slots_name_index').on(t.name),
    timeSlotTypeIndex: index('restaurant_time_slots_type_index').on(
      t.timeSlotType,
    ),
    statusIndex: index('restaurant_time_slots_status_index').on(t.status),
    isActiveIndex: index('restaurant_time_slots_is_active_index').on(
      t.isActive,
    ),
    startTimeIndex: index('restaurant_time_slots_start_time_index').on(
      t.startTime,
    ),
    endTimeIndex: index('restaurant_time_slots_end_time_index').on(t.endTime),
    createdByIndex: index('restaurant_time_slots_created_by_index').on(
      t.createdBy,
    ),

    // Composite indexes for optimal queries
    businessTypeIndex: index('restaurant_time_slots_business_type_index')
      .on(t.businessId, t.timeSlotType)
      .where(sql`${t.isDeleted} = false`),
    businessStatusIndex: index('restaurant_time_slots_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessActiveIndex: index('restaurant_time_slots_business_active_index')
      .on(t.businessId, t.isActive)
      .where(sql`${t.isDeleted} = false`),
    timeRangeIndex: index('restaurant_time_slots_time_range_index')
      .on(t.startTime, t.endTime)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessName: uniqueIndex(
      'restaurant_time_slots_business_name_unique',
    )
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessTypeTime: uniqueIndex(
      'restaurant_time_slots_business_type_time_unique',
    )
      .on(t.businessId, t.timeSlotType, t.startTime, t.endTime)
      .where(sql`${t.isDeleted} = false`),
  }),
);
