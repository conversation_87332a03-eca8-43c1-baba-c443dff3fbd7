import {
  integer,
  pgEnum,
  pgTable,
  uuid,
  uniqueIndex,
  index,
} from 'drizzle-orm/pg-core';
import { business } from './business.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import {
  DefaultTaxRateSelection,
  AccountingMethod,
  Month,
} from '../../shared/types/accounting.enum';

/**
 * Default Tax Rate Selection Enum
 * Defines how tax rates are applied by default in the business
 */
export const defaultTaxRateSelectionEnum = pgEnum(
  'default_tax_rate_selection',
  [
    DefaultTaxRateSelection.EXCLUSIVE_OF_TAX,
    DefaultTaxRateSelection.INCLUSIVE_OF_TAX,
    DefaultTaxRateSelection.OUT_OF_SCOPE_TAX,
  ],
);

/**
 * Accounting Method Enum
 * Defines the accounting method used by the business
 */
export const accountingMethodEnum = pgEnum('accounting_method', [
  AccountingMethod.ACCRUAL,
  AccountingMethod.CASH,
]);

/**
 * Business Accounting Settings Schema
 * Stores accounting configuration for each business
 *
 * This table contains all the accounting settings for a business including:
 * - Default tax rate selection (exclusive, inclusive, or out of scope)
 * - Accounting method (accrual or cash)
 * - First month of financial year (1-12)
 * - First month of tax year (1-12)
 */
export const businessAccountingSettings = pgTable(
  'business_accounting_settings',
  {
    ...createBaseEntityBusinessFields(business),

    // Default tax rate selection
    defaultTaxRateSelection: defaultTaxRateSelectionEnum(
      'default_tax_rate_selection',
    )
      .default(DefaultTaxRateSelection.EXCLUSIVE_OF_TAX)
      .notNull(),

    // Accounting method
    accountingMethod: accountingMethodEnum('accounting_method')
      .default(AccountingMethod.ACCRUAL)
      .notNull(),

    // First month of financial year (1-12)
    firstMonthOfFinancialYear: integer('first_month_of_financial_year')
      .default(Month.JANUARY)
      .notNull(),

    // First month of tax year (1-12)
    firstMonthOfTaxYear: integer('first_month_of_tax_year')
      .default(Month.JANUARY)
      .notNull(),
  },
  (t) => ({
    // Standard base entity indexes
    ...createBaseEntityBusinessIndexes(t, 'business_accounting_settings'),

    // Entity-specific constraints
    uniqueBusinessAccountingSettings: uniqueIndex(
      'business_accounting_settings_business_unique',
    ).on(t.businessId),
  }),
);

/**
 * Default business accounting settings
 */
export const DEFAULT_BUSINESS_ACCOUNTING_SETTINGS = {
  defaultTaxRateSelection: DefaultTaxRateSelection.EXCLUSIVE_OF_TAX,
  accountingMethod: AccountingMethod.ACCRUAL,
  firstMonthOfFinancialYear: Month.JANUARY,
  firstMonthOfTaxYear: Month.JANUARY,
} as const;
