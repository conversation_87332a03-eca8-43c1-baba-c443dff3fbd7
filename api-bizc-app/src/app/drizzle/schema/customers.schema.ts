import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  boolean,
  numeric,
  date,
  integer,
  timestamp,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import { addresses } from './address.schema';
import { customerGroups } from './customer-groups.schema';
import { locations } from './locations.schema';
import {
  personalNameFields,
  extendedContactInfoFields,
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
  locationAllocationFields,
  auditFields,
} from './common-fields.schema';
import { CustomerStatus } from '../../shared/types/customer.enum';
import { PointTransactionType } from '../../shared/types/reward.enum';

export const customerStatusEnum = pgEnum('customer_status', [
  CustomerStatus.ACTIVE,
  CustomerStatus.INACTIVE,
]);

export const customers = pgTable(
  'customers',
  {
    ...createBaseEntityBusinessFields(business),

    // Name and contact information (using reusable fields)
    ...personalNameFields,
    customerDisplayName: text('customer_display_name').notNull(),
    companyName: text('company_name'),

    // Contact details (using reusable fields)
    ...extendedContactInfoFields,

    // Customer relationship
    isSubCustomer: boolean('is_sub_customer').default(false).notNull(),

    // Location allocation (using reusable fields)
    ...locationAllocationFields,

    // Address references
    billingAddressId: uuid('billing_address_id').references(() => addresses.id),
    shippingAddressId: uuid('shipping_address_id').references(
      () => addresses.id,
    ),

    // Customer group relationship
    customerGroupId: uuid('customer_group_id').references(
      () => customerGroups.id,
    ),

    // Notes and attachments
    notes: text('notes'),
    attachments: uuid('attachments').array(),

    // Additional information
    salesTaxRegistration: text('sales_tax_registration'),
    openingBalance: numeric('opening_balance', { precision: 15, scale: 2 }),
    openingBalanceAsOf: date('opening_balance_as_of'),

    // Status and audit fields
    status: customerStatusEnum('status')
      .default(CustomerStatus.ACTIVE)
      .notNull(),

    // Blacklist information
    isBlacklisted: boolean('is_blacklisted').default(false).notNull(),
    blacklistReason: text('blacklist_reason'),
    blacklistDate: timestamp('blacklist_date'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'customers'),
    // Entity-specific indexes
    customerDisplayNameIndex: index('customers_display_name_index').on(
      t.customerDisplayName,
    ),
    companyNameIndex: index('customers_company_name_index').on(t.companyName),
    emailIndex: index('customers_email_index').on(t.email),
    phoneNumberIndex: index('customers_phone_number_index').on(t.phoneNumber),
    mobileNumberIndex: index('customers_mobile_number_index').on(
      t.mobileNumber,
    ),
    statusIndex: index('customers_status_index').on(t.status),

    // Foreign key indexes
    customerGroupIdIndex: index('customers_customer_group_id_index').on(
      t.customerGroupId,
    ),
    billingAddressIdIndex: index('customers_billing_address_id_index').on(
      t.billingAddressId,
    ),
    shippingAddressIdIndex: index('customers_shipping_address_id_index').on(
      t.shippingAddressId,
    ),
    isAllocatedToAllLocationsIndex: index(
      'customers_is_allocated_to_all_locations_index',
    ).on(t.isAllocatedToAllLocations),

    // Blacklist indexes
    isBlacklistedIndex: index('customers_is_blacklisted_index').on(
      t.isBlacklisted,
    ),
    blacklistDateIndex: index('customers_blacklist_date_index').on(
      t.blacklistDate,
    ),

    // Optimized composite indexes for filtering and searching
    businessStatusIndex: index('customers_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessDisplayNameIndex: index('customers_business_display_name_index')
      .on(t.businessId, t.customerDisplayName)
      .where(sql`${t.isDeleted} = false`),
    businessEmailIndex: index('customers_business_email_index')
      .on(t.businessId, t.email)
      .where(sql`${t.isDeleted} = false`),
    businessCompanyNameIndex: index('customers_business_company_name_index')
      .on(t.businessId, t.companyName)
      .where(sql`${t.isDeleted} = false`),
    businessCustomerGroupIndex: index('customers_business_customer_group_index')
      .on(t.businessId, t.customerGroupId)
      .where(sql`${t.isDeleted} = false`),
    businessBlacklistIndex: index('customers_business_blacklist_index')
      .on(t.businessId, t.isBlacklisted)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessDisplayName: uniqueIndex(
      'customers_business_display_name_unique',
    )
      .on(t.businessId, t.customerDisplayName)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessEmail: uniqueIndex('customers_business_email_unique')
      .on(t.businessId, t.email)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Junction table for customer locations (many-to-many relationship)
export const customerLocations = pgTable(
  'customer_locations',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    customerId: uuid('customer_id')
      .notNull()
      .references(() => customers.id, { onDelete: 'cascade' }),
    locationId: uuid('location_id')
      .notNull()
      .references(() => locations.id, { onDelete: 'cascade' }),
    // Standard audit fields (using reusable fields)
    ...auditFields,
  },
  (t) => ({
    customerIdIndex: index('customer_locations_customer_id_index').on(
      t.customerId,
    ),
    locationIdIndex: index('customer_locations_location_id_index').on(
      t.locationId,
    ),
    uniqueCustomerLocation: uniqueIndex('customer_locations_unique').on(
      t.customerId,
      t.locationId,
    ),
  }),
);

// Point Transaction Type Enum
export const pointTransactionTypeEnum = pgEnum('point_transaction_type', [
  PointTransactionType.EARNED,
  PointTransactionType.REDEEMED,
  PointTransactionType.EXPIRED,
  PointTransactionType.ADJUSTED,
]);

// Customer Rewards table
export const customerRewards = pgTable(
  'customer_rewards',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    customerId: uuid('customer_id')
      .notNull()
      .references(() => customers.id, { onDelete: 'cascade' }),
    totalPoints: integer('total_points').default(0).notNull(),
    pointsEarnedYtd: integer('points_earned_ytd').default(0).notNull(),
    lastActivityDate: timestamp('last_activity_date'),
    // Standard audit fields
    ...auditFields,
  },
  (t) => ({
    customerIdIndex: index('customer_rewards_customer_id_index').on(
      t.customerId,
    ),
    totalPointsIndex: index('customer_rewards_total_points_index').on(
      t.totalPoints,
    ),
    lastActivityDateIndex: index(
      'customer_rewards_last_activity_date_index',
    ).on(t.lastActivityDate),
    // Unique constraint - one reward record per customer
    uniqueCustomerReward: uniqueIndex('customer_rewards_customer_unique')
      .on(t.customerId)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Point Transactions table
export const pointTransactions = pgTable(
  'point_transactions',
  {
    ...createBaseEntityBusinessFields(business),
    customerId: uuid('customer_id')
      .notNull()
      .references(() => customers.id, { onDelete: 'cascade' }),
    orderId: uuid('order_id'), // Nullable reference to orders table (when available)
    pointsEarned: integer('points_earned').default(0).notNull(),
    pointsUsed: integer('points_used').default(0).notNull(),
    transactionType: pointTransactionTypeEnum('transaction_type').notNull(),
  },
  (t) => ({
    customerIdIndex: index('point_transactions_customer_id_index').on(
      t.customerId,
    ),
    orderIdIndex: index('point_transactions_order_id_index').on(t.orderId),
    transactionTypeIndex: index('point_transactions_transaction_type_index').on(
      t.transactionType,
    ),
    pointsEarnedIndex: index('point_transactions_points_earned_index').on(
      t.pointsEarned,
    ),
    pointsUsedIndex: index('point_transactions_points_used_index').on(
      t.pointsUsed,
    ),
    createdAtIndex: index('point_transactions_created_at_index').on(
      t.createdAt,
    ),
    // Composite indexes for common query patterns
    customerTransactionTypeIndex: index(
      'point_transactions_customer_type_index',
    )
      .on(t.customerId, t.transactionType, t.createdAt)
      .where(sql`${t.isDeleted} = false`),
    customerDateIndex: index('point_transactions_customer_date_index')
      .on(t.customerId, t.createdAt)
      .where(sql`${t.isDeleted} = false`),
  }),
);
