import {
  boolean,
  decimal,
  index,
  pgEnum,
  pgTable,
  text,
  uniqueIndex,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { business } from './business.schema';
import { LeaveTypeStatus } from '../../shared/types';

export const leaveTypeStatusEnum = pgEnum('leave_type_status', [
  LeaveTypeStatus.ACTIVE,
  LeaveTypeStatus.INACTIVE,
]);

export const leaveTypes = pgTable(
  'leave_types',
  {
    ...createBaseEntityBusinessFields(business),
    leaveCode: text('leave_code').notNull(),
    leaveName: text('leave_name').notNull(),
    daysAllowedPerYear: decimal('days_allowed_per_year', {
      precision: 5,
      scale: 2,
    }).notNull(),
    isPaid: boolean('is_paid').default(true).notNull(),
    carriesForward: boolean('carries_forward').default(false).notNull(),
    description: text('description'),
    status: leaveTypeStatusEnum('status')
      .default(LeaveTypeStatus.ACTIVE)
      .notNull(),
  },
  (t) => ({
    idIndex: index('leave_types_id_index').on(t.id),
    ...createBaseEntityBusinessIndexes(t, 'leave_types'),
    leaveCodeIndex: index('leave_types_leave_code_index').on(t.leaveCode),
    leaveNameIndex: index('leave_types_leave_name_index').on(t.leaveName),
    statusIndex: index('leave_types_status_index').on(t.status),
    uniqueBusinessLeaveCode: uniqueIndex(
      'leave_types_business_leave_code_unique',
    )
      .on(t.businessId, t.leaveCode)
      .where(sql`${t.isDeleted} = false`),
  }),
);
