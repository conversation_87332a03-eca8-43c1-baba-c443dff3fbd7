import {
  index,
  pgTable,
  text,
  timestamp,
  uuid,
  pgEnum,
  uniqueIndex,
  integer,
  jsonb,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { users } from './users.schema';
import { business } from './business.schema';
import { customers } from './customers.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { RestaurantTableStatus } from '../../shared/types';

export const restaurantTableStatusEnum = pgEnum('restaurant_table_status', [
  RestaurantTableStatus.AVAILABLE,
  RestaurantTableStatus.OCCUPIED,
  RestaurantTableStatus.RESERVED,
]);

export const tableShapeTypeEnum = pgEnum('table_shape_type', [
  'square',
  'rectangle',
  'circle',
]);

export const tableReservationStatusEnum = pgEnum('table_reservation_status', [
  'pending',
  'confirmed',
  'seated',
  'completed',
  'cancelled',
  'no_show',
]);

// Floor Plans table
export const floorPlans = pgTable(
  'floor_plans',
  {
    ...createBaseEntityBusinessFields(business),
    name: text('name').notNull(),
    dimensions: jsonb('dimensions').notNull(), // {width: number, height: number}
    kitchen: jsonb('kitchen').notNull(), // {position: {x, y}, size: {width, height}}
    reception: jsonb('reception').notNull(), // {position: {x, y}, size: {width, height}, isVisible: boolean}
  },
  (t) => ({
    idIndex: index('floor_plans_id_index').on(t.id),
    ...createBaseEntityBusinessIndexes(t, 'floor_plans'),
    nameIndex: index('floor_plans_name_index').on(t.name),

    // Optimized composite indexes
    businessNameIndex: index('floor_plans_business_name_index')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessName: uniqueIndex('floor_plans_business_name_unique')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Restaurant Tables table
export const restaurantTables = pgTable(
  'restaurant_tables',
  {
    ...createBaseEntityBusinessFields(business),
    floorId: uuid('floor_id')
      .notNull()
      .references(() => floorPlans.id, { onDelete: 'cascade' }),
    tableNumber: text('table_number').notNull(),
    position: jsonb('position').notNull(), // {x: number, y: number}
    shape: jsonb('shape').notNull(), // {type: 'square'|'rectangle'|'circle', seats: number, size: {width: number, height: number}}
    status: restaurantTableStatusEnum('status')
      .default(RestaurantTableStatus.AVAILABLE)
      .notNull(),
    orders: integer('orders').default(0).notNull(), // Number of active orders
  },
  (t) => ({
    idIndex: index('restaurant_tables_id_index').on(t.id),
    ...createBaseEntityBusinessIndexes(t, 'restaurant_tables'),
    floorIdIndex: index('restaurant_tables_floor_id_index').on(t.floorId),
    tableNumberIndex: index('restaurant_tables_table_number_index').on(
      t.tableNumber,
    ),
    statusIndex: index('restaurant_tables_status_index').on(t.status),

    // Optimized composite indexes for common queries
    businessFloorIndex: index('restaurant_tables_business_floor_index')
      .on(t.businessId, t.floorId)
      .where(sql`${t.isDeleted} = false`),
    businessStatusIndex: index('restaurant_tables_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    floorStatusIndex: index('restaurant_tables_floor_status_index')
      .on(t.floorId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessTableNumberIndex: index(
      'restaurant_tables_business_table_number_index',
    )
      .on(t.businessId, t.tableNumber)
      .where(sql`${t.isDeleted} = false`),
    floorTableNumberIndex: index('restaurant_tables_floor_table_number_index')
      .on(t.floorId, t.tableNumber)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessFloorTableNumber: uniqueIndex(
      'restaurant_tables_business_floor_table_number_unique',
    )
      .on(t.businessId, t.floorId, t.tableNumber)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Table Reservations table
export const tableReservations = pgTable(
  'table_reservations',
  {
    id: integer('id').primaryKey().generatedAlwaysAsIdentity(),
    customerId: uuid('customer_id').references(() => customers.id),
    tableId: uuid('table_id')
      .notNull()
      .references(() => restaurantTables.id, { onDelete: 'cascade' }),
    reservationDatetime: timestamp('reservation_datetime').notNull(),
    partySize: integer('party_size').notNull(),
    status: tableReservationStatusEnum('status').default('pending').notNull(),
    notes: text('notes'),
    // Standard audit fields
    ...createBaseEntityBusinessFields(business),
  },
  (t) => ({
    idIndex: index('table_reservations_id_index').on(t.id),
    customerIdIndex: index('table_reservations_customer_id_index').on(
      t.customerId,
    ),
    tableIdIndex: index('table_reservations_table_id_index').on(t.tableId),
    reservationDatetimeIndex: index(
      'table_reservations_reservation_datetime_index',
    ).on(t.reservationDatetime),
    statusIndex: index('table_reservations_status_index').on(t.status),
    partySizeIndex: index('table_reservations_party_size_index').on(
      t.partySize,
    ),

    // Optimized composite indexes for common queries
    tableDateIndex: index('table_reservations_table_date_index')
      .on(t.tableId, t.reservationDatetime)
      .where(sql`${t.isDeleted} = false`),
    tableStatusIndex: index('table_reservations_table_status_index')
      .on(t.tableId, t.status)
      .where(sql`${t.isDeleted} = false`),
    customerStatusIndex: index('table_reservations_customer_status_index')
      .on(t.customerId, t.status)
      .where(sql`${t.isDeleted} = false`),
    statusDateIndex: index('table_reservations_status_date_index')
      .on(t.status, t.reservationDatetime)
      .where(sql`${t.isDeleted} = false`),
  }),
);
