import {
  boolean,
  index,
  integer,
  pgEnum,
  pgTable,
  text,
  uniqueIndex,
  uuid,
  varchar,
} from 'drizzle-orm/pg-core';
import { business } from './business.schema';
import {
  auditFields,
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { WorkOrderStatusType } from '../../shared/types/work-order.enum';

export const workOrderStatusTypeEnum = pgEnum('work_order_status_type', [
  WorkOrderStatusType.INITIAL,
  WorkOrderStatusType.PLANNING,
  WorkOrderStatusType.IN_PROGRESS,
  WorkOrderStatusType.FINAL,
  WorkOrderStatusType.CANCELLED,
]);

/**
 * Work Order Statuses Schema
 * Defines configurable status levels for work orders with workflow properties
 */
export const workOrderStatuses = pgTable(
  'work_order_statuses',
  {
    ...createBaseEntityBusinessFields(business),
    statusCode: varchar('status_code', { length: 50 }).unique().notNull(),
    statusName: varchar('status_name', { length: 100 }).notNull(),
    description: text('description'),
    colorCode: varchar('color_code', { length: 7 }),
    iconName: varchar('icon_name', { length: 50 }),
    statusType: workOrderStatusTypeEnum('status_type').notNull(),
    isActive: boolean('is_active').default(true).notNull(),
    isDefault: boolean('is_default').default(false).notNull(),
    position: integer('position').default(0).notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'work_order_statuses'),
    statusCodeIndex: index('work_order_statuses_status_code_index').on(
      t.statusCode,
    ),
    statusTypeIndex: index('work_order_statuses_status_type_index').on(
      t.statusType,
    ),
    positionIndex: index('work_order_statuses_position_index').on(t.position),
    isActiveIndex: index('work_order_statuses_is_active_index').on(t.isActive),
    isDefaultIndex: index('work_order_statuses_is_default_index').on(
      t.isDefault,
    ),

    // Composite indexes for common query patterns
    businessActiveIndex: index('work_order_statuses_business_active_index').on(
      t.businessId,
      t.isActive,
    ),
    businessDisplayOrderIndex: index(
      'work_order_statuses_business_position_index',
    ).on(t.businessId, t.position, t.id),
    businessStatusTypeIndex: index(
      'work_order_statuses_business_status_type_index',
    ).on(t.businessId, t.statusType, t.id),

    // Unique constraints
    uniqueBusinessStatusCode: uniqueIndex(
      'work_order_statuses_business_status_code_unique',
    ).on(t.businessId, t.statusCode),
    uniqueBusinessStatusName: uniqueIndex(
      'work_order_statuses_business_status_name_unique',
    ).on(t.businessId, t.statusName),
    uniqueBusinessDisplayOrder: uniqueIndex(
      'work_order_statuses_business_position_unique',
    ).on(t.businessId, t.position),
  }),
);
