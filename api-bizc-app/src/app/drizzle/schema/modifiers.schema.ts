import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  boolean,
  decimal,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import { modifierGroups } from './modifier-groups.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { AdjustmentType, ModifierStatus } from '../../shared/types';

export const adjustmentTypeEnum = pgEnum('adjustment_type', [
  AdjustmentType.VALUE,
  AdjustmentType.PERCENTAGE,
]);

export const modifierStatusEnum = pgEnum('modifier_status', [
  ModifierStatus.ACTIVE,
  ModifierStatus.INACTIVE,
]);

export const modifiers = pgTable(
  'modifiers',
  {
    ...createBaseEntityBusinessFields(business),
    modifierGroupId: uuid('modifier_group_id')
      .notNull()
      .references(() => modifierGroups.id, { onDelete: 'cascade' }),
    modifierName: text('modifier_name').notNull(),
    description: text('description'),
    priceAdjustment: decimal('price_adjustment', { precision: 10, scale: 2 })
      .default('0.00')
      .notNull(),
    cost: decimal('cost', { precision: 10, scale: 2 })
      .default('0.00')
      .notNull(),
    adjustmentType: adjustmentTypeEnum('adjustment_type')
      .default(AdjustmentType.VALUE)
      .notNull(),
    isDefault: boolean('is_default').default(false).notNull(),
    status: modifierStatusEnum('status')
      .default(ModifierStatus.ACTIVE)
      .notNull(),
  },
  (t) => ({
    idIndex: index('modifiers_id_index').on(t.id),
    ...createBaseEntityBusinessIndexes(t, 'modifiers'),
    modifierGroupIdIndex: index('modifiers_modifier_group_id_index').on(
      t.modifierGroupId,
    ),
    modifierNameIndex: index('modifiers_modifier_name_index').on(
      t.modifierName,
    ),

    // Optimized indexes for filtering and searching
    businessModifierNameIndex: index('modifiers_business_modifier_name_index')
      .on(t.businessId, t.modifierName)
      .where(sql`${t.isDeleted} = false`),
    groupModifierNameIndex: index('modifiers_group_modifier_name_index')
      .on(t.modifierGroupId, t.modifierName)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueGroupModifierName: uniqueIndex('modifiers_group_modifier_name_unique')
      .on(t.modifierGroupId, t.modifierName)
      .where(sql`${t.isDeleted} = false`),
  }),
);

export type Modifier = typeof modifiers.$inferSelect;
export type NewModifier = typeof modifiers.$inferInsert;
