import {
  timestamp,
  pgTable,
  text,
  uuid,
  boolean,
  doublePrecision,
  primaryKey,
  decimal,
  integer,
  pgEnum,
  index,
} from 'drizzle-orm/pg-core';

import { business } from './business.schema';
import { users } from './users.schema';
import { createBaseEntityBusinessFields } from './common-fields.schema';
import {
  TaxStatus,
  TaxPeriodStartMonth,
  TaxFilingFrequency,
  TaxApplicableOn,
  TaxReportingMethod,
} from '../../shared/types';

// Define pgEnum types using the shared enum string values
export const taxStatusEnum = pgEnum('tax_status', [
  TaxStatus.ACTIVE,
  TaxStatus.INACTIVE,
  TaxStatus.DELETED,
]);

export const taxPeriodStartMonthEnum = pgEnum('tax_period_start_month', [
  TaxPeriodStartMonth.JANUARY,
  TaxPeriodStartMonth.FEBRUARY,
  TaxPeriodStartMonth.MARCH,
  TaxPeriodStartMonth.APRIL,
  TaxPeriodStartMonth.MAY,
  TaxPeriodStartMonth.JUNE,
  TaxPeriodStartMonth.JULY,
  TaxPeriodStartMonth.AUGUST,
  TaxPeriodStartMonth.SEPTEMBER,
  TaxPeriodStartMonth.OCTOBER,
  TaxPeriodStartMonth.NOVEMBER,
  TaxPeriodStartMonth.DECEMBER,
]);

export const taxFilingFrequencyEnum = pgEnum('tax_filing_frequency', [
  TaxFilingFrequency.WEEKLY,
  TaxFilingFrequency.BIWEEKLY,
  TaxFilingFrequency.MONTHLY,
  TaxFilingFrequency.QUARTERLY,
  TaxFilingFrequency.SEMI_ANNUALLY,
  TaxFilingFrequency.ANNUALLY,
]);

export const taxApplicableOnEnum = pgEnum('tax_applicable_on', [
  TaxApplicableOn.NET_AMOUNT,
  TaxApplicableOn.TAX_AMOUNT,
  TaxApplicableOn.NET_PLUS_TAX_AMOUNT,
]);

export const taxReportingMethodEnum = pgEnum('tax_reporting_method', [
  TaxReportingMethod.CASH_BASIS,
  TaxReportingMethod.ACCRUAL_BASIS,
  TaxReportingMethod.HYBRID,
  TaxReportingMethod.MODIFIED_CASH,
]);

export const taxes = pgTable(
  'taxes',
  {
    ...createBaseEntityBusinessFields(business),

    // Basic tax information
    taxName: text('tax_name').notNull(),
    description: text('description'),
    isGroup: boolean('is_group').default(false).notNull(),

    // Individual tax fields (only applicable when isGroup = false)
    taxAgencyName: text('tax_agency_name'),
    businessIdNo: text('business_id_no'),

    // Tax period and filing information (only applicable for individual taxes)
    startOfCurrentTaxPeriod: taxPeriodStartMonthEnum(
      'start_of_current_tax_period',
    ),
    filingFrequency: taxFilingFrequencyEnum('filing_frequency'),
    reportingMethod: taxReportingMethodEnum('reporting_method'),

    // Tax collection settings
    isCollectedOnSales: boolean('is_collected_on_sales')
      .default(false)
      .notNull(),
    salesRate: decimal('sales_rate', { precision: 5, scale: 4 }), // e.g., 0.1250 for 12.5%
    isCollectedOnPurchases: boolean('is_collected_on_purchases')
      .default(false)
      .notNull(),
    purchaseRate: decimal('purchase_rate', { precision: 5, scale: 4 }), // e.g., 0.1250 for 12.5%
    isPurchaseTaxReclaimable: boolean('is_purchase_tax_reclaimable')
      .default(false)
      .notNull(),

    // Positioning for ordering
    position: integer('position').default(0).notNull(),

    // Status
    status: taxStatusEnum('status').default(TaxStatus.ACTIVE).notNull(),
  },
  (t) => ({
    businessIdIndex: index('taxes_business_id_index').on(t.businessId),
    taxNameIndex: index('taxes_tax_name_index').on(t.taxName),
    statusIndex: index('taxes_status_index').on(t.status),
    positionIndex: index('taxes_position_index').on(t.position),
    // Unique constraint for tax name within a business (excluding soft deleted)
    uniqueTaxNamePerBusiness: index('taxes_unique_name_per_business').on(
      t.businessId,
      t.taxName,
    ),
  }),
);

// Keep the existing taxRates table for backward compatibility
export const taxRates = pgTable('tax_rates', {
  ...createBaseEntityBusinessFields(business),
  name: text('name').notNull(),
  amount: doublePrecision('amount').notNull(),
  isTaxGroup: boolean('is_tax_group').notNull().default(false),
});

// Updated groupSubTaxes table for new tax groups functionality
export const taxGroupItems = pgTable(
  'tax_group_items',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    groupTaxId: uuid('group_tax_id')
      .notNull()
      .references(() => taxes.id),
    individualTaxId: uuid('individual_tax_id')
      .notNull()
      .references(() => taxes.id),
    applicableOn: taxApplicableOnEnum('applicable_on')
      .default(TaxApplicableOn.NET_AMOUNT)
      .notNull(),
    orderIndex: integer('order_index').notNull(),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (t) => ({
    groupTaxIdIndex: index('tax_group_items_group_tax_id_index').on(
      t.groupTaxId,
    ),
    individualTaxIdIndex: index('tax_group_items_individual_tax_id_index').on(
      t.individualTaxId,
    ),
    orderIndex: index('tax_group_items_order_index').on(t.orderIndex),
    // Unique constraint to prevent duplicate taxes in the same group
    uniqueTaxInGroup: index('tax_group_items_unique_tax_in_group').on(
      t.groupTaxId,
      t.individualTaxId,
    ),
  }),
);

// Keep the existing groupSubTaxes table for backward compatibility with taxRates
export const groupSubTaxes = pgTable(
  'group_sub_taxes',
  {
    groupTaxId: uuid('group_tax_id')
      .notNull()
      .references(() => taxRates.id),
    taxId: uuid('tax_id')
      .notNull()
      .references(() => taxRates.id),
  },
  (table) => {
    return {
      pk: primaryKey({ columns: [table.groupTaxId, table.taxId] }),
    };
  },
);
