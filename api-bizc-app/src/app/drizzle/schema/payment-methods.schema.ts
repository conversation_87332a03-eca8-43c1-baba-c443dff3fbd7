import {
  index,
  pgTable,
  text,
  pgEnum,
  uniqueIndex,
  boolean,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import { PaymentMethodStatus } from '../../shared/types';
import { createBaseEntityBusinessFields } from './common-fields.schema';

export const paymentMethodStatusEnum = pgEnum('payment_method_status', [
  PaymentMethodStatus.ACTIVE,
  PaymentMethodStatus.INACTIVE,
]);

export const paymentMethods = pgTable(
  'payment_methods',
  {
    ...createBaseEntityBusinessFields(business),
    name: text('name').notNull(),
    isCreditOrDebitCard: boolean('is_credit_or_debit_card')
      .default(false)
      .notNull(),

    status: paymentMethodStatusEnum('status')
      .default(PaymentMethodStatus.ACTIVE)
      .notNull(),
  },
  (t) => ({
    idIndex: index('payment_methods_id_index').on(t.id),
    businessIdIndex: index('payment_methods_business_id_index').on(
      t.businessId,
    ),
    nameIndex: index('payment_methods_name_index').on(t.name),
    statusIndex: index('payment_methods_status_index').on(t.status),
    createdAtIndex: index('payment_methods_created_at_index').on(t.createdAt),
    updatedAtIndex: index('payment_methods_updated_at_index').on(t.updatedAt),
    createdByIndex: index('payment_methods_created_by_index').on(t.createdBy),
    updatedByIndex: index('payment_methods_updated_by_index').on(t.updatedBy),

    // Unique constraints with soft deletion support
    uniqueBusinessName: uniqueIndex('payment_methods_business_name_unique')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
  }),
);
