import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  decimal,
  date,
  timestamp,
} from 'drizzle-orm/pg-core';
import { business } from './business.schema';
import { assets } from './assets.schema';
import { accounts } from './accounts.schema';
import { paymentMethods } from './payment-methods.schema';
import { taxes } from './taxes.schema';
import { suppliers } from './suppliers.schema';
import { staffMembers } from './staff.schema';
import { MaintenanceStatus, MaintenancePriority } from '../../shared/types';
import { amountTypeEnum, AmountType } from './expenses.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

// Maintenance Status Enum
export const maintenanceStatusEnum = pgEnum('maintenance_status', [
  MaintenanceStatus.PENDING,
  MaintenanceStatus.IN_PROGRESS,
  MaintenanceStatus.COMPLETED,
  MaintenanceStatus.CANCELLED,
]);

// Maintenance Priority Enum
export const maintenancePriorityEnum = pgEnum('maintenance_priority', [
  MaintenancePriority.LOW,
  MaintenancePriority.NORMAL,
  MaintenancePriority.HIGH,
  MaintenancePriority.URGENT,
  MaintenancePriority.EMERGENCY,
]);

export const assetMaintenances = pgTable(
  'asset_maintenances',
  {
    ...createBaseEntityBusinessFields(business),
    assetId: uuid('asset_id')
      .notNull()
      .references(() => assets.id, { onDelete: 'cascade' }),

    // Work Order Information
    workOrderNumber: text('work_order_number'),
    title: text('title').notNull(),
    description: text('description'),
    instructions: text('instructions'),

    // Status and Prioritysd
    status: maintenanceStatusEnum('status').default(MaintenanceStatus.PENDING),
    priority: maintenancePriorityEnum('priority').default(
      MaintenancePriority.NORMAL,
    ),

    // Scheduling and Timeline
    targetCompletionDate: timestamp('target_completion_date'),
    actualStartDate: timestamp('actual_start_date'),

    // Supplier and Staff Assignment
    supplierId: uuid('supplier_id').references(() => suppliers.id),
    assignedTo: uuid('assigned_to').references(() => staffMembers.id),
    qualityCheckBy: uuid('quality_check_by').references(() => staffMembers.id),

    // Completion Information
    completedAt: timestamp('completed_at'),
    completionNotes: text('completion_notes'),

    // Payment and Financial Information
    expenseAccountId: uuid('expense_account_id')
      .notNull()
      .references(() => accounts.id),
    paymentAccountId: uuid('payment_account_id')
      .notNull()
      .references(() => accounts.id),
    paymentDate: date('payment_date').notNull(),
    paymentMethodId: uuid('payment_method_id')
      .notNull()
      .references(() => paymentMethods.id),
    paymentReferenceNumber: text('payment_reference_number'),

    // Amount calculation
    amountType: amountTypeEnum('amount_type')
      .default(AmountType.EXCLUSIVE_OF_TAX)
      .notNull(),

    // Totals
    subtotal: decimal('subtotal', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    total: decimal('total', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    taxId: uuid('tax_id').references(() => taxes.id),
  },
  (t) => ({
    // Standard base entity indexes
    ...createBaseEntityBusinessIndexes(t, 'asset_maintenances'),

    // Entity-specific indexes
    assetIdIndex: index('asset_maintenances_asset_id_index').on(t.assetId),
    workOrderNumberIndex: index(
      'asset_maintenances_work_order_number_index',
    ).on(t.workOrderNumber),
    titleIndex: index('asset_maintenances_title_index').on(t.title),
    statusIndex: index('asset_maintenances_status_index').on(t.status),
    priorityIndex: index('asset_maintenances_priority_index').on(t.priority),
    targetCompletionDateIndex: index(
      'asset_maintenances_target_completion_date_index',
    ).on(t.targetCompletionDate),
    actualStartDateIndex: index(
      'asset_maintenances_actual_start_date_index',
    ).on(t.actualStartDate),
    supplierIdIndex: index('asset_maintenances_supplier_id_index').on(
      t.supplierId,
    ),
    assignedToIndex: index('asset_maintenances_assigned_to_index').on(
      t.assignedTo,
    ),
    qualityCheckByIndex: index('asset_maintenances_quality_check_by_index').on(
      t.qualityCheckBy,
    ),
    completedAtIndex: index('asset_maintenances_completed_at_index').on(
      t.completedAt,
    ),
    expenseAccountIdIndex: index(
      'asset_maintenances_expense_account_id_index',
    ).on(t.expenseAccountId),
    paymentAccountIdIndex: index(
      'asset_maintenances_payment_account_id_index',
    ).on(t.paymentAccountId),
    paymentDateIndex: index('asset_maintenances_payment_date_index').on(
      t.paymentDate,
    ),
    paymentMethodIdIndex: index(
      'asset_maintenances_payment_method_id_index',
    ).on(t.paymentMethodId),
    taxIdIndex: index('asset_maintenances_tax_id_index').on(t.taxId),
  }),
);
