import { index, pgTable, text, timestamp, bigint } from 'drizzle-orm/pg-core';
import { business } from './business.schema';
import { createBaseEntityBusinessFields } from './common-fields.schema';

export const notifications = pgTable(
  'notifications',
  {
    ...createBaseEntityBusinessFields(business),
    type: text('type').notNull(),
    notifiableType: text('notifiable_type').notNull(),
    notifiableId: bigint('notifiable_id', { mode: 'number' }).notNull(),
    data: text('data').notNull(),
    readAt: timestamp('read_at'),
  },
  (t) => ({
    idIndex: index('notifications_id_index').on(t.id),
    businessIdIndex: index('notifications_business_id_index').on(t.businessId),
    typeIndex: index('notifications_type_index').on(t.type),
    notifiableIndex: index('notifications_notifiable_index').on(
      t.notifiableType,
      t.notifiableId,
    ),
    readAtIndex: index('notifications_read_at_index').on(t.readAt),
    createdAtIndex: index('notifications_created_at_index').on(t.createdAt),
  }),
);
