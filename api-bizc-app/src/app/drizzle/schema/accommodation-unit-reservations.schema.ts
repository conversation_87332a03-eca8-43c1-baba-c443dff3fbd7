import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  timestamp,
  integer,
  decimal,
  boolean,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import { accommodationUnits } from './accommodation-units.schema';
import { guests, guestRelationshipEnum } from './guests.schema';
import { taxes } from './taxes.schema';
import {
  auditFields,
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { paymentStatusEnum } from './common-fields.schema';
import {
  AccommodationReservationStatus,
  ReservationSource,
  MealPeriod,
} from '../../shared/types/accommodation.enum';
import {
  PaymentStatus,
  TaxType,
  DiscountType,
} from '../../shared/types/common.enum';

// Accommodation Unit Reservation Status Enum
export const accommodationReservationStatusEnum = pgEnum(
  'accommodation_reservation_status',
  [
    AccommodationReservationStatus.INQUIRY,
    AccommodationReservationStatus.PENDING,
    AccommodationReservationStatus.CONFIRMED,
    AccommodationReservationStatus.CHECKED_IN,
    AccommodationReservationStatus.CHECKED_OUT,
    AccommodationReservationStatus.CANCELLED,
    AccommodationReservationStatus.NO_SHOW,
    AccommodationReservationStatus.BLOCKED,
  ],
);

// Payment Status Enum is imported from common-fields.schema.ts

// Reservation Source Enum
export const reservationSourceEnum = pgEnum('reservation_source', [
  ReservationSource.ONLINE,
  ReservationSource.PHONE,
  ReservationSource.WALK_IN,
  ReservationSource.EMAIL,
  ReservationSource.AGENT,
  ReservationSource.CORPORATE,
  ReservationSource.REPEAT_GUEST,
  ReservationSource.REFERRAL,
  ReservationSource.OTHER,
]);

// Tax Type Enum
export const taxTypeEnum = pgEnum('tax_type', [
  TaxType.INCLUSIVE,
  TaxType.EXCLUSIVE,
  TaxType.OUT_OF_SCOPE,
]);

// Discount Type Enum
export const discountTypeEnum = pgEnum('discount_type', [
  DiscountType.PERCENTAGE,
  DiscountType.FIXED_AMOUNT,
]);

// Meal Period Enum
export const mealPeriodEnum = pgEnum('meal_period', [
  MealPeriod.ROOM_ONLY,
  MealPeriod.BED_AND_BREAKFAST,
  MealPeriod.HALF_BOARD,
  MealPeriod.FULL_BOARD,
  MealPeriod.ALL_INCLUSIVE,
  MealPeriod.CONTINENTAL_BREAKFAST,
  MealPeriod.AMERICAN_BREAKFAST,
  MealPeriod.LUNCH_ONLY,
  MealPeriod.DINNER_ONLY,
  MealPeriod.CUSTOM,
]);

// Accommodation Reservations table
export const accommodationReservations = pgTable(
  'accommodation_reservations',
  {
    ...createBaseEntityBusinessFields(business),
    // Reservation identification
    reservationNumber: text('reservation_number').notNull(),
    referenceNumber: text('reference_number'), // External booking reference

    // Reservation dates and occupancy (aggregated across all units)
    checkInDate: timestamp('check_in_date').notNull(),
    checkOutDate: timestamp('check_out_date').notNull(),
    // Reservation status and type
    status: accommodationReservationStatusEnum('status')
      .default(AccommodationReservationStatus.PENDING)
      .notNull(),
    reservationSource: reservationSourceEnum('reservation_source'),
    // Payment information
    paymentStatus: paymentStatusEnum('payment_status')
      .default(PaymentStatus.PENDING)
      .notNull(),
    subtotal: decimal('subtotal', { precision: 12, scale: 2 })
      .default('0.00')
      .notNull(),
    total: decimal('total', { precision: 12, scale: 2 })
      .default('0.00')
      .notNull(),
    depositPaid: decimal('deposit_paid', { precision: 12, scale: 2 }).default(
      '0.00',
    ),
    balanceDue: decimal('balance_due', { precision: 12, scale: 2 }).default(
      '0.00',
    ),
    // Discount information
    discountType: discountTypeEnum('discount_type'),
    discountValue: decimal('discount_value', {
      precision: 12,
      scale: 2,
    }).default('0.00'),
    discountAmount: decimal('discount_amount', {
      precision: 12,
      scale: 2,
    }).default('0.00'),
    discountReason: text('discount_reason'),
    // Tax information
    taxType: taxTypeEnum('tax_type').default(TaxType.INCLUSIVE).notNull(),

    // Meal period information
    mealPeriod: mealPeriodEnum('meal_period')
      .default(MealPeriod.ROOM_ONLY)
      .notNull(),
    mealPeriodCost: decimal('meal_period_cost', {
      precision: 12,
      scale: 2,
    }).default('0.00'),
    mealPeriodNotes: text('meal_period_notes'),
    customMealDetails: text('custom_meal_details'), // For CUSTOM meal period type

    // Reservation metadata
    notes: text('notes'),
    cancellationReason: text('cancellation_reason'),
    cancellationDate: timestamp('cancellation_date'),
    // Confirmation and communication
    confirmationSent: boolean('confirmation_sent').default(false).notNull(),
    confirmationSentAt: timestamp('confirmation_sent_at'),
    reminderSent: boolean('reminder_sent').default(false).notNull(),
    reminderSentAt: timestamp('reminder_sent_at'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'accommodation_reservations'),
    // Primary indexes
    businessIdIndex: index('accommodation_reservations_business_id_index').on(
      t.businessId,
    ),
    reservationNumberIndex: index('accommodation_reservations_number_index').on(
      t.reservationNumber,
    ),
    referenceNumberIndex: index(
      'accommodation_reservations_reference_index',
    ).on(t.referenceNumber),

    // Date and time indexes for availability queries
    checkInDateIndex: index(
      'accommodation_reservations_check_in_date_index',
    ).on(t.checkInDate),
    checkOutDateIndex: index(
      'accommodation_reservations_check_out_date_index',
    ).on(t.checkOutDate),

    // Status and type indexes
    statusIndex: index('accommodation_reservations_status_index').on(t.status),
    paymentStatusIndex: index(
      'accommodation_reservations_payment_status_index',
    ).on(t.paymentStatus),
    reservationSourceIndex: index('accommodation_reservations_source_index').on(
      t.reservationSource,
    ),

    confirmationSentIndex: index(
      'accommodation_reservations_confirmation_sent_index',
    ).on(t.confirmationSent),

    // Meal period indexes
    mealPeriodIndex: index('accommodation_reservations_meal_period_index').on(
      t.mealPeriod,
    ),

    // Composite indexes for common queries
    businessStatusIndex: index(
      'accommodation_reservations_business_status_index',
    ).on(t.businessId, t.status),
    businessDateRangeIndex: index(
      'accommodation_reservations_business_date_range_index',
    ).on(t.businessId, t.checkInDate, t.checkOutDate),
    paymentDueIndex: index('accommodation_reservations_payment_due_index').on(
      t.businessId,
      t.paymentStatus,
      t.checkInDate,
    ),

    // Unique constraints with soft deletion support
    uniqueBusinessReservationNumber: uniqueIndex(
      'accommodation_reservations_business_number_unique',
    )
      .on(t.businessId, t.reservationNumber)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessReferenceNumber: uniqueIndex(
      'accommodation_reservations_business_reference_unique',
    )
      .on(t.businessId, t.referenceNumber)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Junction table for reservation-accommodation unit relationships (many-to-many)
export const accommodationReservationUnits = pgTable(
  'accommodation_reservation_units',
  {
    ...createBaseEntityBusinessFields(business),
    reservationId: uuid('reservation_id')
      .notNull()
      .references(() => accommodationReservations.id, {
        onDelete: 'cascade',
      }),
    accommodationUnitId: uuid('accommodation_unit_id')
      .notNull()
      .references(() => accommodationUnits.id, {
        onDelete: 'cascade',
      }),

    // Unit-specific reservation details
    unitRoomRate: decimal('unit_room_rate', {
      precision: 12,
      scale: 2,
    }).notNull(),
    unitTotalCost: decimal('unit_total_cost', {
      precision: 12,
      scale: 2,
    }).notNull(),
    // Unit-specific occupancy
    unitNumberOfAdults: integer('unit_number_of_adults').notNull(),
    unitNumberOfChildren: integer('unit_number_of_children')
      .default(0)
      .notNull(),

    // Unit-specific status and times
    unitStatus: accommodationReservationStatusEnum('unit_status')
      .default(AccommodationReservationStatus.PENDING)
      .notNull(),

    // Unit-specific notes
    notes: text('notes'), // Reservation-specific notes about this unit
    // Unit order in multi-unit reservation (for primary unit designation)
    unitOrder: integer('unit_order').default(1).notNull(),
    // Unit pricing totals
    unitSubtotal: decimal('unit_subtotal', { precision: 12, scale: 2 })
      .default('0.00')
      .notNull(),
    unitTotal: decimal('unit_total', { precision: 12, scale: 2 })
      .default('0.00')
      .notNull(),
    // Unit discount information
    unitDiscountType: discountTypeEnum('unit_discount_type'),
    unitDiscountValue: decimal('unit_discount_value', {
      precision: 12,
      scale: 2,
    }).default('0.00'),
    unitDiscountAmount: decimal('unit_discount_amount', {
      precision: 12,
      scale: 2,
    }).default('0.00'),
    // Unit tax information
    unitTaxRateId: uuid('unit_tax_rate_id').references(() => taxes.id),
    unitTaxAmount: decimal('unit_tax_amount', {
      precision: 12,
      scale: 2,
    }).default('0.00'),

    // Unit-specific meal period information (can override reservation-level meal period)
    unitMealPeriod: mealPeriodEnum('unit_meal_period'), // If null, inherits from reservation
    unitMealPeriodCost: decimal('unit_meal_period_cost', {
      precision: 12,
      scale: 2,
    }).default('0.00'),
    unitMealPeriodNotes: text('unit_meal_period_notes'),
    unitCustomMealDetails: text('unit_custom_meal_details'), // For CUSTOM meal period type
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'accommodation_reservation_units'),
    // Primary indexes
    reservationIdIndex: index(
      'accommodation_reservation_units_reservation_id_index',
    ).on(t.reservationId),
    accommodationUnitIdIndex: index(
      'accommodation_reservation_units_unit_id_index',
    ).on(t.accommodationUnitId),

    // Status and role indexes
    unitStatusIndex: index(
      'accommodation_reservation_units_unit_status_index',
    ).on(t.unitStatus),
    unitOrderIndex: index(
      'accommodation_reservation_units_unit_order_index',
    ).on(t.unitOrder),

    // Unit tax indexes
    unitTaxRateIdIndex: index(
      'accommodation_reservation_units_tax_rate_index',
    ).on(t.unitTaxRateId),

    // Unit meal period indexes
    unitMealPeriodIndex: index(
      'accommodation_reservation_units_meal_period_index',
    ).on(t.unitMealPeriod),

    // Composite indexes for common queries
    reservationUnitOrderIndex: index(
      'accommodation_reservation_units_reservation_order_index',
    ).on(t.reservationId, t.unitOrder),
    unitReservationStatusIndex: index(
      'accommodation_reservation_units_unit_reservation_status_index',
    ).on(t.accommodationUnitId, t.unitStatus),
    // Availability check for individual units
    unitDateOverlapIndex: index(
      'accommodation_reservation_units_unit_overlap_check',
    )
      .on(t.accommodationUnitId, t.unitStatus)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints
    uniqueReservationUnit: uniqueIndex(
      'accommodation_reservation_units_unique',
    ).on(t.reservationId, t.accommodationUnitId),
  }),
);

// Junction table for reservation-guest relationships (many-to-many)
export const accommodationReservationGuests = pgTable(
  'accommodation_reservation_guests',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    reservationUnitId: uuid('reservation_unit_id')
      .notNull()
      .references(() => accommodationReservationUnits.id, {
        onDelete: 'cascade',
      }),
    guestId: uuid('guest_id')
      .notNull()
      .references(() => guests.id, {
        onDelete: 'cascade',
      }),

    // Guest role in this specific reservation
    isPrimaryGuest: boolean('is_primary_guest').default(false).notNull(),
    primaryGuestId: uuid('primary_guest_id').references(() => guests.id), // Reference to the primary guest in this reservation
    relationshipToPrimary: guestRelationshipEnum('relationship_to_primary'), // Relationship to primary guest

    // Reservation-specific guest information
    checkInStatus: text('check_in_status').default('PENDING').notNull(), // PENDING, CHECKED_IN, CHECKED_OUT
    checkInTime: timestamp('check_in_time'),
    checkOutTime: timestamp('check_out_time'),

    notes: text('notes'), // Reservation-specific notes about this guest

    // Standard audit fields
    ...auditFields,
  },
  (t) => ({
    // Primary indexes
    reservationUnitIdIndex: index(
      'accommodation_reservation_guests_reservation_unit_id_index',
    ).on(t.reservationUnitId),
    guestIdIndex: index('accommodation_reservation_guests_guest_id_index').on(
      t.guestId,
    ),

    // Status and role indexes
    isPrimaryGuestIndex: index(
      'accommodation_reservation_guests_is_primary_guest_index',
    ).on(t.isPrimaryGuest),
    primaryGuestIdIndex: index(
      'accommodation_reservation_guests_primary_guest_id_index',
    ).on(t.primaryGuestId),
    relationshipToPrimaryIndex: index(
      'accommodation_reservation_guests_relationship_to_primary_index',
    ).on(t.relationshipToPrimary),
    checkInStatusIndex: index(
      'accommodation_reservation_guests_check_in_status_index',
    ).on(t.checkInStatus),

    // Time-based indexes
    checkInTimeIndex: index(
      'accommodation_reservation_guests_check_in_time_index',
    ).on(t.checkInTime),
    checkOutTimeIndex: index(
      'accommodation_reservation_guests_check_out_time_index',
    ).on(t.checkOutTime),

    // Composite indexes for common queries
    reservationUnitPrimaryGuestIndex: index(
      'accommodation_reservation_guests_unit_primary_index',
    ).on(t.reservationUnitId, t.isPrimaryGuest),
    reservationUnitGuestRelationshipIndex: index(
      'accommodation_reservation_guests_unit_relationship_index',
    ).on(t.reservationUnitId, t.relationshipToPrimary),
    primaryGuestCompanionsIndex: index(
      'accommodation_reservation_guests_primary_companions_index',
    ).on(t.primaryGuestId, t.relationshipToPrimary),
    guestReservationStatusIndex: index(
      'accommodation_reservation_guests_guest_status_index',
    ).on(t.guestId, t.checkInStatus),

    // Unique constraints
    uniqueReservationUnitGuest: uniqueIndex(
      'accommodation_reservation_guests_unique',
    ).on(t.reservationUnitId, t.guestId),

    // Ensure only one primary guest per reservation unit
    uniquePrimaryGuestPerReservationUnit: uniqueIndex(
      'accommodation_reservation_guests_unique_primary',
    )
      .on(t.reservationUnitId)
      .where(sql`${t.isDeleted} = false`),
  }),
);
