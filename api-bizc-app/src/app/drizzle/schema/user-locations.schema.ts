import { index, pgEnum, pgTable, uuid } from 'drizzle-orm/pg-core';
import { business } from './business.schema';
import { users } from './users.schema';
import { locations } from './locations.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

// Status enum for user location assignments
export const userLocationStatusEnum = pgEnum('user_location_status', [
  'active',
  'inactive',
]);

export const userLocations = pgTable(
  'user_locations',
  {
    ...createBaseEntityBusinessFields(business),
    userId: uuid('user_id')
      .notNull()
      .references(() => users.id),
    locationId: uuid('location_id')
      .notNull()
      .references(() => locations.id),
    status: userLocationStatusEnum('status').default('active').notNull(),
  },
  (t) => ({
    // Standard indexes for base entity fields
    ...createBaseEntityBusinessIndexes(t, 'user_locations'),

    // Entity-specific indexes
    userIdIndex: index('user_locations_user_id_index').on(t.userId),
    locationIdIndex: index('user_locations_location_id_index').on(t.locationId),
    statusIndex: index('user_locations_status_index').on(t.status),

    // Composite indexes for performance
    businessUserIndex: index('user_locations_business_user_index').on(
      t.businessId,
      t.userId,
    ),
    businessLocationIndex: index('user_locations_business_location_index').on(
      t.businessId,
      t.locationId,
    ),
    userLocationIndex: index('user_locations_user_location_index').on(
      t.userId,
      t.locationId,
    ),
  }),
);
