import { pgTable, jsonb, uniqueIndex } from 'drizzle-orm/pg-core';
import { business } from './business.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

/**
 * Working Hours Schema
 *
 * Stores business working hours configuration with weekly schedule in JSONB format.
 * Each business can have one working hours configuration.
 *
 * Weekly schedule format:
 * {
 *   "monday": {
 *     "isWorkingDay": true,
 *     "workingHours": {
 *       "start": "09:00",
 *       "end": "17:00"
 *     },
 *     "breaks": [
 *       {
 *         "name": "Lunch Break",
 *         "start": "12:00",
 *         "end": "13:00",
 *         "isPaid": false
 *       }
 *     ],
 *     "totalWorkingHours": 7.0
 *   },
 *   // ... other days
 * }
 */

// TypeScript interfaces for the JSONB structure
export interface WorkingHoursBreak {
  name: string;
  start: string; // HH:MM format
  end: string; // HH:MM format
  isPaid: boolean;
}

export interface WorkingHoursDay {
  isWorkingDay: boolean;
  workingHours?: {
    start: string; // HH:MM format
    end: string; // HH:MM format
  };
  breaks: WorkingHoursBreak[];
  totalWorkingHours: number;
}

export interface WeeklySchedule {
  monday: WorkingHoursDay;
  tuesday: WorkingHoursDay;
  wednesday: WorkingHoursDay;
  thursday: WorkingHoursDay;
  friday: WorkingHoursDay;
  saturday: WorkingHoursDay;
  sunday: WorkingHoursDay;
}

export const workingHours = pgTable(
  'working_hours',
  {
    ...createBaseEntityBusinessFields(business),

    // Weekly schedule stored as JSONB
    // Format: WeeklySchedule interface defined above
    weeklySchedule: jsonb('weekly_schedule').$type<WeeklySchedule>().notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'working_hours'),

    // Ensure one working hours record per business
    uniqueBusinessWorkingHours: uniqueIndex('working_hours_business_unique').on(
      t.businessId,
    ),
  }),
);

// Default working hours template
export const DEFAULT_WORKING_HOURS: WeeklySchedule = {
  monday: {
    isWorkingDay: true,
    workingHours: {
      start: '09:00',
      end: '17:00',
    },
    breaks: [
      {
        name: 'Lunch Break',
        start: '12:00',
        end: '13:00',
        isPaid: false,
      },
    ],
    totalWorkingHours: 7.0,
  },
  tuesday: {
    isWorkingDay: true,
    workingHours: {
      start: '09:00',
      end: '17:00',
    },
    breaks: [
      {
        name: 'Lunch Break',
        start: '12:00',
        end: '13:00',
        isPaid: false,
      },
    ],
    totalWorkingHours: 7.0,
  },
  wednesday: {
    isWorkingDay: true,
    workingHours: {
      start: '09:00',
      end: '17:00',
    },
    breaks: [
      {
        name: 'Lunch Break',
        start: '12:00',
        end: '13:00',
        isPaid: false,
      },
    ],
    totalWorkingHours: 7.0,
  },
  thursday: {
    isWorkingDay: true,
    workingHours: {
      start: '09:00',
      end: '17:00',
    },
    breaks: [
      {
        name: 'Lunch Break',
        start: '12:00',
        end: '13:00',
        isPaid: false,
      },
    ],
    totalWorkingHours: 7.0,
  },
  friday: {
    isWorkingDay: true,
    workingHours: {
      start: '09:00',
      end: '17:00',
    },
    breaks: [
      {
        name: 'Lunch Break',
        start: '12:00',
        end: '13:00',
        isPaid: false,
      },
    ],
    totalWorkingHours: 7.0,
  },
  saturday: {
    isWorkingDay: false,
    breaks: [],
    totalWorkingHours: 0,
  },
  sunday: {
    isWorkingDay: false,
    breaks: [],
    totalWorkingHours: 0,
  },
};
