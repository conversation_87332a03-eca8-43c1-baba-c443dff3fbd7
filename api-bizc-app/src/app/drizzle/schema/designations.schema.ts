import {
  index,
  pgEnum,
  pgTable,
  text,
  uuid,
  uniqueIndex,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { DesignationStatus } from '../../designations/dto/designation.dto';

export const designationStatusEnum = pgEnum('designation_status', [
  DesignationStatus.ACTIVE,
  DesignationStatus.INACTIVE,
]);

export const designations = pgTable(
  'designations',
  {
    ...createBaseEntityBusinessFields(business),
    name: text('name').notNull(),
    parentId: uuid('parent_id').references(() => designations.id),
    description: text('description'),
    status: designationStatusEnum('status')
      .default(DesignationStatus.ACTIVE)
      .notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'designations'),
    // Entity-specific indexes
    nameIndex: index('designations_name_index').on(t.name),
    parentIdIndex: index('designations_parent_id_index').on(t.parentId),
    statusIndex: index('designations_status_index').on(t.status),

    // Composite indexes for performance
    businessStatusIndex: index('designations_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessParentIndex: index('designations_business_parent_index')
      .on(t.businessId, t.parentId)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessName: uniqueIndex('designations_business_name_unique')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
  }),
);
