import {
  boolean,
  index,
  pgTable,
  text,
  timestamp,
  uuid,
} from 'drizzle-orm/pg-core';
import { business } from './business.schema';
import { games } from './games.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

export const gameParticipants = pgTable(
  'game_participants',
  {
    ...createBaseEntityBusinessFields(business),

    // Game reference
    gameId: uuid('game_id')
      .notNull()
      .references(() => games.id, { onDelete: 'cascade' }),

    // Participant information
    firstName: text('first_name').notNull(),
    lastName: text('last_name').notNull(),
    phone: text('phone').notNull(),
    promocode: text('promocode'), // Optional promo code

    // Winner information
    isWinner: boolean('is_winner').default(false).notNull(),
    winnerAt: timestamp('winner_at'), // Nullable timestamp for when they won
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'game_participants'),
    gameIdIndex: index('game_participants_game_id_index').on(t.gameId),
    phoneIndex: index('game_participants_phone_index').on(t.phone),
    isWinnerIndex: index('game_participants_is_winner_index').on(t.isWinner),
    createdAtIndex: index('game_participants_created_at_index').on(t.createdAt),
  }),
);

// Type exports for use in other files
export type GameParticipant = typeof gameParticipants.$inferSelect;
export type NewGameParticipant = typeof gameParticipants.$inferInsert;
