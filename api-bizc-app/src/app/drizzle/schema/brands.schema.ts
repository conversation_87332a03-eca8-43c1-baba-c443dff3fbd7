import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  boolean,
  integer,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import { media } from './media.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { BrandStatus } from '../../shared/types';

export const brandStatusEnum = pgEnum('brand_status', [
  BrandStatus.ACTIVE,
  BrandStatus.INACTIVE,
]);

export const brands = pgTable(
  'brands',
  {
    ...createBaseEntityBusinessFields(business),
    name: text('name').notNull(),
    slug: text('slug'),
    availableOnline: boolean('available_online').default(false).notNull(),
    position: integer('position').default(0).notNull(),
    logo: uuid('logo').references(() => media.id),
    // SEO fields
    seoTitle: text('seo_title'),
    seoDescription: text('seo_description'),
    seoKeywords: text('seo_keywords').array(),
    ogImage: uuid('og_image').references(() => media.id),

    status: brandStatusEnum('status').default(BrandStatus.ACTIVE).notNull(),
  },
  (t) => ({
    // Standard indexes for base entity fields
    ...createBaseEntityBusinessIndexes(t, 'brands'),

    // Entity-specific indexes
    nameIndex: index('brands_name_index').on(t.name),
    slugIndex: index('brands_slug_index').on(t.slug),
    logoIndex: index('brands_logo_index').on(t.logo),
    ogImageIndex: index('brands_og_image_index').on(t.ogImage),
    positionIndex: index('brands_position_index').on(t.position),
    statusIndex: index('brands_status_index').on(t.status),
    availableOnlineIndex: index('brands_available_online_index').on(
      t.availableOnline,
    ),

    // Composite indexes for performance
    businessNameIndex: index('brands_business_name_index').on(
      t.businessId,
      t.name,
    ),
    businessStatusIndex: index('brands_business_status_index').on(
      t.businessId,
      t.status,
    ),
    businessOnlineIndex: index('brands_business_online_index').on(
      t.businessId,
      t.availableOnline,
    ),

    // Unique constraints
    uniqueBusinessName: uniqueIndex('brands_business_name_unique')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessSlug: uniqueIndex('brands_business_slug_unique')
      .on(t.businessId, t.slug)
      .where(sql`${t.isDeleted} = false`),
  }),
);
