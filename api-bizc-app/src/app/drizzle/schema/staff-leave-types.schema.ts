import {
  boolean,
  index,
  pgTable,
  uniqueIndex,
  uuid,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import { staffMembers } from './staff.schema';
import { leaveTypes } from './leave-types.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

export const staffLeaveTypes = pgTable(
  'staff_leave_types',
  {
    ...createBaseEntityBusinessFields(business),
    staffId: uuid('staff_id')
      .notNull()
      .references(() => staffMembers.id),
    leaveTypeId: uuid('leave_type_id')
      .notNull()
      .references(() => leaveTypes.id),
    isActive: boolean('is_active').default(true).notNull(),
  },
  (t) => ({
    // Standard indexes for base entity fields
    ...createBaseEntityBusinessIndexes(t, 'staff_leave_types'),

    // Entity-specific indexes
    staffIdIndex: index('staff_leave_types_staff_id_index').on(t.staffId),
    leaveTypeIdIndex: index('staff_leave_types_leave_type_id_index').on(
      t.leaveTypeId,
    ),
    isActiveIndex: index('staff_leave_types_is_active_index').on(t.isActive),

    // Composite indexes for performance
    businessStaffIndex: index('staff_leave_types_business_staff_index')
      .on(t.businessId, t.staffId)
      .where(sql`${t.isDeleted} = false`),
    businessLeaveTypeIndex: index('staff_leave_types_business_leave_type_index')
      .on(t.businessId, t.leaveTypeId)
      .where(sql`${t.isDeleted} = false`),
    staffLeaveTypeActiveIndex: index(
      'staff_leave_types_staff_leave_type_active_index',
    )
      .on(t.staffId, t.leaveTypeId, t.isActive)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraint to prevent duplicate assignments
    uniqueStaffLeaveType: uniqueIndex(
      'staff_leave_types_staff_leave_type_unique',
    )
      .on(t.businessId, t.staffId, t.leaveTypeId)
      .where(sql`${t.isDeleted} = false`),
  }),
);
