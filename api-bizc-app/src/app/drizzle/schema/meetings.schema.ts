import {
  index,
  pgTable,
  text,
  timestamp,
  uuid,
  pgEnum,
  integer,
} from 'drizzle-orm/pg-core';
import { users } from './users.schema';
import { business } from './business.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { MeetingStatus, MeetingType } from '../../shared/types';

export const meetingStatusEnum = pgEnum('meeting_status', [
  MeetingStatus.SCHEDULED,
  MeetingStatus.COMPLETED,
  MeetingStatus.CANCELLED,
  MeetingStatus.RESCHEDULED,
]);

export const meetingTypeEnum = pgEnum('meeting_type', [
  MeetingType.ZOOM_MEET,
  MeetingType.GOOGLE_MEET,
  MeetingType.PHYSICAL,
  MeetingType.MICROSOFT_TEAMS,
]);

export const meetings = pgTable(
  'meetings',
  {
    ...createBaseEntityBusinessFields(business),
    title: text('title').notNull(),
    description: text('description'),
    scheduledDateTime: timestamp('scheduled_date_time').notNull(),
    duration: integer('duration').notNull(), // Duration in minutes
    meetingType: meetingTypeEnum('meeting_type')
      .default(MeetingType.ZOOM_MEET)
      .notNull(),
    status: meetingStatusEnum('status')
      .default(MeetingStatus.SCHEDULED)
      .notNull(),
    assignedAdminId: uuid('assigned_admin_id').references(() => users.id),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'meetings'),
    assignedAdminIdIndex: index('meetings_assigned_admin_id_index').on(
      t.assignedAdminId,
    ),
    scheduledDateTimeIndex: index('meetings_scheduled_date_time_index').on(
      t.scheduledDateTime,
    ),
    statusIndex: index('meetings_status_index').on(t.status),
  }),
);
