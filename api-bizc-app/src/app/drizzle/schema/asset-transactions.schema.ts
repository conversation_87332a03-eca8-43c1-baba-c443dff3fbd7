import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  date,
  uniqueIndex,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import { assets } from './assets.schema';
import { locations } from './locations.schema';
import { staffMembers } from './staff.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { AssetsTransactionType } from '@app/shared/types';

// Asset Transaction Type Enum
export const assetTransactionTypeEnum = pgEnum('asset_transaction_type', [
  AssetsTransactionType.ALLOCATION,
  AssetsTransactionType.DEALLOCATION,
  AssetsTransactionType.TRANSFER,
]);

export const assetTransactions = pgTable(
  'asset_transactions',
  {
    ...createBaseEntityBusinessFields(business),
    assetId: uuid('asset_id').references(() => assets.id, {
      onDelete: 'cascade',
    }),

    // Transaction details
    transactionType: assetTransactionTypeEnum(
      'asset_transaction_type',
    ).notNull(),
    refNo: text('ref_no').notNull(),
    receiver: uuid('receiver').references(() => staffMembers.id),
    fromLocationId: uuid('from_location_id').references(() => locations.id),
    toLocationId: uuid('to_location_id').references(() => locations.id),
    transactionDatetime: date('transaction_datetime').notNull(),
    allocatedUpto: date('allocated_upto'),
    reason: text('reason'),
    parentId: uuid('parent_id').references(() => assetTransactions.id, {
      onDelete: 'cascade',
    }),
  },
  (t) => ({
    // Standard base entity indexes
    ...createBaseEntityBusinessIndexes(t, 'asset_transactions'),

    // Entity-specific indexes
    assetIdIndex: index('asset_transactions_asset_id_index').on(t.assetId),
    receiverIndex: index('asset_transactions_receiver_index').on(t.receiver),
    fromLocationIdIndex: index('asset_transactions_from_location_id_index').on(
      t.fromLocationId,
    ),
    toLocationIdIndex: index('asset_transactions_to_location_id_index').on(
      t.toLocationId,
    ),
    parentIdIndex: index('asset_transactions_parent_id_index').on(t.parentId),
    transactionTypeIndex: index('asset_transactions_type_index').on(
      t.transactionType,
    ),
    refNoIndex: index('asset_transactions_ref_no_index').on(t.refNo),
    transactionDatetimeIndex: index('asset_transactions_datetime_index').on(
      t.transactionDatetime,
    ),

    // Composite indexes for common query patterns
    businessAssetIndex: index('asset_transactions_business_asset_index')
      .on(t.businessId, t.assetId)
      .where(sql`${t.isDeleted} = false`),
    businessDateIndex: index('asset_transactions_business_date_index')
      .on(t.businessId, t.transactionDatetime)
      .where(sql`${t.isDeleted} = false`),
    assetTypeIndex: index('asset_transactions_asset_type_index')
      .on(t.assetId, t.transactionType)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints
    uniqueBusinessRefNo: uniqueIndex(
      'asset_transactions_business_ref_no_unique',
    )
      .on(t.businessId, t.refNo)
      .where(sql`${t.isDeleted} = false`),
  }),
);
