import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  boolean,
  decimal,
  date,
  jsonb,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import { categories } from './categories.schema';
import { serviceCategories } from './service-categories.schema';
import { services } from './services.schema';
import { products } from './products.schema';
import { customerGroups } from './customer-groups.schema';
import { customers } from './customers.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { StatusType } from '../../shared/types';

// Enums for better type safety and consistency
export enum DiscountMethod {
  ATTACHED_TO_RECEIPT = 'attached_to_receipt',
  ATTACHED_TO_PRODUCTS = 'attached_to_products',
  ATTACHED_TO_SERVICES = 'attached_to_services',
  ATTACHED_TO_PRODUCT_CATEGORIES = 'attached_to_product_categories',
  ATTACHED_TO_SERVICE_CATEGORIES = 'attached_to_service_categories',
  ATTACHED_TO_CUSTOMER_GROUP = 'attached_to_customer_group',
  ATTACHED_TO_CUSTOMERS = 'attached_to_customers',
}

export enum DiscountType {
  PERCENTAGE = 'percentage',
  FIXED_AMOUNT = 'fixed_amount',
}

export enum DayOfWeek {
  MONDAY = 'monday',
  TUESDAY = 'tuesday',
  WEDNESDAY = 'wednesday',
  THURSDAY = 'thursday',
  FRIDAY = 'friday',
  SATURDAY = 'saturday',
  SUNDAY = 'sunday',
}

export enum TimeType {
  ALL_DAY = 'all_day',
  CUSTOM_PERIOD = 'custom_period',
}

// TypeScript interfaces for JSON field structures
// These interfaces define the expected structure of JSON data stored in the database

/**
 * Interface for time period configuration
 * Used when timeType is CUSTOM_PERIOD
 */
export interface TimePeriod {
  startTime: string; // Format: "HH:MM" (24-hour format, e.g., "09:00", "14:30")
  endTime: string; // Format: "HH:MM" (24-hour format, e.g., "17:00", "23:59")
}

/**
 * Interface for schedule configuration
 * Defines when the discount plan is active
 */
export interface ScheduleConfig {
  days: DayOfWeek[]; // Array of days when discount is active, e.g., ["monday", "tuesday"]
  timeType: TimeType; // Either "all_day" or "custom_period"
  timePeriod?: TimePeriod; // Required when timeType is "custom_period", optional otherwise
}

// Simplified approach: Use junction tables for relationships and boolean flags for "apply to all" scenarios

// Database enums using the TypeScript enum values for consistency
export const discountMethodEnum = pgEnum('discount_method', [
  DiscountMethod.ATTACHED_TO_RECEIPT,
  DiscountMethod.ATTACHED_TO_PRODUCTS,
  DiscountMethod.ATTACHED_TO_SERVICES,
  DiscountMethod.ATTACHED_TO_PRODUCT_CATEGORIES,
  DiscountMethod.ATTACHED_TO_SERVICE_CATEGORIES,
  DiscountMethod.ATTACHED_TO_CUSTOMER_GROUP,
  DiscountMethod.ATTACHED_TO_CUSTOMERS,
]);

export const discountTypeEnum = pgEnum('discount_type', [
  DiscountType.PERCENTAGE,
  DiscountType.FIXED_AMOUNT,
]);

export const discountPlanStatusEnum = pgEnum('discount_plan_status', [
  StatusType.ACTIVE,
  StatusType.INACTIVE,
]);

export const discountDayOfWeekEnum = pgEnum('discount_day_of_week', [
  DayOfWeek.MONDAY,
  DayOfWeek.TUESDAY,
  DayOfWeek.WEDNESDAY,
  DayOfWeek.THURSDAY,
  DayOfWeek.FRIDAY,
  DayOfWeek.SATURDAY,
  DayOfWeek.SUNDAY,
]);

export const timeTypeEnum = pgEnum('time_type', [
  TimeType.ALL_DAY,
  TimeType.CUSTOM_PERIOD,
]);

// Main discount plans table
export const discountPlans = pgTable(
  'discount_plans',
  {
    ...createBaseEntityBusinessFields(business),

    // Basic plan information
    planName: text('plan_name').notNull(),
    method: discountMethodEnum('method').notNull(),

    // Discount value configuration
    discountType: discountTypeEnum('discount_type').notNull(),
    discountAmount: decimal('discount_amount', {
      precision: 15,
      scale: 2,
    }).notNull(),

    // Application settings
    applyToReceiptAutomatically: boolean('apply_to_receipt_automatically')
      .default(false)
      .notNull(),
    applyToCustomer: boolean('apply_to_customer').default(false).notNull(),

    // "Apply to All" flags for different discount methods
    applyToAllProducts: boolean('apply_to_all_products')
      .default(false)
      .notNull(),
    applyToAllServices: boolean('apply_to_all_services')
      .default(false)
      .notNull(),
    applyToAllCategories: boolean('apply_to_all_categories')
      .default(false)
      .notNull(),
    applyToAllCustomerGroups: boolean('apply_to_all_customer_groups')
      .default(false)
      .notNull(),
    applyToAllCustomers: boolean('apply_to_all_customers')
      .default(false)
      .notNull(),

    // Date range
    startDate: date('start_date').notNull(),
    endDate: date('end_date').notNull(),

    // Schedule configuration (stored as JSON)
    // Format: ScheduleConfig[] - Array of schedule configurations
    // Example: [{"days": ["monday", "tuesday"], "timeType": "all_day"}, {"days": ["friday"], "timeType": "custom_period", "timePeriod": {"startTime": "09:00", "endTime": "17:00"}}]
    schedules: jsonb('schedules').notNull(),

    // Status field
    status: discountPlanStatusEnum('status')
      .default(StatusType.ACTIVE)
      .notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'discount_plans'),
    planNameIndex: index('discount_plans_plan_name_index').on(t.planName),
    methodIndex: index('discount_plans_method_index').on(t.method),
    statusIndex: index('discount_plans_status_index').on(t.status),
    startDateIndex: index('discount_plans_start_date_index').on(t.startDate),
    endDateIndex: index('discount_plans_end_date_index').on(t.endDate),

    // Optimized composite indexes
    businessStatusIndex: index('discount_plans_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessDateRangeIndex: index('discount_plans_business_date_range_index')
      .on(t.businessId, t.startDate, t.endDate)
      .where(sql`${t.isDeleted} = false`),
    businessMethodIndex: index('discount_plans_business_method_index')
      .on(t.businessId, t.method)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraint for plan name within business
    uniqueBusinessPlanName: uniqueIndex(
      'discount_plans_business_plan_name_unique',
    )
      .on(t.businessId, t.planName)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Junction table for discount plan products (many-to-many relationship)
// Includes specific discount configuration for each product
export const discountPlanProducts = pgTable(
  'discount_plan_products',
  {
    discountPlanId: uuid('discount_plan_id')
      .notNull()
      .references(() => discountPlans.id, { onDelete: 'cascade' }),
    productId: uuid('product_id')
      .notNull()
      .references(() => products.id, { onDelete: 'cascade' }),

    // Product-specific discount configuration
    discountType: discountTypeEnum('discount_type').notNull(),
    discountAmount: decimal('discount_amount', {
      precision: 15,
      scale: 2,
    }).notNull(),

    // Standard audit fields
    ...createBaseEntityBusinessFields(business),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'discount_plan_products'),
    discountPlanIdIndex: index(
      'discount_plan_products_discount_plan_id_index',
    ).on(t.discountPlanId),
    productIdIndex: index('discount_plan_products_product_id_index').on(
      t.productId,
    ),
    uniqueDiscountPlanProduct: uniqueIndex('discount_plan_products_unique').on(
      t.discountPlanId,
      t.productId,
    ),
  }),
);

// Junction table for discount plan services (many-to-many relationship)
// Includes specific discount configuration for each service
export const discountPlanServices = pgTable(
  'discount_plan_services',
  {
    discountPlanId: uuid('discount_plan_id')
      .notNull()
      .references(() => discountPlans.id, { onDelete: 'cascade' }),
    serviceId: uuid('service_id')
      .notNull()
      .references(() => services.id, { onDelete: 'cascade' }),

    // Service-specific discount configuration
    discountType: discountTypeEnum('discount_type').notNull(),
    discountAmount: decimal('discount_amount', {
      precision: 15,
      scale: 2,
    }).notNull(),

    // Standard audit fields
    ...createBaseEntityBusinessFields(business),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'discount_plan_services'),
    discountPlanIdIndex: index(
      'discount_plan_services_discount_plan_id_index',
    ).on(t.discountPlanId),
    serviceIdIndex: index('discount_plan_services_service_id_index').on(
      t.serviceId,
    ),
    uniqueDiscountPlanService: uniqueIndex('discount_plan_services_unique').on(
      t.discountPlanId,
      t.serviceId,
    ),
  }),
);

// Junction table for discount plan product categories (many-to-many relationship)
export const discountPlanProductCategories = pgTable(
  'discount_plan_product_categories',
  {
    discountPlanId: uuid('discount_plan_id')
      .notNull()
      .references(() => discountPlans.id, { onDelete: 'cascade' }),
    categoryId: uuid('category_id')
      .notNull()
      .references(() => categories.id, { onDelete: 'cascade' }),
    // Standard audit fields
    ...createBaseEntityBusinessFields(business),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'discount_plan_product_categories'),
    discountPlanIdIndex: index(
      'discount_plan_product_categories_discount_plan_id_index',
    ).on(t.discountPlanId),
    categoryIdIndex: index(
      'discount_plan_product_categories_category_id_index',
    ).on(t.categoryId),
    uniqueDiscountPlanProductCategory: uniqueIndex(
      'discount_plan_product_categories_unique',
    ).on(t.discountPlanId, t.categoryId),
  }),
);

// Junction table for discount plan service categories (many-to-many relationship)
export const discountPlanServiceCategories = pgTable(
  'discount_plan_service_categories',
  {
    discountPlanId: uuid('discount_plan_id')
      .notNull()
      .references(() => discountPlans.id, { onDelete: 'cascade' }),
    serviceCategoryId: uuid('service_category_id')
      .notNull()
      .references(() => serviceCategories.id, { onDelete: 'cascade' }),
    // Standard audit fields
    ...createBaseEntityBusinessFields(business),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'discount_plan_service_categories'),
    discountPlanIdIndex: index(
      'discount_plan_service_categories_discount_plan_id_index',
    ).on(t.discountPlanId),
    serviceCategoryIdIndex: index(
      'discount_plan_service_categories_service_category_id_index',
    ).on(t.serviceCategoryId),
    uniqueDiscountPlanServiceCategory: uniqueIndex(
      'discount_plan_service_categories_unique',
    ).on(t.discountPlanId, t.serviceCategoryId),
  }),
);

// Junction table for discount plan customer groups (many-to-many relationship)
export const discountPlanCustomerGroups = pgTable(
  'discount_plan_customer_groups',
  {
    discountPlanId: uuid('discount_plan_id')
      .notNull()
      .references(() => discountPlans.id, { onDelete: 'cascade' }),
    customerGroupId: uuid('customer_group_id')
      .notNull()
      .references(() => customerGroups.id, { onDelete: 'cascade' }),
    // Standard audit fields
    ...createBaseEntityBusinessFields(business),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'discount_plan_customer_groups'),
    discountPlanIdIndex: index(
      'discount_plan_customer_groups_discount_plan_id_index',
    ).on(t.discountPlanId),
    customerGroupIdIndex: index(
      'discount_plan_customer_groups_customer_group_id_index',
    ).on(t.customerGroupId),
    uniqueDiscountPlanCustomerGroup: uniqueIndex(
      'discount_plan_customer_groups_unique',
    ).on(t.discountPlanId, t.customerGroupId),
  }),
);

// Junction table for discount plan customers (many-to-many relationship)
export const discountPlanCustomers = pgTable(
  'discount_plan_customers',
  {
    discountPlanId: uuid('discount_plan_id')
      .notNull()
      .references(() => discountPlans.id, { onDelete: 'cascade' }),
    customerId: uuid('customer_id')
      .notNull()
      .references(() => customers.id, { onDelete: 'cascade' }),
    // Standard audit fields
    ...createBaseEntityBusinessFields(business),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'discount_plan_customers'),
    discountPlanIdIndex: index(
      'discount_plan_customers_discount_plan_id_index',
    ).on(t.discountPlanId),
    customerIdIndex: index('discount_plan_customers_customer_id_index').on(
      t.customerId,
    ),
    uniqueDiscountPlanCustomer: uniqueIndex(
      'discount_plan_customers_unique',
    ).on(t.discountPlanId, t.customerId),
  }),
);
