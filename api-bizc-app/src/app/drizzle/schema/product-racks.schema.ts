import { pgTable, uuid, text, index } from 'drizzle-orm/pg-core';
import { products } from './products.schema';
import { locations } from './locations.schema';
import { business } from './business.schema';
import { createBaseEntityBusinessFields } from './common-fields.schema';

export const productRacks = pgTable(
  'product_racks',
  {
    ...createBaseEntityBusinessFields(business),
    locationId: uuid('location_id')
      .notNull()
      .references(() => locations.id),
    productId: uuid('product_id')
      .notNull()
      .references(() => products.id),
    rack: text('rack'),
    row: text('row'),
    position: text('position'),
  },
  (t) => ({
    businessIdIndex: index('product_racks_business_id_index').on(t.businessId),
    locationIdIndex: index('product_racks_location_id_index').on(t.locationId),
    productIdIndex: index('product_racks_product_id_index').on(t.productId),
  }),
);
