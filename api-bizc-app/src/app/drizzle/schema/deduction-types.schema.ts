import {
  pgTable,
  text,
  boolean,
  decimal,
  pgEnum,
  index,
  uniqueIndex,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';

import { business } from './business.schema';
import {
  DeductionCalculationMethod,
  DeductionAppliesTo,
  DeductionStatus,
} from '../../shared/types';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

export const deductionCalculationMethodEnum = pgEnum(
  'deduction_calculation_method',
  [
    DeductionCalculationMethod.FIXED,
    DeductionCalculationMethod.PERCENTAGE,
    DeductionCalculationMethod.PROGRESSIVE,
    DeductionCalculationMethod.FORMULA,
  ],
);

export const deductionAppliesEnum = pgEnum('deduction_applies', [
  DeductionAppliesTo.GROSS,
  DeductionAppliesTo.BASIC,
  DeductionAppliesTo.NET,
]);

export const deductionStatusEnum = pgEnum('deduction_status', [
  DeductionStatus.ACTIVE,
  DeductionStatus.INACTIVE,
  DeductionStatus.PENDING,
  DeductionStatus.EXPIRED,
  DeductionStatus.COMPLETED,
]);

export const deductionTypes = pgTable(
  'deduction_types',
  {
    ...createBaseEntityBusinessFields(business),
    deductionName: text('deduction_name').notNull(),
    deductionCode: text('deduction_code').notNull(),
    calculationMethod:
      deductionCalculationMethodEnum('calculation_method').notNull(),
    amount: decimal('amount', { precision: 12, scale: 2 }),
    isSystemDefined: boolean('is_system_defined').default(false).notNull(),
    isActive: boolean('is_active').default(true).notNull(),
    appliesTo: deductionAppliesEnum('applies_to')
      .default(DeductionAppliesTo.GROSS)
      .notNull(),
    description: text('description'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'deduction_types'),
    // Entity-specific indexes
    deductionNameIndex: index('deduction_types_deduction_name_index').on(
      t.deductionName,
    ),
    deductionCodeIndex: index('deduction_types_deduction_code_index').on(
      t.deductionCode,
    ),
    calculationMethodIndex: index(
      'deduction_types_calculation_method_index',
    ).on(t.calculationMethod),
    isSystemDefinedIndex: index('deduction_types_is_system_defined_index').on(
      t.isSystemDefined,
    ),
    isActiveIndex: index('deduction_types_is_active_index').on(t.isActive),
    appliesToIndex: index('deduction_types_applies_to_index').on(t.appliesTo),

    // Composite indexes for performance
    businessActiveIndex: index('deduction_types_business_active_index')
      .on(t.businessId, t.isActive)
      .where(sql`${t.isDeleted} = false`),
    businessSystemDefinedIndex: index(
      'deduction_types_business_system_defined_index',
    )
      .on(t.businessId, t.isSystemDefined)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessDeductionCode: uniqueIndex(
      'deduction_types_business_deduction_code_unique',
    )
      .on(t.businessId, t.deductionCode)
      .where(sql`${t.isDeleted} = false`),
  }),
);
