import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  boolean,
  json,
} from 'drizzle-orm/pg-core';
import { business } from './business.schema';
import { media } from './media.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import {
  TemplateCategory,
  TemplateType,
} from '../../shared/types/template.enum';
import { StatusType } from '../../shared/types/common.enum';

// Template Category enum
export const templateCategoryEnum = pgEnum('template_category', [
  TemplateCategory.MARKETING,
  TemplateCategory.TRANSACTIONAL,
  TemplateCategory.NOTIFICATION,
  TemplateCategory.REMINDER,
  TemplateCategory.WELCOME,
  TemplateCategory.CONFIRMATION,
  TemplateCategory.INVOICE,
  TemplateCategory.RECEIPT,
  TemplateCategory.APPOINTMENT,
  TemplateCategory.PROMOTIONAL,
  TemplateCategory.NEWSLETTER,
  TemplateCategory.SUPPORT,
  TemplateCategory.ALERT,
  TemplateCategory.SURVEY,
  TemplateCategory.FEEDBACK,
  TemplateCategory.OTHER,
]);

// Template Type enum
export const templateTypeEnum = pgEnum('template_type', [
  TemplateType.PRODUCT_LAUNCH,
  TemplateType.SALE_ANNOUNCEMENT,
  TemplateType.DISCOUNT_OFFER,
  TemplateType.ORDER_CONFIRMATION,
  TemplateType.PAYMENT_CONFIRMATION,
  TemplateType.SHIPPING_NOTIFICATION,
  TemplateType.DELIVERY_CONFIRMATION,
  TemplateType.SYSTEM_UPDATE,
  TemplateType.MAINTENANCE_NOTICE,
  TemplateType.SECURITY_ALERT,
  TemplateType.APPOINTMENT_REMINDER,
  TemplateType.PAYMENT_DUE,
  TemplateType.SUBSCRIPTION_RENEWAL,
  TemplateType.CART_ABANDONMENT,
  TemplateType.NEW_USER_WELCOME,
  TemplateType.ONBOARDING_STEP,
  TemplateType.TICKET_CREATED,
  TemplateType.TICKET_RESOLVED,
  TemplateType.FAQ_RESPONSE,
  TemplateType.CUSTOM,
  TemplateType.GENERAL,
]);

// Template Status enum (using existing StatusType)
export const templateStatusEnum = pgEnum('template_status', [
  StatusType.ACTIVE,
  StatusType.INACTIVE,
]);

// SMS Template Schema
export const smsTemplates = pgTable(
  'sms_templates',
  {
    ...createBaseEntityBusinessFields(business),
    name: text('name').notNull(),
    content: text('content').notNull(),
    category: templateCategoryEnum('category').notNull(),
    type: templateTypeEnum('type'),
    description: text('description'),
    status: templateStatusEnum('status').default(StatusType.ACTIVE).notNull(),
    language: text('language'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'sms_templates'),
    nameIndex: index('sms_templates_name_index').on(t.name),
    categoryIndex: index('sms_templates_category_index').on(t.category),
    statusIndex: index('sms_templates_status_index').on(t.status),
    uniqueNamePerBusiness: uniqueIndex('sms_templates_name_business_unique').on(
      t.name,
      t.businessId,
    ),
  }),
);

// WhatsApp Template Schema
export const whatsappTemplates = pgTable(
  'whatsapp_templates',
  {
    ...createBaseEntityBusinessFields(business),
    name: text('name').notNull(),
    content: text('content').notNull(),
    category: templateCategoryEnum('category').notNull(),
    type: templateTypeEnum('type'),
    description: text('description'),
    status: templateStatusEnum('status').default(StatusType.ACTIVE).notNull(),
    language: text('language'),
    mediaId: uuid('media_id').references(() => media.id),
    captionEnabled: boolean('caption_enabled').default(false).notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'whatsapp_templates'),
    nameIndex: index('whatsapp_templates_name_index').on(t.name),
    categoryIndex: index('whatsapp_templates_category_index').on(t.category),
    statusIndex: index('whatsapp_templates_status_index').on(t.status),
    mediaIdIndex: index('whatsapp_templates_media_id_index').on(t.mediaId),
    uniqueNamePerBusiness: uniqueIndex(
      'whatsapp_templates_name_business_unique',
    ).on(t.name, t.businessId),
  }),
);

// Email Template Schema
export const emailTemplates = pgTable(
  'email_templates',
  {
    ...createBaseEntityBusinessFields(business),
    name: text('name').notNull(),
    subject: text('subject').notNull(),
    textContent: text('text_content'),
    htmlContent: text('html_content'),
    ampContent: text('amp_content'),
    fromEmail: text('from_email').notNull(),
    fromName: text('from_name'),
    replyToEmail: text('reply_to_email'),
    defaultToEmails: json('default_to_emails').$type<string[]>(),
    defaultCcEmails: json('default_cc_emails').$type<string[]>(),
    defaultBccEmails: json('default_bcc_emails').$type<string[]>(),
    category: templateCategoryEnum('category').notNull(),
    type: templateTypeEnum('type'),
    description: text('description'),
    status: templateStatusEnum('status').default(StatusType.ACTIVE).notNull(),
    language: text('language'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'email_templates'),
    nameIndex: index('email_templates_name_index').on(t.name),
    categoryIndex: index('email_templates_category_index').on(t.category),
    statusIndex: index('email_templates_status_index').on(t.status),
    fromEmailIndex: index('email_templates_from_email_index').on(t.fromEmail),
    uniqueNamePerBusiness: uniqueIndex(
      'email_templates_name_business_unique',
    ).on(t.name, t.businessId),
  }),
);
