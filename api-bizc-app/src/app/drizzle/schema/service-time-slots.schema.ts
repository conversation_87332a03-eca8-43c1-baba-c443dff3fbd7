import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  integer,
  time,
} from 'drizzle-orm/pg-core';
import { locations } from './locations.schema';
import { services } from './services.schema';
import { staffMembers } from './staff.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { business } from './business.schema';

// Day of Week TypeScript Enum
export enum DayOfWeek {
  MONDAY = 'Mon',
  TUESDAY = 'Tue',
  WEDNESDAY = 'Wed',
  THURSDAY = 'Thu',
  FRIDAY = 'Fri',
  SATURDAY = 'Sat',
  SUNDAY = 'Sun',
  ALL = 'All',
}

// Schedule Type TypeScript Enum
export enum ScheduleType {
  WEEKLY = 'weekly',
  BI_WEEKLY = 'bi_weekly',
  MONTHLY = 'monthly',
  YEARLY = 'yearly',
  ONE_TIME = 'one_time',
}

// Day of Week Enum
export const dayOfWeekEnum = pgEnum('day_of_week', [
  DayOfWeek.MONDAY,
  DayOfWeek.TUESDAY,
  DayOfWeek.WEDNESDAY,
  DayOfWeek.THURSDAY,
  DayOfWeek.FRIDAY,
  DayOfWeek.SATURDAY,
  DayOfWeek.SUNDAY,
  DayOfWeek.ALL,
]);

// Schedule Type Enum
export const scheduleTypeEnum = pgEnum('schedule_type', [
  ScheduleType.WEEKLY,
  ScheduleType.BI_WEEKLY,
  ScheduleType.MONTHLY,
  ScheduleType.YEARLY,
  ScheduleType.ONE_TIME,
]);

// Service Time Slots table
export const serviceTimeSlots = pgTable(
  'service_time_slots',
  {
    ...createBaseEntityBusinessFields(business),

    // Foreign key references
    locationId: uuid('location_id')
      .notNull()
      .references(() => locations.id),

    // Basic slot information
    slotName: text('slot_name').notNull(),

    // Time information
    startTime: time('start_time').notNull(),
    endTime: time('end_time').notNull(),

    // Capacity management
    maxAppointments: integer('max_appointments').default(1).notNull(),
    reservedCount: integer('reserved_count').default(0).notNull(),

    // Scheduling configuration
    scheduleType: scheduleTypeEnum('schedule_type')
      .default(ScheduleType.WEEKLY)
      .notNull(),
    dayOfWeek: dayOfWeekEnum('day_of_week').default(DayOfWeek.ALL).notNull(),
    dayOfMonth: integer('day_of_month'), // For monthly scheduling (1-31)
    weekOfMonth: integer('week_of_month'), // For bi-weekly scheduling (1-5: 1st week, 2nd week, etc.)
    monthOfYear: integer('month_of_year'), // For yearly scheduling (1-12: Jan-Dec)
  },
  (t) => ({
    // Indexes for performance
    locationIdIndex: index('service_time_slots_location_id_index').on(
      t.locationId,
    ),
    slotNameIndex: index('service_time_slots_slot_name_index').on(t.slotName),
    startTimeIndex: index('service_time_slots_start_time_index').on(
      t.startTime,
    ),
    endTimeIndex: index('service_time_slots_end_time_index').on(t.endTime),
    scheduleTypeIndex: index('service_time_slots_schedule_type_index').on(
      t.scheduleType,
    ),
    dayOfWeekIndex: index('service_time_slots_day_of_week_index').on(
      t.dayOfWeek,
    ),
    dayOfMonthIndex: index('service_time_slots_day_of_month_index').on(
      t.dayOfMonth,
    ),
    weekOfMonthIndex: index('service_time_slots_week_of_month_index').on(
      t.weekOfMonth,
    ),
    monthOfYearIndex: index('service_time_slots_month_of_year_index').on(
      t.monthOfYear,
    ),

    // Composite indexes for common queries
    scheduleTypeTimeIndex: index(
      'service_time_slots_schedule_type_time_index',
    ).on(t.scheduleType, t.startTime),
    dayTimeIndex: index('service_time_slots_day_time_index').on(
      t.dayOfWeek,
      t.startTime,
    ),
    monthlyScheduleIndex: index('service_time_slots_monthly_schedule_index').on(
      t.dayOfMonth,
      t.startTime,
    ),
    biWeeklyScheduleIndex: index(
      'service_time_slots_bi_weekly_schedule_index',
    ).on(t.weekOfMonth, t.dayOfWeek, t.startTime),
    yearlyScheduleIndex: index('service_time_slots_yearly_schedule_index').on(
      t.monthOfYear,
      t.dayOfMonth,
      t.startTime,
    ),

    // Unique constraints
    uniqueLocationSlotName: uniqueIndex(
      'service_time_slots_location_slot_name_unique',
    ).on(t.locationId, t.slotName),

    uniqueLocationDateTime: uniqueIndex(
      'service_time_slots_location_date_time_unique',
    ).on(t.locationId, t.startTime, t.dayOfWeek),
  }),
);

// Service Time Slot Staff Join Table
export const serviceTimeSlotStaff = pgTable(
  'service_time_slot_staff',
  {
    ...createBaseEntityBusinessFields(business),
    timeSlotId: uuid('time_slot_id')
      .notNull()
      .references(() => serviceTimeSlots.id),
    staffId: uuid('staff_id')
      .notNull()
      .references(() => staffMembers.id),
  },
  (t) => ({
    // Indexes for performance
    timeSlotIdIndex: index('service_time_slot_staff_time_slot_id_index').on(
      t.timeSlotId,
    ),
    staffIdIndex: index('service_time_slot_staff_staff_id_index').on(t.staffId),

    // Unique constraint to prevent duplicate assignments
    uniqueTimeSlotStaff: uniqueIndex(
      'service_time_slot_staff_time_slot_staff_unique',
    ).on(t.timeSlotId, t.staffId),
  }),
);

// Service Time Slot Services Join Table
export const serviceTimeSlotServices = pgTable(
  'service_time_slot_services',
  {
    ...createBaseEntityBusinessFields(business),
    timeSlotId: uuid('time_slot_id')
      .notNull()
      .references(() => serviceTimeSlots.id),
    serviceId: uuid('service_id')
      .notNull()
      .references(() => services.id),
  },
  (t) => ({
    // Indexes for performance
    timeSlotIdIndex: index('service_time_slot_services_time_slot_id_index').on(
      t.timeSlotId,
    ),
    serviceIdIndex: index('service_time_slot_services_service_id_index').on(
      t.serviceId,
    ),

    // Unique constraint to prevent duplicate service assignments
    uniqueTimeSlotService: uniqueIndex(
      'service_time_slot_services_time_slot_service_unique',
    ).on(t.timeSlotId, t.serviceId),
  }),
);
