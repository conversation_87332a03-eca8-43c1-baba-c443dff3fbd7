import {
  index,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uuid,
} from 'drizzle-orm/pg-core';
import { users } from './users.schema';
import { business } from './business.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { BusinessRoleStatus } from '../../shared/types/business.enum';

export const businessRoleStatusEnum = pgEnum('business_role_status', [
  BusinessRoleStatus.ACTIVE,
  BusinessRoleStatus.INACTIVE,
]);

export const businessRoles = pgTable(
  'business_roles',
  {
    ...createBaseEntityBusinessFields(business),
    name: text('name').notNull(),
    description: text('description'),
    permissions: text('permissions').array().notNull(),
    deletedBy: uuid('deleted_by').references(() => users.id),
    status: businessRoleStatusEnum('status')
      .default(BusinessRoleStatus.ACTIVE)
      .notNull(),
    deletedAt: timestamp('deleted_at'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'business_roles'),
    statusIndex: index('business_roles_status_index').on(t.status),
    nameIndex: index('business_roles_name_index').on(t.name),
    deletedByIndex: index('business_roles_deleted_by_index').on(t.deletedBy),
    deletedAtIndex: index('business_roles_deleted_at_index').on(t.deletedAt),
    // Composite indexes for common queries
    businessStatusIndex: index('business_roles_business_status_index').on(
      t.businessId,
      t.status,
    ),
    businessNameIndex: index('business_roles_business_name_index').on(
      t.businessId,
      t.name,
    ),
  }),
);
