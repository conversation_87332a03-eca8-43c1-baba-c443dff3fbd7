import {
  boolean,
  decimal,
  integer,
  pgEnum,
  pgTable,
  text,
  uniqueIndex,
} from 'drizzle-orm/pg-core';
import { business } from './business.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

/**
 * Reward Point Expiry Period Enum
 * Defines the time units for reward point expiration
 */
export const rewardExpiryPeriodEnum = pgEnum('reward_expiry_period', [
  'days',
  'weeks',
  'months',
  'years',
]);

/**
 * Business Reward Settings Schema
 * Stores reward point configuration for each business
 *
 * This table contains all the settings for reward point system including:
 * - Enable/disable reward points
 * - Display name for reward points
 * - Earning settings (amount to spend for 1 point, minimum order total, maximum points per order)
 * - Redemption settings (redeem amount per unit point, minimum order total, minimum redeem point, maximum redeem point per order)
 * - Expiry settings (reward point expiry period and duration)
 */
export const businessRewardSettings = pgTable(
  'business_reward_settings',
  {
    ...createBaseEntityBusinessFields(business),

    // Basic Settings
    isEnabled: boolean('is_enabled').default(false).notNull(),
    displayName: text('display_name').default('Customer Reward').notNull(),

    // Earning Points Settings
    amountSpendForUnitPoint: decimal('amount_spend_for_unit_point', {
      precision: 10,
      scale: 2,
    })
      .default('1.00')
      .notNull(), // Amount customer needs to spend to earn 1 point

    minimumOrderTotalToEarnReward: decimal(
      'minimum_order_total_to_earn_reward',
      {
        precision: 10,
        scale: 2,
      },
    )
      .default('1000.00')
      .notNull(), // Minimum order total to earn reward points

    maximumPointsPerOrder: integer('maximum_points_per_order'), // Maximum points that can be earned per order (nullable for unlimited)

    // Redeem Points Settings
    redeemAmountPerUnitPoint: decimal('redeem_amount_per_unit_point', {
      precision: 10,
      scale: 2,
    })
      .default('0.01')
      .notNull(), // Amount value of 1 reward point when redeeming

    minimumOrderTotalToRedeemPoints: decimal(
      'minimum_order_total_to_redeem_points',
      {
        precision: 10,
        scale: 2,
      },
    )
      .default('1000.00')
      .notNull(), // Minimum order total to redeem points

    minimumRedeemPoint: integer('minimum_redeem_point'), // Minimum points required to redeem (nullable for no minimum)

    maximumRedeemPointPerOrder: integer('maximum_redeem_point_per_order'), // Maximum points that can be redeemed per order (nullable for unlimited)

    // Reward Point Expiry Settings
    rewardPointExpiryPeriod: integer('reward_point_expiry_period')
      .default(1)
      .notNull(), // Duration number
    rewardPointExpiryPeriodUnit: rewardExpiryPeriodEnum(
      'reward_point_expiry_period_unit',
    )
      .default('years')
      .notNull(), // Duration unit (days, weeks, months, years)
  },
  (t) => ({
    // Standard base entity indexes
    ...createBaseEntityBusinessIndexes(t, 'business_reward_settings'),

    // Entity-specific constraints - ensure one reward setting per business
    uniqueBusinessRewardSettings: uniqueIndex(
      'business_reward_settings_business_unique',
    ).on(t.businessId),
  }),
);
