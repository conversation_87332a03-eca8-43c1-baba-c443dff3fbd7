import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  jsonb,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import { promoCodes } from './promo-codes.schema';
import { media } from './media.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { GameType, GameStatus } from '../../shared/types';

// Game types enum
export const gameEnum = pgEnum('game', [
  GameType.CLICK_CHALLENGE,
  GameType.LUCKY_SPINNER,
  GameType.MEMORY_GAME,
  GameType.QUIZ_GAME,
  GameType.SLIDING_PUZZLE,
  GameType.SPINNER_GAME,
]);

// Game status enum
export const gameStatusEnum = pgEnum('game_status', [
  GameStatus.ACTIVE,
  GameStatus.INACTIVE,
]);

export const games = pgTable(
  'games',
  {
    ...createBaseEntityBusinessFields(business),
    name: text('name').notNull(),
    description: text('description'),
    publicId: text('public_id').notNull(),
    game: gameEnum('game').notNull(),

    // Purchase and configuration
    configuration: jsonb('configuration'), // Store game-specific settings as JSON

    // Promo code relationship
    promoCode: uuid('promo_code').references(() => promoCodes.id),

    // Media
    ogImage: uuid('og_image').references(() => media.id),

    // Pricing and financial

    // Status and lifecycle
    status: gameStatusEnum('status').default(GameStatus.ACTIVE).notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'games'),
    nameIndex: index('games_name_index').on(t.name),
    gameTypeIndex: index('games_game_type_index').on(t.game),
    statusIndex: index('games_status_index').on(t.status),
    promoCodeIndex: index('games_promo_code_index').on(t.promoCode),
    ogImageIndex: index('games_og_image_index').on(t.ogImage),

    // Composite indexes for filtering and sorting
    businessGameTypeIndex: index('games_business_game_type_index')
      .on(t.businessId, t.game)
      .where(sql`${t.isDeleted} = false`),
    businessStatusIndex: index('games_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),

    // Search and filtering indexes
    businessNameIndex: index('games_business_name_index')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
    businessPublicIdIndex: index('games_business_public_id_index')
      .on(t.businessId, t.publicId)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessName: uniqueIndex('games_business_name_unique')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessPublicId: uniqueIndex('games_business_public_id_unique')
      .on(t.businessId, t.publicId)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Export types for TypeScript usage
export type Game = typeof games.$inferSelect;
export type NewGame = typeof games.$inferInsert;
