import {
  index,
  pgTable,
  text,
  pgEnum,
  uniqueIndex,
  boolean,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { UnitStatus } from '../../shared/types';

export const unitStatusEnum = pgEnum('unit_status', [
  UnitStatus.ACTIVE,
  UnitStatus.INACTIVE,
]);

export const units = pgTable(
  'units',
  {
    ...createBaseEntityBusinessFields(business),
    name: text('name').notNull(),
    code: text('code').notNull(),
    category: text('category').notNull(),
    isBaseUnit: boolean('is_base_unit').default(false).notNull(),
    status: unitStatusEnum('status').default(UnitStatus.ACTIVE).notNull(),
  },
  (t) => ({
    // Standard base entity indexes
    ...createBaseEntityBusinessIndexes(t, 'units'),
    nameIndex: index('units_name_index').on(t.name),
    codeIndex: index('units_code_index').on(t.code),
    categoryIndex: index('units_category_index').on(t.category),
    statusIndex: index('units_status_index').on(t.status),
    isBaseUnitIndex: index('units_is_base_unit_index').on(t.isBaseUnit),

    // Optimized composite indexes for filtering and searching
    businessNameIndex: index('units_business_name_index')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
    businessCodeIndex: index('units_business_code_index')
      .on(t.businessId, t.code)
      .where(sql`${t.isDeleted} = false`),
    businessCategoryIndex: index('units_business_category_index')
      .on(t.businessId, t.category)
      .where(sql`${t.isDeleted} = false`),
    businessStatusIndex: index('units_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessName: uniqueIndex('units_business_name_unique')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessCode: uniqueIndex('units_business_code_unique')
      .on(t.businessId, t.code)
      .where(sql`${t.isDeleted} = false`),
  }),
);
