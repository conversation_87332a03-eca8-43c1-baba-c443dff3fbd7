import {
  date,
  decimal,
  index,
  integer,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uuid,
  uniqueIndex,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import { locations } from './locations.schema';
import { staffMembers } from './staff.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

// Priority Enum
export enum Priority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent',
}

// Purchase Requisition Status Enum
export enum PurchaseRequisitionStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  PARTIALLY_CONVERTED = 'partially_converted',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

// Purchase Requisition Line Status Enum
export enum PurchaseRequisitionLineStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  CONVERTED = 'converted',
}

// Purchase Quotation Status Enum
export enum PurchaseQuotationStatus {
  DRAFT = 'draft',
  SENT = 'sent',
  RECEIVED = 'received',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected',
  EXPIRED = 'expired',
}

// Purchase Order Status Enum
export enum PurchaseOrderStatus {
  DRAFT = 'draft',
  SENT = 'sent',
  ACKNOWLEDGED = 'acknowledged',
  PARTIALLY_RECEIVED = 'partially_received',
  RECEIVED = 'received',
  INVOICED = 'invoiced',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

// Purchase Order Line Status Enum
export enum PurchaseOrderLineStatus {
  PENDING = 'pending',
  PARTIALLY_RECEIVED = 'partially_received',
  RECEIVED = 'received',
  CANCELLED = 'cancelled',
}

// Goods Receipt Status Enum
export enum GoodsReceiptStatus {
  DRAFT = 'draft',
  RECEIVED = 'received',
  INSPECTED = 'inspected',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected',
  POSTED = 'posted',
}

// Goods Receipt Line Status Enum
export enum GoodsReceiptLineStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected',
}

// Purchase Invoice Status Enum
export enum PurchaseInvoiceStatus {
  DRAFT = 'draft',
  RECEIVED = 'received',
  VERIFIED = 'verified',
  APPROVED = 'approved',
  PAID = 'paid',
  DISPUTED = 'disputed',
  CANCELLED = 'cancelled',
}

// Purchase Return Type Enum
export enum PurchaseReturnType {
  QUALITY_ISSUE = 'quality_issue',
  WRONG_ITEM = 'wrong_item',
  EXCESS_QUANTITY = 'excess_quantity',
  DAMAGED = 'damaged',
  EXPIRED = 'expired',
}

// Purchase Return Status Enum
export enum PurchaseReturnStatus {
  DRAFT = 'draft',
  APPROVED = 'approved',
  SHIPPED = 'shipped',
  ACKNOWLEDGED = 'acknowledged',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

// Supplier Payment Method Enum
export enum SupplierPaymentMethod {
  CASH = 'cash',
  CHEQUE = 'cheque',
  BANK_TRANSFER = 'bank_transfer',
  CREDIT_CARD = 'credit_card',
  ONLINE = 'online',
}

// Supplier Payment Status Enum
export enum SupplierPaymentStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

export const priorityEnum = pgEnum('priority', [
  Priority.LOW,
  Priority.NORMAL,
  Priority.HIGH,
  Priority.URGENT,
]);

export const purchaseRequisitionStatusEnum = pgEnum(
  'purchase_requisition_status',
  [
    PurchaseRequisitionStatus.DRAFT,
    PurchaseRequisitionStatus.SUBMITTED,
    PurchaseRequisitionStatus.APPROVED,
    PurchaseRequisitionStatus.REJECTED,
    PurchaseRequisitionStatus.PARTIALLY_CONVERTED,
    PurchaseRequisitionStatus.COMPLETED,
    PurchaseRequisitionStatus.CANCELLED,
  ],
);

export const purchaseRequisitionLineStatusEnum = pgEnum(
  'purchase_requisition_line_status',
  [
    PurchaseRequisitionLineStatus.PENDING,
    PurchaseRequisitionLineStatus.APPROVED,
    PurchaseRequisitionLineStatus.REJECTED,
    PurchaseRequisitionLineStatus.CONVERTED,
  ],
);

export const purchaseQuotationStatusEnum = pgEnum('purchase_quotation_status', [
  PurchaseQuotationStatus.DRAFT,
  PurchaseQuotationStatus.SENT,
  PurchaseQuotationStatus.RECEIVED,
  PurchaseQuotationStatus.ACCEPTED,
  PurchaseQuotationStatus.REJECTED,
  PurchaseQuotationStatus.EXPIRED,
]);

export const purchaseOrderStatusEnum = pgEnum('purchase_order_status', [
  PurchaseOrderStatus.DRAFT,
  PurchaseOrderStatus.SENT,
  PurchaseOrderStatus.ACKNOWLEDGED,
  PurchaseOrderStatus.PARTIALLY_RECEIVED,
  PurchaseOrderStatus.RECEIVED,
  PurchaseOrderStatus.INVOICED,
  PurchaseOrderStatus.COMPLETED,
  PurchaseOrderStatus.CANCELLED,
]);

export const purchaseOrderLineStatusEnum = pgEnum(
  'purchase_order_line_status',
  [
    PurchaseOrderLineStatus.PENDING,
    PurchaseOrderLineStatus.PARTIALLY_RECEIVED,
    PurchaseOrderLineStatus.RECEIVED,
    PurchaseOrderLineStatus.CANCELLED,
  ],
);

export const goodsReceiptStatusEnum = pgEnum('goods_receipt_status', [
  GoodsReceiptStatus.DRAFT,
  GoodsReceiptStatus.RECEIVED,
  GoodsReceiptStatus.INSPECTED,
  GoodsReceiptStatus.ACCEPTED,
  GoodsReceiptStatus.REJECTED,
  GoodsReceiptStatus.POSTED,
]);

export const goodsReceiptLineStatusEnum = pgEnum('goods_receipt_line_status', [
  GoodsReceiptLineStatus.PENDING,
  GoodsReceiptLineStatus.ACCEPTED,
  GoodsReceiptLineStatus.REJECTED,
]);

export const purchaseInvoiceStatusEnum = pgEnum('purchase_invoice_status', [
  PurchaseInvoiceStatus.DRAFT,
  PurchaseInvoiceStatus.RECEIVED,
  PurchaseInvoiceStatus.VERIFIED,
  PurchaseInvoiceStatus.APPROVED,
  PurchaseInvoiceStatus.PAID,
  PurchaseInvoiceStatus.DISPUTED,
  PurchaseInvoiceStatus.CANCELLED,
]);

export const purchaseReturnTypeEnum = pgEnum('purchase_return_type', [
  PurchaseReturnType.QUALITY_ISSUE,
  PurchaseReturnType.WRONG_ITEM,
  PurchaseReturnType.EXCESS_QUANTITY,
  PurchaseReturnType.DAMAGED,
  PurchaseReturnType.EXPIRED,
]);

export const purchaseReturnStatusEnum = pgEnum('purchase_return_status', [
  PurchaseReturnStatus.DRAFT,
  PurchaseReturnStatus.APPROVED,
  PurchaseReturnStatus.SHIPPED,
  PurchaseReturnStatus.ACKNOWLEDGED,
  PurchaseReturnStatus.COMPLETED,
  PurchaseReturnStatus.CANCELLED,
]);

export const supplierPaymentMethodEnum = pgEnum('supplier_payment_method', [
  SupplierPaymentMethod.CASH,
  SupplierPaymentMethod.CHEQUE,
  SupplierPaymentMethod.BANK_TRANSFER,
  SupplierPaymentMethod.CREDIT_CARD,
  SupplierPaymentMethod.ONLINE,
]);

export const supplierPaymentStatusEnum = pgEnum('supplier_payment_status', [
  SupplierPaymentStatus.PENDING,
  SupplierPaymentStatus.PROCESSING,
  SupplierPaymentStatus.COMPLETED,
  SupplierPaymentStatus.FAILED,
  SupplierPaymentStatus.CANCELLED,
]);

// Purchase Requisitions table
export const purchaseRequisitions = pgTable(
  'purchase_requisitions',
  {
    ...createBaseEntityBusinessFields(business),
    requisitionNumber: text('requisition_number').notNull(),
    departmentId: uuid('department_id'), // References DEPARTMENTS table
    requestedBy: uuid('requested_by')
      .notNull()
      .references(() => staffMembers.id),
    requisitionDate: date('requisition_date').notNull(),
    requiredDate: date('required_date'),
    priority: priorityEnum('priority').default(Priority.NORMAL).notNull(),
    status: purchaseRequisitionStatusEnum('status')
      .default(PurchaseRequisitionStatus.DRAFT)
      .notNull(),
    purpose: text('purpose'),
    justification: text('justification'),
    estimatedTotal: decimal('estimated_total', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    approvedBy: uuid('approved_by').references(() => staffMembers.id),
    approvedDate: timestamp('approved_date'),
    rejectionReason: text('rejection_reason'),
    notes: text('notes'),
  },
  (t) => ({
    // Primary index
    idIndex: index('purchase_requisitions_id_index').on(t.id),

    // Foreign key indexes
    ...createBaseEntityBusinessIndexes(t, 'purchase_requisitions'),

    departmentIdIndex: index('purchase_requisitions_department_id_index').on(
      t.departmentId,
    requestedByIndex: index('purchase_requisitions_requested_by_index').on(
      t.requestedBy,
    approvedByIndex: index('purchase_requisitions_approved_by_index').on(
      t.approvedBy,
    // Performance indexes
    priorityIndex: index('purchase_requisitions_priority_index').on(t.priority),
    statusIndex: index('purchase_requisitions_status_index').on(t.status),
    requisitionDateIndex: index(
      'purchase_requisitions_requisition_date_index',
    ).on(t.requisitionDate),
    requiredDateIndex: index('purchase_requisitions_required_date_index').on(
      t.requiredDate,
    approvedDateIndex: index('purchase_requisitions_approved_date_index').on(
      t.approvedDate,
    // Unique constraint
    uniqueRequisitionNumber: uniqueIndex(
      'purchase_requisitions_requisition_number_unique',
    )
      .on(t.businessId, t.requisitionNumber)
      .where(sql`${t.isDeleted} = false`),

    // Composite indexes for common query patterns
    businessStatusIndex: index('purchase_requisitions_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessPriorityIndex: index(
      'purchase_requisitions_business_priority_index',
    )
      .on(t.businessId, t.priority)
      .where(sql`${t.isDeleted} = false`),
    businessDateIndex: index('purchase_requisitions_business_date_index')
      .on(t.businessId, t.requisitionDate)
      .where(sql`${t.isDeleted} = false`),

    // Audit indexes
    createdAtIndex: index('purchase_requisitions_created_at_index').on(
      t.createdAt,
    updatedAtIndex: index('purchase_requisitions_updated_at_index').on(
      t.updatedAt,
  }),
);

// Purchase Requisition Lines table
export const purchaseRequisitionLines = pgTable(
  'purchase_requisition_lines',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    requisitionId: uuid('requisition_id')
      .notNull()
      .references(() => purchaseRequisitions.id, { onDelete: 'cascade' }),
    productId: uuid('product_id').notNull(), // References PRODUCTS table
    variantId: uuid('variant_id'), // References PRODUCT_VARIANTS table
    productDescription: text('product_description').notNull(),
    quantityRequested: decimal('quantity_requested', {
      precision: 15,
      scale: 3,
    }).notNull(),
    quantityApproved: decimal('quantity_approved', { precision: 15, scale: 3 })
      .default('0.000')
      .notNull(),
    unitOfMeasure: text('unit_of_measure'),
    estimatedUnitPrice: decimal('estimated_unit_price', {
      precision: 10,
      scale: 2,
    })
      .default('0.00')
      .notNull(),
    estimatedTotal: decimal('estimated_total', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    specifications: text('specifications'),
    preferredSupplier: text('preferred_supplier'),
    status: purchaseRequisitionLineStatusEnum('status')
      .default(PurchaseRequisitionLineStatus.PENDING)
      .notNull(),
    notes: text('notes'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
  },
  (t) => ({
    // Primary index
    idIndex: index('purchase_requisition_lines_id_index').on(t.id),

    // Foreign key indexes
    ...createBaseEntityBusinessIndexes(t, 'purchase_requisition_lines'),

    requisitionIdIndex: index(
      'purchase_requisition_lines_requisition_id_index',
    ).on(t.requisitionId),
    productIdIndex: index('purchase_requisition_lines_product_id_index').on(
      t.productId,
    variantIdIndex: index('purchase_requisition_lines_variant_id_index').on(
      t.variantId,
    // Performance indexes
    statusIndex: index('purchase_requisition_lines_status_index').on(t.status),

    // Composite indexes for common query patterns
    businessRequisitionIndex: index(
      'purchase_requisition_lines_business_requisition_index',
    ).on(t.businessId, t.requisitionId),
    businessStatusIndex: index(
      'purchase_requisition_lines_business_status_index',
    ).on(t.businessId, t.status),

    // Audit indexes
    createdAtIndex: index('purchase_requisition_lines_created_at_index').on(
      t.createdAt,
  }),
);

// Purchase Quotations table
export const purchaseQuotations = pgTable(
  'purchase_quotations',
  {
    ...createBaseEntityBusinessFields(business),
    quotationNumber: text('quotation_number').notNull(),
    supplierId: uuid('supplier_id').notNull(), // References SUPPLIERS table
    requisitionId: uuid('requisition_id').references(
      () => purchaseRequisitions.id,
    quotationDate: date('quotation_date').notNull(),
    validUntilDate: date('valid_until_date'),
    status: purchaseQuotationStatusEnum('status')
      .default(PurchaseQuotationStatus.DRAFT)
      .notNull(),
    referenceNumber: text('reference_number'),
    subtotal: decimal('subtotal', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    taxAmount: decimal('tax_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    discountAmount: decimal('discount_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    shippingAmount: decimal('shipping_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    totalAmount: decimal('total_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    paymentTerms: text('payment_terms'),
    deliveryTerms: text('delivery_terms'),
    termsAndConditions: text('terms_and_conditions'),
  },
  (t) => ({
    // Primary index
    idIndex: index('purchase_quotations_id_index').on(t.id),

    // Foreign key indexes
    ...createBaseEntityBusinessIndexes(t, 'purchase_quotations'),

    supplierIdIndex: index('purchase_quotations_supplier_id_index').on(
      t.supplierId,
    requisitionIdIndex: index('purchase_quotations_requisition_id_index').on(
      t.requisitionId,
    // Performance indexes
    statusIndex: index('purchase_quotations_status_index').on(t.status),
    quotationDateIndex: index('purchase_quotations_quotation_date_index').on(
      t.quotationDate,
    validUntilDateIndex: index('purchase_quotations_valid_until_date_index').on(
      t.validUntilDate,
    // Unique constraint
    uniqueQuotationNumber: uniqueIndex(
      'purchase_quotations_quotation_number_unique',
    )
      .on(t.businessId, t.quotationNumber)
      .where(sql`${t.isDeleted} = false`),

    // Composite indexes for common query patterns
    businessStatusIndex: index('purchase_quotations_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessSupplierIndex: index('purchase_quotations_business_supplier_index')
      .on(t.businessId, t.supplierId)
      .where(sql`${t.isDeleted} = false`),
    businessDateIndex: index('purchase_quotations_business_date_index')
      .on(t.businessId, t.quotationDate)
      .where(sql`${t.isDeleted} = false`),

    // Audit indexes
    createdAtIndex: index('purchase_quotations_created_at_index').on(
      t.createdAt,
    updatedAtIndex: index('purchase_quotations_updated_at_index').on(
      t.updatedAt,
  }),
);

// Purchase Quotation Lines table
export const purchaseQuotationLines = pgTable(
  'purchase_quotation_lines',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    quotationId: uuid('quotation_id')
      .notNull()
      .references(() => purchaseQuotations.id, { onDelete: 'cascade' }),
    productId: uuid('product_id').notNull(), // References PRODUCTS table
    variantId: uuid('variant_id'), // References PRODUCT_VARIANTS table
    productDescription: text('product_description').notNull(),
    quantity: decimal('quantity', { precision: 15, scale: 3 }).notNull(),
    unitPrice: decimal('unit_price', { precision: 10, scale: 2 }).notNull(),
    discountPercent: decimal('discount_percent', { precision: 5, scale: 2 })
      .default('0.00')
      .notNull(),
    lineSubtotal: decimal('line_subtotal', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    taxAmount: decimal('tax_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    lineTotal: decimal('line_total', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    leadTimeDays: integer('lead_time_days').default(0).notNull(),
    brand: text('brand'),
    model: text('model'),
    specifications: text('specifications'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
  },
  (t) => ({
    // Primary index
    idIndex: index('purchase_quotation_lines_id_index').on(t.id),

    // Foreign key indexes
    ...createBaseEntityBusinessIndexes(t, 'purchase_quotation_lines'),

    quotationIdIndex: index('purchase_quotation_lines_quotation_id_index').on(
      t.quotationId,
    productIdIndex: index('purchase_quotation_lines_product_id_index').on(
      t.productId,
    variantIdIndex: index('purchase_quotation_lines_variant_id_index').on(
      t.variantId,
    // Performance indexes
    leadTimeDaysIndex: index(
      'purchase_quotation_lines_lead_time_days_index',
    ).on(t.leadTimeDays),

    // Composite indexes for common query patterns
    businessQuotationIndex: index(
      'purchase_quotation_lines_business_quotation_index',
    ).on(t.businessId, t.quotationId),

    // Audit indexes
    createdAtIndex: index('purchase_quotation_lines_created_at_index').on(
      t.createdAt,
  }),
);

// Purchase Orders table
export const purchaseOrders = pgTable(
  'purchase_orders',
  {
    ...createBaseEntityBusinessFields(business),
    poNumber: text('po_number').notNull(),
    supplierId: uuid('supplier_id').notNull(), // References SUPPLIERS table
    quotationId: uuid('quotation_id').references(() => purchaseQuotations.id),
    requisitionId: uuid('requisition_id').references(
      () => purchaseRequisitions.id,
    orderDate: date('order_date').notNull(),
    expectedDeliveryDate: date('expected_delivery_date'),
    promisedDeliveryDate: date('promised_delivery_date'),
    status: purchaseOrderStatusEnum('status')
      .default(PurchaseOrderStatus.DRAFT)
      .notNull(),
    priority: priorityEnum('priority').default(Priority.NORMAL).notNull(),
    locationId: uuid('location_id')
      .notNull()
      .references(() => locations.id),
    subtotal: decimal('subtotal', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    taxAmount: decimal('tax_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    discountAmount: decimal('discount_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    shippingAmount: decimal('shipping_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    totalAmount: decimal('total_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    paymentTerms: text('payment_terms'),
    deliveryTerms: text('delivery_terms'),
    billingAddress: text('billing_address'),
    deliveryAddress: text('delivery_address'),
    termsAndConditions: text('terms_and_conditions'),
    notes: text('notes'),
    approvedBy: uuid('approved_by').references(() => staffMembers.id),
    approvedDate: timestamp('approved_date'),
    sentDate: timestamp('sent_date'),
  },
  (t) => ({
    // Primary index
    idIndex: index('purchase_orders_id_index').on(t.id),

    // Foreign key indexes
    ...createBaseEntityBusinessIndexes(t, 'purchase_orders'),

    supplierIdIndex: index('purchase_orders_supplier_id_index').on(
      t.supplierId,
    quotationIdIndex: index('purchase_orders_quotation_id_index').on(
      t.quotationId,
    requisitionIdIndex: index('purchase_orders_requisition_id_index').on(
      t.requisitionId,
    locationIdIndex: index('purchase_orders_location_id_index').on(
      t.locationId,
    approvedByIndex: index('purchase_orders_approved_by_index').on(
      t.approvedBy,
    // Performance indexes
    statusIndex: index('purchase_orders_status_index').on(t.status),
    priorityIndex: index('purchase_orders_priority_index').on(t.priority),
    orderDateIndex: index('purchase_orders_order_date_index').on(t.orderDate),
    expectedDeliveryDateIndex: index(
      'purchase_orders_expected_delivery_date_index',
    ).on(t.expectedDeliveryDate),
    promisedDeliveryDateIndex: index(
      'purchase_orders_promised_delivery_date_index',
    ).on(t.promisedDeliveryDate),
    approvedDateIndex: index('purchase_orders_approved_date_index').on(
      t.approvedDate,
    sentDateIndex: index('purchase_orders_sent_date_index').on(t.sentDate),

    // Unique constraint
    uniquePoNumber: uniqueIndex('purchase_orders_po_number_unique')
      .on(t.businessId, t.poNumber)
      .where(sql`${t.isDeleted} = false`),

    // Composite indexes for common query patterns
    businessStatusIndex: index('purchase_orders_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessSupplierIndex: index('purchase_orders_business_supplier_index')
      .on(t.businessId, t.supplierId)
      .where(sql`${t.isDeleted} = false`),
    businessPriorityIndex: index('purchase_orders_business_priority_index')
      .on(t.businessId, t.priority)
      .where(sql`${t.isDeleted} = false`),
    businessDateIndex: index('purchase_orders_business_date_index')
      .on(t.businessId, t.orderDate)
      .where(sql`${t.isDeleted} = false`),

    // Audit indexes
    createdAtIndex: index('purchase_orders_created_at_index').on(t.createdAt),
    updatedAtIndex: index('purchase_orders_updated_at_index').on(t.updatedAt),
  }),
);

// Purchase Order Lines table
export const purchaseOrderLines = pgTable(
  'purchase_order_lines',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    purchaseOrderId: uuid('purchase_order_id')
      .notNull()
      .references(() => purchaseOrders.id, { onDelete: 'cascade' }),
    productId: uuid('product_id').notNull(), // References PRODUCTS table
    variantId: uuid('variant_id'), // References PRODUCT_VARIANTS table
    reqLineId: uuid('req_line_id').references(
      () => purchaseRequisitionLines.id,
    productDescription: text('product_description').notNull(),
    quantityOrdered: decimal('quantity_ordered', {
      precision: 15,
      scale: 3,
    }).notNull(),
    quantityReceived: decimal('quantity_received', { precision: 15, scale: 3 })
      .default('0.000')
      .notNull(),
    quantityInvoiced: decimal('quantity_invoiced', { precision: 15, scale: 3 })
      .default('0.000')
      .notNull(),
    unitPrice: decimal('unit_price', { precision: 10, scale: 2 }).notNull(),
    discountPercent: decimal('discount_percent', { precision: 5, scale: 2 })
      .default('0.00')
      .notNull(),
    lineSubtotal: decimal('line_subtotal', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    taxAmount: decimal('tax_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    lineTotal: decimal('line_total', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    expectedDeliveryDate: date('expected_delivery_date'),
    lineStatus: purchaseOrderLineStatusEnum('line_status')
      .default(PurchaseOrderLineStatus.PENDING)
      .notNull(),
    specifications: text('specifications'),
    notes: text('notes'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
  },
  (t) => ({
    // Primary index
    ...createBaseEntityBusinessIndexes(t, 'purchase_order_lines'),
    idIndex: index('purchase_order_lines_id_index').on(t.id),

    // Foreign key indexes
    purchaseOrderIdIndex: index(
      'purchase_order_lines_purchase_order_id_index',
    ).on(t.purchaseOrderId),
    productIdIndex: index('purchase_order_lines_product_id_index').on(
      t.productId,
    variantIdIndex: index('purchase_order_lines_variant_id_index').on(
      t.variantId,
    reqLineIdIndex: index('purchase_order_lines_req_line_id_index').on(
      t.reqLineId,
    // Performance indexes
    lineStatusIndex: index('purchase_order_lines_line_status_index').on(
      t.lineStatus,
    expectedDeliveryDateIndex: index(
      'purchase_order_lines_expected_delivery_date_index',
    ).on(t.expectedDeliveryDate),

    // Composite indexes for common query patterns
    businessPurchaseOrderIndex: index(
      'purchase_order_lines_business_purchase_order_index',
    ).on(t.businessId, t.purchaseOrderId),
    businessLineStatusIndex: index(
      'purchase_order_lines_business_line_status_index',
    ).on(t.businessId, t.lineStatus),

    // Audit indexes
    createdAtIndex: index('purchase_order_lines_created_at_index').on(
      t.createdAt,
  }),
);

// Goods Receipts table
export const goodsReceipts = pgTable(
  'goods_receipts',
  {
    ...createBaseEntityBusinessFields(business),
    receiptNumber: text('receipt_number').notNull(),
    purchaseOrderId: uuid('purchase_order_id')
      .notNull()
      .references(() => purchaseOrders.id),
    supplierId: uuid('supplier_id').notNull(), // References SUPPLIERS table
    locationId: uuid('location_id')
      .notNull()
      .references(() => locations.id),
    receiptDate: date('receipt_date').notNull(),
    deliveryNoteNumber: text('delivery_note_number'),
    vehicleNumber: text('vehicle_number'),
    status: goodsReceiptStatusEnum('status')
      .default(GoodsReceiptStatus.DRAFT)
      .notNull(),
    freightCharges: decimal('freight_charges', { precision: 10, scale: 2 })
      .default('0.00')
      .notNull(),
    receivedBy: text('received_by'),
    inspectedBy: text('inspected_by'),
    notes: text('notes'),
  },
  (t) => ({
    // Primary index
    ...createBaseEntityBusinessIndexes(t, 'goods_receipts'),
    idIndex: index('goods_receipts_id_index').on(t.id),

    // Foreign key indexes
    businessIdIndex: index('goods_receipts_business_id_index').on(t.businessId),
    purchaseOrderIdIndex: index('goods_receipts_purchase_order_id_index').on(
      t.purchaseOrderId,
    supplierIdIndex: index('goods_receipts_supplier_id_index').on(t.supplierId),
    locationIdIndex: index('goods_receipts_location_id_index').on(t.locationId),

    // Performance indexes
    statusIndex: index('goods_receipts_status_index').on(t.status),
    receiptDateIndex: index('goods_receipts_receipt_date_index').on(
      t.receiptDate,
    // Unique constraint
    uniqueReceiptNumber: uniqueIndex('goods_receipts_receipt_number_unique')
      .on(t.businessId, t.receiptNumber)
      .where(sql`${t.isDeleted} = false`),

    // Composite indexes for common query patterns
    businessStatusIndex: index('goods_receipts_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessSupplierIndex: index('goods_receipts_business_supplier_index')
      .on(t.businessId, t.supplierId)
      .where(sql`${t.isDeleted} = false`),
    businessDateIndex: index('goods_receipts_business_date_index')
      .on(t.businessId, t.receiptDate)
      .where(sql`${t.isDeleted} = false`),

    // Audit indexes
    createdAtIndex: index('goods_receipts_created_at_index').on(t.createdAt),
    updatedAtIndex: index('goods_receipts_updated_at_index').on(t.updatedAt),
  }),
);

// Goods Receipt Lines table
export const goodsReceiptLines = pgTable(
  'goods_receipt_lines',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    receiptId: uuid('receipt_id')
      .notNull()
      .references(() => goodsReceipts.id, { onDelete: 'cascade' }),
    poLineId: uuid('po_line_id')
      .notNull()
      .references(() => purchaseOrderLines.id),
    productId: uuid('product_id').notNull(), // References PRODUCTS table
    variantId: uuid('variant_id'), // References PRODUCT_VARIANTS table
    quantityOrdered: decimal('quantity_ordered', {
      precision: 15,
      scale: 3,
    }).notNull(),
    quantityReceived: decimal('quantity_received', {
      precision: 15,
      scale: 3,
    }).notNull(),
    quantityAccepted: decimal('quantity_accepted', { precision: 15, scale: 3 })
      .default('0.000')
      .notNull(),
    quantityRejected: decimal('quantity_rejected', { precision: 15, scale: 3 })
      .default('0.000')
      .notNull(),
    unitCost: decimal('unit_cost', { precision: 10, scale: 2 }).notNull(),
    totalCost: decimal('total_cost', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    batchNumber: text('batch_number'),
    manufactureDate: date('manufacture_date'),
    expiryDate: date('expiry_date'),
    serialNumbers: text('serial_numbers'),
    lineStatus: goodsReceiptLineStatusEnum('line_status')
      .default(GoodsReceiptLineStatus.PENDING)
      .notNull(),
    rejectionReason: text('rejection_reason'),
    notes: text('notes'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
  },
  (t) => ({
    // Primary index
    ...createBaseEntityBusinessIndexes(t, 'goods_receipt_lines'),
    idIndex: index('goods_receipt_lines_id_index').on(t.id),

    // Foreign key indexes
    receiptIdIndex: index('goods_receipt_lines_receipt_id_index').on(
      t.receiptId,
    poLineIdIndex: index('goods_receipt_lines_po_line_id_index').on(t.poLineId),
    productIdIndex: index('goods_receipt_lines_product_id_index').on(
      t.productId,
    variantIdIndex: index('goods_receipt_lines_variant_id_index').on(
      t.variantId,
    // Performance indexes
    lineStatusIndex: index('goods_receipt_lines_line_status_index').on(
      t.lineStatus,
    batchNumberIndex: index('goods_receipt_lines_batch_number_index').on(
      t.batchNumber,
    manufactureDateIndex: index(
      'goods_receipt_lines_manufacture_date_index',
    ).on(t.manufactureDate),
    expiryDateIndex: index('goods_receipt_lines_expiry_date_index').on(
      t.expiryDate,
    // Composite indexes for common query patterns
    businessReceiptIndex: index(
      'goods_receipt_lines_business_receipt_index',
    ).on(t.businessId, t.receiptId),
    businessLineStatusIndex: index(
      'goods_receipt_lines_business_line_status_index',
    ).on(t.businessId, t.lineStatus),

    // Audit indexes
    createdAtIndex: index('goods_receipt_lines_created_at_index').on(
      t.createdAt,
  }),
);

// Purchase Invoices table
export const purchaseInvoices = pgTable(
  'purchase_invoices',
  {
    ...createBaseEntityBusinessFields(business),
    invoiceNumber: text('invoice_number').notNull(),
    supplierInvoiceNumber: text('supplier_invoice_number').notNull(),
    supplierId: uuid('supplier_id').notNull(), // References SUPPLIERS table
    purchaseOrderId: uuid('purchase_order_id').references(
      () => purchaseOrders.id,
    goodsReceiptId: uuid('goods_receipt_id').references(() => goodsReceipts.id),
    invoiceDate: date('invoice_date').notNull(),
    dueDate: date('due_date'),
    status: purchaseInvoiceStatusEnum('status')
      .default(PurchaseInvoiceStatus.DRAFT)
      .notNull(),
    subtotal: decimal('subtotal', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    taxAmount: decimal('tax_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    discountAmount: decimal('discount_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    shippingAmount: decimal('shipping_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    totalAmount: decimal('total_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    paidAmount: decimal('paid_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    balanceDue: decimal('balance_due', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    paymentTerms: text('payment_terms'),
    currencyCode: text('currency_code').default('USD').notNull(),
    exchangeRate: decimal('exchange_rate', { precision: 10, scale: 6 })
      .default('1.000000')
      .notNull(),
    referenceNumber: text('reference_number'),
    notes: text('notes'),
    verifiedBy: uuid('verified_by'),
    approvedBy: uuid('approved_by'),
    verifiedDate: timestamp('verified_date'),
    approvedDate: timestamp('approved_date'),
  },
  (t) => ({
    // Primary index
    ...createBaseEntityBusinessIndexes(t, 'purchase_invoices'),
    idIndex: index('purchase_invoices_id_index').on(t.id),

    // Foreign key indexes
    supplierIdIndex: index('purchase_invoices_supplier_id_index').on(
      t.supplierId,
    purchaseOrderIdIndex: index('purchase_invoices_purchase_order_id_index').on(
      t.purchaseOrderId,
    goodsReceiptIdIndex: index('purchase_invoices_goods_receipt_id_index').on(
      t.goodsReceiptId,
    verifiedByIndex: index('purchase_invoices_verified_by_index').on(
      t.verifiedBy,
    approvedByIndex: index('purchase_invoices_approved_by_index').on(
      t.approvedBy,
    // Performance indexes
    statusIndex: index('purchase_invoices_status_index').on(t.status),
    invoiceDateIndex: index('purchase_invoices_invoice_date_index').on(
      t.invoiceDate,
    dueDateIndex: index('purchase_invoices_due_date_index').on(t.dueDate),
    verifiedDateIndex: index('purchase_invoices_verified_date_index').on(
      t.verifiedDate,
    approvedDateIndex: index('purchase_invoices_approved_date_index').on(
      t.approvedDate,
    currencyCodeIndex: index('purchase_invoices_currency_code_index').on(
      t.currencyCode,
    // Unique constraints
    uniqueInvoiceNumber: uniqueIndex('purchase_invoices_invoice_number_unique')
      .on(t.businessId, t.invoiceNumber)
      .where(sql`${t.isDeleted} = false`),
    uniqueSupplierInvoiceNumber: uniqueIndex(
      'purchase_invoices_supplier_invoice_number_unique',
    )
      .on(t.businessId, t.supplierId, t.supplierInvoiceNumber)
      .where(sql`${t.isDeleted} = false`),

    // Composite indexes for common query patterns
    businessStatusIndex: index('purchase_invoices_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessSupplierIndex: index('purchase_invoices_business_supplier_index')
      .on(t.businessId, t.supplierId)
      .where(sql`${t.isDeleted} = false`),
    businessDateIndex: index('purchase_invoices_business_date_index')
      .on(t.businessId, t.invoiceDate)
      .where(sql`${t.isDeleted} = false`),

    // Audit indexes
    createdAtIndex: index('purchase_invoices_created_at_index').on(t.createdAt),
    updatedAtIndex: index('purchase_invoices_updated_at_index').on(t.updatedAt),
  }),
);

// Purchase Invoice Lines table
export const purchaseInvoiceLines = pgTable(
  'purchase_invoice_lines',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    invoiceId: uuid('invoice_id')
      .notNull()
      .references(() => purchaseInvoices.id, { onDelete: 'cascade' }),
    poLineId: uuid('po_line_id').references(() => purchaseOrderLines.id),
    receiptLineId: uuid('receipt_line_id').references(
      () => goodsReceiptLines.id,
    productId: uuid('product_id').notNull(), // References PRODUCTS table
    variantId: uuid('variant_id'), // References PRODUCT_VARIANTS table
    description: text('description').notNull(),
    quantity: decimal('quantity', { precision: 15, scale: 3 }).notNull(),
    unitPrice: decimal('unit_price', { precision: 10, scale: 2 }).notNull(),
    discountPercent: decimal('discount_percent', { precision: 5, scale: 2 })
      .default('0.00')
      .notNull(),
    lineSubtotal: decimal('line_subtotal', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    taxAmount: decimal('tax_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    lineTotal: decimal('line_total', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    batchNumber: text('batch_number'),
    serialNumbers: text('serial_numbers'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
  },
  (t) => ({
    // Primary index
    ...createBaseEntityBusinessIndexes(t, 'purchase_invoice_lines'),
    idIndex: index('purchase_invoice_lines_id_index').on(t.id),

    // Foreign key indexes
    invoiceIdIndex: index('purchase_invoice_lines_invoice_id_index').on(
      t.invoiceId,
    poLineIdIndex: index('purchase_invoice_lines_po_line_id_index').on(
      t.poLineId,
    receiptLineIdIndex: index(
      'purchase_invoice_lines_receipt_line_id_index',
    ).on(t.receiptLineId),
    productIdIndex: index('purchase_invoice_lines_product_id_index').on(
      t.productId,
    variantIdIndex: index('purchase_invoice_lines_variant_id_index').on(
      t.variantId,
    // Performance indexes
    batchNumberIndex: index('purchase_invoice_lines_batch_number_index').on(
      t.batchNumber,
    // Composite indexes for common query patterns
    businessInvoiceIndex: index(
      'purchase_invoice_lines_business_invoice_index',
    ).on(t.businessId, t.invoiceId),

    // Audit indexes
    createdAtIndex: index('purchase_invoice_lines_created_at_index').on(
      t.createdAt,
  }),
);

// Purchase Returns table
export const purchaseReturns = pgTable(
  'purchase_returns',
  {
    ...createBaseEntityBusinessFields(business),
    returnNumber: text('return_number').notNull(),
    supplierId: uuid('supplier_id').notNull(), // References SUPPLIERS table
    purchaseOrderId: uuid('purchase_order_id').references(
      () => purchaseOrders.id,
    goodsReceiptId: uuid('goods_receipt_id').references(() => goodsReceipts.id),
    returnDate: date('return_date').notNull(),
    returnType: purchaseReturnTypeEnum('return_type').notNull(),
    status: purchaseReturnStatusEnum('status')
      .default(PurchaseReturnStatus.DRAFT)
      .notNull(),
    returnReason: text('return_reason'),
    subtotal: decimal('subtotal', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    taxAmount: decimal('tax_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    shippingCost: decimal('shipping_cost', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    totalAmount: decimal('total_amount', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    notes: text('notes'),
    requestedBy: uuid('requested_by').notNull(),
    approvedBy: uuid('approved_by'),
    approvedDate: timestamp('approved_date'),
    shippedDate: timestamp('shipped_date'),
  },
  (t) => ({
    // Primary index
    ...createBaseEntityBusinessIndexes(t, 'purchase_returns'),
    idIndex: index('purchase_returns_id_index').on(t.id),

    // Foreign key indexes
    supplierIdIndex: index('purchase_returns_supplier_id_index').on(
      t.supplierId,
    purchaseOrderIdIndex: index('purchase_returns_purchase_order_id_index').on(
      t.purchaseOrderId,
    goodsReceiptIdIndex: index('purchase_returns_goods_receipt_id_index').on(
      t.goodsReceiptId,
    requestedByIndex: index('purchase_returns_requested_by_index').on(
      t.requestedBy,
    approvedByIndex: index('purchase_returns_approved_by_index').on(
      t.approvedBy,
    // Performance indexes
    returnTypeIndex: index('purchase_returns_return_type_index').on(
      t.returnType,
    statusIndex: index('purchase_returns_status_index').on(t.status),
    returnDateIndex: index('purchase_returns_return_date_index').on(
      t.returnDate,
    approvedDateIndex: index('purchase_returns_approved_date_index').on(
      t.approvedDate,
    shippedDateIndex: index('purchase_returns_shipped_date_index').on(
      t.shippedDate,
    // Unique constraint
    uniqueReturnNumber: uniqueIndex('purchase_returns_return_number_unique')
      .on(t.businessId, t.returnNumber)
      .where(sql`${t.isDeleted} = false`),

    // Composite indexes for common query patterns
    businessStatusIndex: index('purchase_returns_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessSupplierIndex: index('purchase_returns_business_supplier_index')
      .on(t.businessId, t.supplierId)
      .where(sql`${t.isDeleted} = false`),
    businessDateIndex: index('purchase_returns_business_date_index')
      .on(t.businessId, t.returnDate)
      .where(sql`${t.isDeleted} = false`),

    // Audit indexes
    createdAtIndex: index('purchase_returns_created_at_index').on(t.createdAt),
    updatedAtIndex: index('purchase_returns_updated_at_index').on(t.updatedAt),
  }),
);

// Purchase Return Lines table
export const purchaseReturnLines = pgTable(
  'purchase_return_lines',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    returnId: uuid('return_id')
      .notNull()
      .references(() => purchaseReturns.id, { onDelete: 'cascade' }),
    receiptLineId: uuid('receipt_line_id')
      .notNull()
      .references(() => goodsReceiptLines.id),
    productId: uuid('product_id').notNull(), // References PRODUCTS table
    variantId: uuid('variant_id'), // References PRODUCT_VARIANTS table
    quantityReturned: decimal('quantity_returned', {
      precision: 15,
      scale: 3,
    }).notNull(),
    unitPrice: decimal('unit_price', { precision: 10, scale: 2 }).notNull(),
    lineTotal: decimal('line_total', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    batchNumber: text('batch_number'),
    serialNumbers: text('serial_numbers'),
    returnReason: text('return_reason'),
    conditionOnReturn: text('condition_on_return'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
  },
  (t) => ({
    // Primary index
    ...createBaseEntityBusinessIndexes(t, 'purchase_return_lines'),
    idIndex: index('purchase_return_lines_id_index').on(t.id),

    // Foreign key indexes
    returnIdIndex: index('purchase_return_lines_return_id_index').on(
      t.returnId,
    receiptLineIdIndex: index('purchase_return_lines_receipt_line_id_index').on(
      t.receiptLineId,
    productIdIndex: index('purchase_return_lines_product_id_index').on(
      t.productId,
    variantIdIndex: index('purchase_return_lines_variant_id_index').on(
      t.variantId,
    // Performance indexes
    batchNumberIndex: index('purchase_return_lines_batch_number_index').on(
      t.batchNumber,
    // Composite indexes for common query patterns
    businessReturnIndex: index(
      'purchase_return_lines_business_return_index',
    ).on(t.businessId, t.returnId),

    // Audit indexes
    createdAtIndex: index('purchase_return_lines_created_at_index').on(
      t.createdAt,
  }),
);

// Supplier Payments table
export const supplierPayments = pgTable(
  'supplier_payments',
  {
    ...createBaseEntityBusinessFields(business),
    supplierId: uuid('supplier_id').notNull(), // References SUPPLIERS table
    invoiceId: uuid('invoice_id')
      .notNull()
      .references(() => purchaseInvoices.id),
    paymentNumber: text('payment_number').notNull(),
    paymentDate: date('payment_date').notNull(),
    paymentMethod: supplierPaymentMethodEnum('payment_method').notNull(),
    amount: decimal('amount', { precision: 15, scale: 2 }).notNull(),
    referenceNumber: text('reference_number'),
    bankDetails: text('bank_details'),
    status: supplierPaymentStatusEnum('status')
      .default(SupplierPaymentStatus.PENDING)
      .notNull(),
    notes: text('notes'),
  },
  (t) => ({
    // Primary index
    ...createBaseEntityBusinessIndexes(t, 'supplier_payments'),
    idIndex: index('supplier_payments_id_index').on(t.id),

    // Foreign key indexes
    supplierIdIndex: index('supplier_payments_supplier_id_index').on(
      t.supplierId,
    invoiceIdIndex: index('supplier_payments_invoice_id_index').on(t.invoiceId),

    // Performance indexes
    paymentMethodIndex: index('supplier_payments_payment_method_index').on(
      t.paymentMethod,
    statusIndex: index('supplier_payments_status_index').on(t.status),
    paymentDateIndex: index('supplier_payments_payment_date_index').on(
      t.paymentDate,
    referenceNumberIndex: index('supplier_payments_reference_number_index').on(
      t.referenceNumber,
    // Unique constraint
    uniquePaymentNumber: uniqueIndex('supplier_payments_payment_number_unique')
      .on(t.businessId, t.paymentNumber)
      .where(sql`${t.isDeleted} = false`),

    // Composite indexes for common query patterns
    businessSupplierIndex: index('supplier_payments_business_supplier_index')
      .on(t.businessId, t.supplierId)
      .where(sql`${t.isDeleted} = false`),
    businessStatusIndex: index('supplier_payments_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessDateIndex: index('supplier_payments_business_date_index')
      .on(t.businessId, t.paymentDate)
      .where(sql`${t.isDeleted} = false`),

    // Audit indexes
    createdAtIndex: index('supplier_payments_created_at_index').on(t.createdAt),
    updatedAtIndex: index('supplier_payments_updated_at_index').on(t.updatedAt),
  }),
);
