import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  varchar,
  decimal,
  boolean,
  integer,
  check,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import { categories } from './categories.schema';
import { accounts } from './accounts.schema';
import { taxes } from './taxes.schema';
import { media } from './media.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { StatusType } from '../../shared/types';

export const restaurantMenuItemStatusEnum = pgEnum(
  'restaurant_menu_item_status',
  [StatusType.ACTIVE, StatusType.INACTIVE],
);

export const restaurantMenuItems = pgTable(
  'restaurant_menu_items',
  {
    ...createBaseEntityBusinessFields(business),

    // Basic Info
    itemName: varchar('item_name', { length: 200 }).notNull(),
    description: text('description'),

    // Pricing
    price: decimal('price', { precision: 10, scale: 2 }).notNull(),
    cost: decimal('cost', { precision: 10, scale: 2 }),

    // Preparation and nutritional info
    preparationTime: integer('preparation_time'), // in minutes
    calories: integer('calories'),

    // Dietary flags
    isVegetarian: boolean('is_vegetarian').default(false).notNull(),
    isVegan: boolean('is_vegan').default(false).notNull(),
    isGlutenFree: boolean('is_gluten_free').default(false).notNull(),
    isSpicy: boolean('is_spicy').default(false).notNull(),
    spiceLevel: integer('spice_level'), // 0-5 scale

    // Featured flag
    isFeatured: boolean('is_featured').default(false).notNull(),

    // Foreign Key References
    categoryId: uuid('category_id')
      .notNull()
      .references(() => categories.id),

    // Business accounting fields
    priceRate: decimal('price_rate', { precision: 15, scale: 2 }),
    incomeAccountId: uuid('income_account_id').references(() => accounts.id),
    salesTaxId: uuid('sales_tax_id').references(() => taxes.id),
    expenseAccountId: uuid('expense_account_id').references(() => accounts.id),

    // Media references
    ogImage: uuid('og_image').references(() => media.id),

    // SEO fields
    seoTitle: text('seo_title'),
    seoDescription: text('seo_description'),
    seoKeywords: text('seo_keywords').array(),

    // Status field
    status: restaurantMenuItemStatusEnum('status')
      .default(StatusType.ACTIVE)
      .notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'restaurant_menu_items'),
    // Primary indexes
    itemNameIndex: index('restaurant_menu_items_item_name_index').on(
      t.itemName,
    ),
    categoryIdIndex: index('restaurant_menu_items_category_id_index').on(
      t.categoryId,
    ),
    statusIndex: index('restaurant_menu_items_status_index').on(t.status),

    // Dietary and feature indexes
    isVegetarianIndex: index('restaurant_menu_items_is_vegetarian_index').on(
      t.isVegetarian,
    ),
    isVeganIndex: index('restaurant_menu_items_is_vegan_index').on(t.isVegan),
    isGlutenFreeIndex: index('restaurant_menu_items_is_gluten_free_index').on(
      t.isGlutenFree,
    ),
    isSpicyIndex: index('restaurant_menu_items_is_spicy_index').on(t.isSpicy),
    isFeaturedIndex: index('restaurant_menu_items_is_featured_index').on(
      t.isFeatured,
    ),

    // Pricing indexes
    priceIndex: index('restaurant_menu_items_price_index').on(t.price),

    // Account and tax indexes
    incomeAccountIdIndex: index(
      'restaurant_menu_items_income_account_id_index',
    ).on(t.incomeAccountId),
    salesTaxIdIndex: index('restaurant_menu_items_sales_tax_id_index').on(
      t.salesTaxId,
    ),
    expenseAccountIdIndex: index(
      'restaurant_menu_items_expense_account_id_index',
    ).on(t.expenseAccountId),

    // Media indexes
    ogImageIndex: index('restaurant_menu_items_og_image_index').on(t.ogImage),

    // Composite indexes for filtering and sorting
    businessCategoryIndex: index(
      'restaurant_menu_items_business_category_index',
    )
      .on(t.businessId, t.categoryId)
      .where(sql`${t.isDeleted} = false`),
    businessStatusIndex: index('restaurant_menu_items_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessFeaturedIndex: index(
      'restaurant_menu_items_business_featured_index',
    )
      .on(t.businessId, t.isFeatured)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints
    uniqueBusinessItemName: uniqueIndex(
      'restaurant_menu_items_business_item_name_unique',
    )
      .on(t.businessId, t.itemName)
      .where(sql`${t.isDeleted} = false`),

    // Check constraints
    spiceLevelCheck: check(
      'restaurant_menu_items_spice_level_check',
      sql`spice_level >= 0 AND spice_level <= 5`,
    ),
  }),
);
