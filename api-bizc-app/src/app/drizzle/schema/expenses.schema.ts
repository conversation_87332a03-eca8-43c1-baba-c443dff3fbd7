import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  decimal,
  integer,
  date,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import { accounts } from './accounts.schema';
import { paymentMethods } from './payment-methods.schema';
import { taxes } from './taxes.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

// Enums
export enum AmountType {
  EXCLUSIVE_OF_TAX = 'Exclusive of Tax',
  INCLUSIVE_OF_TAX = 'Inclusive of Tax',
  OUT_OF_SCOPE = 'Out Of Scope',
}

export enum ExpenseStatus {
  DRAFT = 'Draft',
  SUBMITTED = 'Submitted',
  APPROVED = 'Approved',
  REJECTED = 'Rejected',
}

export enum PayeeType {
  SUPPLIER = 'Supplier',
  STAFF = 'Staff',
  CUSTOMER = 'Customer',
  PROJECT = 'Project',
}

export const amountTypeEnum = pgEnum('amount_type', [
  AmountType.EXCLUSIVE_OF_TAX,
  AmountType.INCLUSIVE_OF_TAX,
  AmountType.OUT_OF_SCOPE,
]);

export const expenseStatusEnum = pgEnum('expense_status', [
  ExpenseStatus.DRAFT,
  ExpenseStatus.SUBMITTED,
  ExpenseStatus.APPROVED,
  ExpenseStatus.REJECTED,
]);

export const payeeTypeEnum = pgEnum('payee_type', [
  PayeeType.SUPPLIER,
  PayeeType.STAFF,
  PayeeType.CUSTOMER,
  PayeeType.PROJECT,
]);

// Main expenses table
export const expenses = pgTable(
  'expenses',
  {
    ...createBaseEntityBusinessFields(business),

    // Payee information (polymorphic relationship)
    payeeType: payeeTypeEnum('payee_type').notNull(),
    payeeId: uuid('payee_id').notNull(), // References suppliers, staff, customers, or projects

    // Payment information
    paymentAccountId: uuid('payment_account_id')
      .notNull()
      .references(() => accounts.id),
    paymentDate: date('payment_date').notNull(),
    paymentMethodId: uuid('payment_method_id')
      .notNull()
      .references(() => paymentMethods.id),
    referenceNumber: text('reference_number'),

    // Amount calculation
    amountType: amountTypeEnum('amount_type')
      .default(AmountType.EXCLUSIVE_OF_TAX)
      .notNull(),

    // Totals
    subtotal: decimal('subtotal', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    total: decimal('total', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),

    // Additional information
    memo: text('memo'),

    // Status
    status: expenseStatusEnum('status').default(ExpenseStatus.DRAFT).notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'expenses'),
    payeeTypeIndex: index('expenses_payee_type_index').on(t.payeeType),
    payeeIdIndex: index('expenses_payee_id_index').on(t.payeeId),
    paymentAccountIdIndex: index('expenses_payment_account_id_index').on(
      t.paymentAccountId,
    ),
    paymentDateIndex: index('expenses_payment_date_index').on(t.paymentDate),
    paymentMethodIdIndex: index('expenses_payment_method_id_index').on(
      t.paymentMethodId,
    ),
    statusIndex: index('expenses_status_index').on(t.status),
    referenceNumberIndex: index('expenses_reference_number_index').on(
      t.referenceNumber,
    ),

    // Composite indexes for efficient querying
    businessPayeeIndex: index('expenses_business_payee_index')
      .on(t.businessId, t.payeeType, t.payeeId)
      .where(sql`${t.isDeleted} = false`),
    businessStatusIndex: index('expenses_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessDateIndex: index('expenses_business_date_index')
      .on(t.businessId, t.paymentDate)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints
    uniqueBusinessReferenceNumber: uniqueIndex(
      'expenses_business_reference_number_unique',
    )
      .on(t.businessId, t.referenceNumber)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Category line items table
export const expenseCategoryLineItems = pgTable(
  'expense_category_line_items',
  {
    ...createBaseEntityBusinessFields(business),
    expenseId: uuid('expense_id')
      .notNull()
      .references(() => expenses.id, { onDelete: 'cascade' }),
    categoryId: uuid('category_id')
      .notNull()
      .references(() => accounts.id), // References expense accounts/categories
    description: text('description'),
    amount: decimal('amount', { precision: 15, scale: 2 }).notNull(),
    taxId: uuid('tax_id').references(() => taxes.id),

    // Line item order
    lineOrder: integer('line_order').default(0).notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'expense_category_line_items'),
    expenseIdIndex: index('expense_category_line_items_expense_id_index').on(
      t.expenseId,
    ),
    categoryIdIndex: index('expense_category_line_items_category_id_index').on(
      t.categoryId,
    ),
    taxIdIndex: index('expense_category_line_items_tax_id_index').on(t.taxId),
    lineOrderIndex: index('expense_category_line_items_line_order_index').on(
      t.lineOrder,
    ),
  }),
);

// Product/Service line items table
export const expenseProductServiceLineItems = pgTable(
  'expense_product_service_line_items',
  {
    ...createBaseEntityBusinessFields(business),
    expenseId: uuid('expense_id')
      .notNull()
      .references(() => expenses.id, { onDelete: 'cascade' }),

    // Product or Service reference (polymorphic)
    itemType: text('item_type').notNull(), // 'product' or 'service'
    itemId: uuid('item_id').notNull(), // References products or services

    description: text('description'),
    quantity: decimal('quantity', { precision: 15, scale: 4 })
      .default('1.0000')
      .notNull(),
    rate: decimal('rate', { precision: 15, scale: 2 }).notNull(),
    amount: decimal('amount', { precision: 15, scale: 2 }).notNull(),
    taxId: uuid('tax_id').references(() => taxes.id),

    // Line item order
    lineOrder: integer('line_order').default(0).notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'expense_product_service_line_items'),
    expenseIdIndex: index(
      'expense_product_service_line_items_expense_id_index',
    ).on(t.expenseId),
    itemTypeIndex: index(
      'expense_product_service_line_items_item_type_index',
    ).on(t.itemType),
    itemIdIndex: index('expense_product_service_line_items_item_id_index').on(
      t.itemId,
    ),
    taxIdIndex: index('expense_product_service_line_items_tax_id_index').on(
      t.taxId,
    ),
    lineOrderIndex: index(
      'expense_product_service_line_items_line_order_index',
    ).on(t.lineOrder),

    // Composite index for item lookup
    itemTypeIdIndex: index(
      'expense_product_service_line_items_item_type_id_index',
    ).on(t.itemType, t.itemId),
  }),
);
