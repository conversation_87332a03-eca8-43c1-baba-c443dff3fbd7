import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  integer,
  decimal,
  jsonb,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import { media } from './media.schema';
import { reservationTypes } from './reservation-types.schema';
import { accounts } from './accounts.schema';
import { taxes } from './taxes.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { assets } from './assets.schema';
import {
  BedType,
  ViewType,
  RoomStatus,
  AccommodationUnitStatus,
  TaxType,
} from '../../shared/types';
import { AccommodationUnitFeatures } from '@shared/types';

// Bed Type Enum
export const bedTypeEnum = pgEnum('bed_type', [
  BedType.SINGLE,
  BedType.DOUBLE,
  BedType.QUEEN,
  BedType.KING,
  BedType.TWIN,
  BedType.SOFA_BED,
  BedType.BUNK_BED,
  BedType.MURPHY_BED,
  BedType.DAYBED,
  BedType.FUTON,
]);

// View Type Enum
export const viewTypeEnum = pgEnum('view_type', [
  ViewType.OCEAN_VIEW,
  ViewType.MOUNTAIN_VIEW,
  ViewType.CITY_VIEW,
  ViewType.GARDEN_VIEW,
  ViewType.POOL_VIEW,
  ViewType.COURTYARD_VIEW,
  ViewType.STREET_VIEW,
  ViewType.INTERIOR_VIEW,
  ViewType.PARTIAL_OCEAN_VIEW,
  ViewType.PARTIAL_MOUNTAIN_VIEW,
  ViewType.BALCONY_VIEW,
  ViewType.NO_VIEW,
]);

// Room Status Enum
export const roomStatusEnum = pgEnum('room_status', [
  RoomStatus.AVAILABLE,
  RoomStatus.OCCUPIED,
  RoomStatus.RESERVED,
  RoomStatus.OUT_OF_ORDER,
  RoomStatus.MAINTENANCE,
  RoomStatus.CLEANING,
  RoomStatus.DIRTY,
  RoomStatus.INSPECTED,
  RoomStatus.BLOCKED,
]);

// Accommodation Unit Status Enum
export const accommodationUnitStatusEnum = pgEnum('accommodation_unit_status', [
  AccommodationUnitStatus.ACTIVE,
  AccommodationUnitStatus.INACTIVE,
]);

// Tax Type Enum (reused from products)
export const taxTypeEnum = pgEnum('tax_type', [
  TaxType.INCLUSIVE,
  TaxType.EXCLUSIVE,
  TaxType.OUT_OF_SCOPE,
]);

// Accommodation Units table
export const accommodationUnits = pgTable(
  'accommodation_units',
  {
    ...createBaseEntityBusinessFields(business),

    // Basic unit information
    roomNumber: text('room_number'),
    name: text('name'), // Optional custom name (e.g., "Ocean View Suite")

    // Type references to reservation-types
    type: uuid('type')
      .notNull()
      .references(() => reservationTypes.id),
    subType: uuid('sub_type').references(() => reservationTypes.id),

    // Room details
    status: roomStatusEnum('status').default(RoomStatus.AVAILABLE).notNull(),
    floor: integer('floor').notNull(),
    building: text('building'), // For hotels with multiple buildings
    description: text('description'),

    // Bed and view information
    bedType: bedTypeEnum('bed_type').notNull(),
    viewType: viewTypeEnum('view_type'),

    // Features and amenities
    features: jsonb('features')
      .$type<AccommodationUnitFeatures>()
      .default({})
      .notNull(),

    // Occupancy limits
    maxAdults: integer('max_adults').notNull(),
    maxChildren: integer('max_children').notNull(),

    // Pricing information
    basePrice: decimal('base_price', { precision: 12, scale: 2 })
      .default('0.00')
      .notNull(),
    baseWeekendRate: decimal('base_weekend_rate', { precision: 12, scale: 2 })
      .default('0.00')
      .notNull(),
    baseWeeklyRate: decimal('base_weekly_rate', { precision: 12, scale: 2 })
      .default('0.00')
      .notNull(),
    baseMonthlyRate: decimal('base_monthly_rate', { precision: 12, scale: 2 })
      .default('0.00')
      .notNull(),
    standardCost: decimal('standard_cost', { precision: 12, scale: 2 })
      .default('0.00')
      .notNull(),

    // Account references
    incomeAccountId: uuid('income_account_id')
      .notNull()
      .references(() => accounts.id),
    expenseAccountId: uuid('expense_account_id')
      .notNull()
      .references(() => accounts.id),
    assetAccountId: uuid('asset_account_id').references(() => accounts.id),

    // Asset reference
    assetId: uuid('asset_id').references(() => assets.id),

    // SEO fields
    seoTitle: text('seo_title'),
    seoDescription: text('seo_description'),
    seoKeywords: text('seo_keywords').array(),
    ogImage: uuid('og_image').references(() => media.id),

    // Position for ordering
    typePosition: integer('category_position').default(0).notNull(),
    subTypePosition: integer('sub_category_position').default(0).notNull(),
    globalPosition: integer('global_position').default(0).notNull(),

    // Tax information
    taxType: taxTypeEnum('tax_type').default(TaxType.INCLUSIVE).notNull(),
    defaultTaxRateId: uuid('default_tax_rate_id').references(() => taxes.id),

    // System status
    systemStatus: accommodationUnitStatusEnum('system_status')
      .default(AccommodationUnitStatus.ACTIVE)
      .notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'accommodation_units'),
    // Indexes for performance
    businessIdIndex: index('accommodation_units_business_id_index').on(
      t.businessId,
    ),
    roomNumberIndex: index('accommodation_units_room_number_index').on(
      t.roomNumber,
    ),
    typeIndex: index('accommodation_units_type_index').on(t.type),
    subTypeIndex: index('accommodation_units_sub_type_index').on(t.subType),
    statusIndex: index('accommodation_units_status_index').on(t.status),
    systemStatusIndex: index('accommodation_units_system_status_index').on(
      t.systemStatus,
    ),
    floorIndex: index('accommodation_units_floor_index').on(t.floor),
    buildingIndex: index('accommodation_units_building_index').on(t.building),
    bedTypeIndex: index('accommodation_units_bed_type_index').on(t.bedType),
    viewTypeIndex: index('accommodation_units_view_type_index').on(t.viewType),
    maxAdultsIndex: index('accommodation_units_max_adults_index').on(
      t.maxAdults,
    ),
    maxChildrenIndex: index('accommodation_units_max_children_index').on(
      t.maxChildren,
    ),
    assetAccountIdIndex: index('accommodation_units_asset_account_id_index').on(
      t.assetAccountId,
    ),
    assetIdIndex: index('accommodation_units_asset_id_index').on(t.assetId),
    typePositionIndex: index('accommodation_units_type_position_index').on(
      t.typePosition,
    ),
    subTypePositionIndex: index(
      'accommodation_units_sub_type_position_index',
    ).on(t.subTypePosition),
    globalPositionIndex: index('accommodation_units_global_position_index').on(
      t.globalPosition,
    ),
    incomeAccountIdIndex: index(
      'accommodation_units_income_account_id_index',
    ).on(t.incomeAccountId),
    expenseAccountIdIndex: index(
      'accommodation_units_expense_account_id_index',
    ).on(t.expenseAccountId),
    defaultTaxRateIdIndex: index(
      'accommodation_units_default_tax_rate_id_index',
    ).on(t.defaultTaxRateId),

    // Unique constraints with soft deletion support
    uniqueBusinessRoomNumber: uniqueIndex(
      'accommodation_units_business_room_number_unique',
    )
      .on(t.businessId, t.roomNumber)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessName: uniqueIndex('accommodation_units_business_name_unique')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),

    // Composite indexes for common queries
    businessStatusIndex: index('accommodation_units_business_status_index').on(
      t.businessId,
      t.status,
    ),
    businessTypeStatusIndex: index(
      'accommodation_units_business_type_status_index',
    ).on(t.businessId, t.type, t.status),
    businessSystemStatusIndex: index(
      'accommodation_units_business_system_status_index',
    ).on(t.businessId, t.systemStatus),
  }),
);
