import {
  index,
  pgEnum,
  pgTable,
  text,
  timestamp,
  boolean,
  uuid,
  uniqueIndex,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { users } from './users.schema';
import { business } from './business.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import {
  BankAccountType,
  CurrencyCode,
  BankAccountEntityType,
} from '../../shared/types';

export const bankAccountTypeEnum = pgEnum('bank_account_type', [
  BankAccountType.CHECKING,
  BankAccountType.SAVINGS,
]);

export const currencyCodeEnum = pgEnum('currency_code', [
  // Major currencies (your existing ones)
  CurrencyCode.USD,
  CurrencyCode.EUR,
  CurrencyCode.GBP,
  CurrencyCode.JPY,
  CurrencyCode.AUD,
  CurrencyCode.CAD,
  CurrencyCode.CHF,
  CurrencyCode.CNY,
  CurrencyCode.INR,
  CurrencyCode.LKR,

  // Additional major currencies
  CurrencyCode.KRW,
  CurrencyCode.SGD,
  CurrencyCode.HKD,
  CurrencyCode.NOK,
  CurrencyCode.SEK,
  CurrencyCode.DKK,
  CurrencyCode.PLN,
  CurrencyCode.CZK,
  CurrencyCode.HUF,
  CurrencyCode.RUB,
  CurrencyCode.BRL,
  CurrencyCode.MXN,
  CurrencyCode.ZAR,
  CurrencyCode.TRY,
  CurrencyCode.ILS,
  CurrencyCode.THB,
  CurrencyCode.MYR,
  CurrencyCode.IDR,
  CurrencyCode.PHP,
  CurrencyCode.VND,

  // Africa
  CurrencyCode.EGP,
  CurrencyCode.NGN,
  CurrencyCode.KES,
  CurrencyCode.GHS,
  CurrencyCode.MAD,
  CurrencyCode.TND,
  CurrencyCode.ETB,
  CurrencyCode.UGX,
  CurrencyCode.TZS,
  CurrencyCode.ZMW,
  CurrencyCode.BWP,
  CurrencyCode.XOF,
  CurrencyCode.XAF,

  // Middle East
  CurrencyCode.AED,
  CurrencyCode.SAR,
  CurrencyCode.QAR,
  CurrencyCode.KWD,
  CurrencyCode.BHD,
  CurrencyCode.OMR,
  CurrencyCode.JOD,
  CurrencyCode.LBP,
  CurrencyCode.IRR,
  CurrencyCode.IQD,

  // Asia Pacific
  CurrencyCode.PKR,
  CurrencyCode.BDT,
  CurrencyCode.NPR,
  CurrencyCode.AFN,
  CurrencyCode.MMK,
  CurrencyCode.LAK,
  CurrencyCode.KHR,
  CurrencyCode.BND,
  CurrencyCode.TWD,
  CurrencyCode.MOP,
  CurrencyCode.FJD,
  CurrencyCode.PGK,
  CurrencyCode.WST,
  CurrencyCode.TOP,
  CurrencyCode.VUV,
  CurrencyCode.SBD,

  // Europe
  CurrencyCode.RON,
  CurrencyCode.BGN,
  CurrencyCode.HRK,
  CurrencyCode.RSD,
  CurrencyCode.BAM,
  CurrencyCode.MKD,
  CurrencyCode.ALL,
  CurrencyCode.ISK,
  CurrencyCode.MDL,
  CurrencyCode.UAH,
  CurrencyCode.BYN,
  CurrencyCode.GEL,
  CurrencyCode.AMD,
  CurrencyCode.AZN,

  // Americas
  CurrencyCode.ARS,
  CurrencyCode.CLP,
  CurrencyCode.COP,
  CurrencyCode.PEN,
  CurrencyCode.UYU,
  CurrencyCode.PYG,
  CurrencyCode.BOB,
  CurrencyCode.VES,
  CurrencyCode.GYD,
  CurrencyCode.SRD,
  CurrencyCode.TTD,
  CurrencyCode.JMD,
  CurrencyCode.BBD,
  CurrencyCode.BSD,
  CurrencyCode.BZD,
  CurrencyCode.GTQ,
  CurrencyCode.HNL,
  CurrencyCode.NIO,
  CurrencyCode.CRC,
  CurrencyCode.PAB,
  CurrencyCode.DOP,
  CurrencyCode.HTG,
  CurrencyCode.CUP,

  // Central Asia
  CurrencyCode.KZT,
  CurrencyCode.UZS,
  CurrencyCode.KGS,
  CurrencyCode.TJS,
  CurrencyCode.TMT,
  CurrencyCode.MNT,

  // Pacific
  CurrencyCode.NZD,
  CurrencyCode.XPF,

  // Special/Commodity currencies
  CurrencyCode.XAU,
  CurrencyCode.XAG,
  CurrencyCode.XPT,
  CurrencyCode.XPD,
]);

export const bankAccountEntityTypeEnum = pgEnum('bank_account_entity_type', [
  BankAccountEntityType.CUSTOMER,
  BankAccountEntityType.SUPPLIER,
  BankAccountEntityType.STAFF,
  BankAccountEntityType.BUSINESS,
]);

export const bankAccounts = pgTable(
  'bank_accounts',
  {
    ...createBaseEntityBusinessFields(business),
    entityId: uuid('entity_id'), // ID of the entity (customer, supplier, staff, etc.)
    entityType: bankAccountEntityTypeEnum('entity_type')
      .default(BankAccountEntityType.BUSINESS)
      .notNull(),
    bankName: text('bank_name').notNull(),
    accountHolderName: text('account_holder_name').notNull(),
    branchName: text('branch_name'),
    bankCode: text('bank_code'),
    branchCode: text('branch_code'),
    accountName: text('account_name'),
    accountNumber: text('account_number').notNull(),
    routingNumber: text('routing_number'),
    iban: text('iban'),
    swiftCode: text('swift_code'),
    accountType: bankAccountTypeEnum('account_type')
      .default(BankAccountType.CHECKING)
      .notNull(),
    currency: currencyCodeEnum('currency').default(CurrencyCode.USD).notNull(),
    isPrimary: boolean('is_primary').default(false).notNull(),
    isActive: boolean('is_active').default(true).notNull(),
    deletedBy: uuid('deleted_by').references(() => users.id),
    deletedAt: timestamp('deleted_at'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'bank_accounts'),
    entityIdIndex: index('bank_account_entity_id_index').on(t.entityId),
    entityTypeIndex: index('bank_account_entity_type_index').on(t.entityType),
    // Unique constraint for bank name + account number within business (considering soft deletion)
    uniqueBankAccountIndex: uniqueIndex('bank_account_unique_index').on(
      t.businessId,
      t.bankName,
      t.accountNumber,
      t.deletedAt,
    ),
    // Unique constraint for primary account per entity type and entity ID (considering soft deletion)
    uniquePrimaryEntityIndex: uniqueIndex('bank_account_primary_entity_index')
      .on(t.businessId, t.entityType, t.entityId, t.isPrimary, t.deletedAt)
      .where(sql`${t.isPrimary} = true`),
  }),
);
