import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  varchar,
  decimal,
  boolean,
  integer,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import { serviceCategories } from './service-categories.schema';
import { accounts } from './accounts.schema';
import { taxes } from './taxes.schema';
import { suppliers } from './suppliers.schema';
import { staffMembers } from './staff.schema';
import { locations } from './locations.schema';
import { media } from './media.schema';
import {
  locationAllocationFields,
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { ServiceStatus } from '../../shared/types';

export const serviceStatusEnum = pgEnum('service_status', [
  ServiceStatus.ACTIVE,
  ServiceStatus.INACTIVE,
]);

export const services = pgTable(
  'services',
  {
    ...createBaseEntityBusinessFields(business),

    // Basic Info
    name: text('name').notNull(),
    itemType: text('item_type').notNull().default('service'),
    sku: varchar('sku', { length: 100 }).notNull(),
    serviceCategoryId: uuid('service_category_id')
      .notNull()
      .references(() => serviceCategories.id),

    // Sales
    sellToCustomers: boolean('sell_to_customers').default(true).notNull(),
    description: text('description'),
    priceRate: decimal('price_rate', { precision: 15, scale: 2 }).notNull(),
    incomeAccountId: uuid('income_account_id')
      .notNull()
      .references(() => accounts.id),
    salesTaxId: uuid('sales_tax_id')
      .notNull()
      .references(() => taxes.id),

    // Purchasing
    purchaseFromSupplier: boolean('purchase_from_supplier')
      .default(false)
      .notNull(),
    purchaseDescription: text('purchase_description'),
    purchaseCost: decimal('purchase_cost', { precision: 15, scale: 2 }),
    expenseAccountId: uuid('expense_account_id').references(() => accounts.id),
    preferredSupplierId: uuid('preferred_supplier_id').references(
      () => suppliers.id,
    ),

    // Service-specific fields
    availableOnline: boolean('available_online').default(false).notNull(),
    position: integer('position').default(0).notNull(),

    // SEO fields
    seoTitle: text('seo_title'),
    seoDescription: text('seo_description'),
    seoKeywords: text('seo_keywords').array(),
    ogImage: uuid('og_image').references(() => media.id),

    // Location allocation (using reusable fields)
    ...locationAllocationFields,

    // Status field
    status: serviceStatusEnum('status').default(ServiceStatus.ACTIVE).notNull(),
  },
  (t) => ({
    idIndex: index('services_id_index').on(t.id),
    ...createBaseEntityBusinessIndexes(t, 'services'),
    nameIndex: index('services_name_index').on(t.name),
    skuIndex: index('services_sku_index').on(t.sku),
    serviceCategoryIdIndex: index('services_service_category_id_index').on(
      t.serviceCategoryId,
    ),
    incomeAccountIdIndex: index('services_income_account_id_index').on(
      t.incomeAccountId,
    ),
    salesTaxIdIndex: index('services_sales_tax_id_index').on(t.salesTaxId),
    expenseAccountIdIndex: index('services_expense_account_id_index').on(
      t.expenseAccountId,
    ),
    preferredSupplierIdIndex: index('services_preferred_supplier_id_index').on(
      t.preferredSupplierId,
    ),
    positionIndex: index('services_position_index').on(t.position),
    ogImageIndex: index('services_og_image_index').on(t.ogImage),

    // Optimized composite indexes for position-based sorting performance
    businessPositionIndex: index('services_business_position_index')
      .on(t.businessId, t.position, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessStatusPositionIndex: index(
      'services_business_status_position_index',
    )
      .on(t.businessId, t.status, t.position, t.id)
      .where(sql`${t.isDeleted} = false`),

    uniqueBusinessSku: uniqueIndex('services_business_sku_unique').on(
      t.businessId,
      t.sku,
    ),
  }),
);

// Junction table for service-staff relationships (many-to-many relationship)
export const serviceStaff = pgTable(
  'service_staff',
  {
    ...createBaseEntityBusinessFields(business),
    serviceId: uuid('service_id')
      .notNull()
      .references(() => services.id, { onDelete: 'cascade' }),
    staffMemberId: uuid('staff_member_id')
      .notNull()
      .references(() => staffMembers.id, { onDelete: 'cascade' }),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'service_staff'),
    serviceIdIndex: index('service_staff_service_id_index').on(t.serviceId),
    staffMemberIdIndex: index('service_staff_staff_member_id_index').on(
      t.staffMemberId,
    ),
    uniqueServiceStaff: uniqueIndex('service_staff_unique').on(
      t.serviceId,
      t.staffMemberId,
    ),
  }),
);

// Junction table for service locations (many-to-many relationship)
export const serviceLocations = pgTable(
  'service_locations',
  {
    ...createBaseEntityBusinessFields(business),
    serviceId: uuid('service_id')
      .notNull()
      .references(() => services.id, { onDelete: 'cascade' }),
    locationId: uuid('location_id')
      .notNull()
      .references(() => locations.id, { onDelete: 'cascade' }),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'service_locations'),
    serviceIdIndex: index('service_locations_service_id_index').on(t.serviceId),
    locationIdIndex: index('service_locations_location_id_index').on(
      t.locationId,
    ),
    uniqueServiceLocation: uniqueIndex('service_locations_unique').on(
      t.serviceId,
      t.locationId,
    ),
  }),
);
