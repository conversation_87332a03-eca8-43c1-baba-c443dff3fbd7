import { pgEnum, pgTable, uuid, index } from 'drizzle-orm/pg-core';
import { users } from './users.schema';
import { business } from './business.schema';
import { businessRoles } from './business-roles.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

export const userBusinessRoleStatusEnum = pgEnum('user_business_role_status', [
  'active',
  'inactive',
  'deleted',
]);

export const userBusinessRoles = pgTable(
  'user_business_roles',
  {
    ...createBaseEntityBusinessFields(business),
    userId: uuid('user_id')
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),
    roleId: uuid('role_id')
      .notNull()
      .references(() => businessRoles.id, { onDelete: 'cascade' }),
    assignedBy: uuid('assigned_by')
      .notNull()
      .references(() => users.id),
    status: userBusinessRoleStatusEnum('status').default('active').notNull(),
  },
  (t) => ({
    // Standard indexes for base entity fields
    ...createBaseEntityBusinessIndexes(t, 'user_business_roles'),

    // Entity-specific indexes
    userIdIndex: index('user_business_roles_user_id_index').on(t.userId),
    roleIdIndex: index('user_business_roles_role_id_index').on(t.roleId),
    assignedByIndex: index('user_business_roles_assigned_by_index').on(
      t.assignedBy,
    ),
    statusIndex: index('user_business_roles_status_index').on(t.status),

    // Composite indexes for performance
    businessUserIndex: index('user_business_roles_business_user_index').on(
      t.businessId,
      t.userId,
    ),
    businessRoleIndex: index('user_business_roles_business_role_index').on(
      t.businessId,
      t.roleId,
    ),
    userRoleIndex: index('user_business_roles_user_role_index').on(
      t.userId,
      t.roleId,
    ),
  }),
);
