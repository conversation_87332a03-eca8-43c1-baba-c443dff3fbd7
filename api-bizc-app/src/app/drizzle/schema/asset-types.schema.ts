import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { CategoryStatus } from '../../shared/types';

export const assetTypeStatusEnum = pgEnum('asset_type_status', [
  CategoryStatus.ACTIVE,
  CategoryStatus.INACTIVE,
]);

export const assetTypes = pgTable(
  'asset_types',
  {
    ...createBaseEntityBusinessFields(business),
    name: text('name').notNull(),
    description: text('description'),
    parentId: uuid('parent_id').references(() => assetTypes.id),

    // Status
    status: assetTypeStatusEnum('status')
      .default(CategoryStatus.ACTIVE)
      .notNull(),
  },
  (t) => ({
    // Standard indexes for base entity fields
    ...createBaseEntityBusinessIndexes(t, 'asset_types'),

    // Entity-specific indexes
    nameIndex: index('asset_types_name_index').on(t.name),
    parentIdIndex: index('asset_types_parent_id_index').on(t.parentId),
    statusIndex: index('asset_types_status_index').on(t.status),

    // Composite indexes for performance
    businessNameIndex: index('asset_types_business_name_index').on(
      t.businessId,
      t.name,
    ),
    businessStatusIndex: index('asset_types_business_status_index').on(
      t.businessId,
      t.status,
    ),

    // Unique constraints
    uniqueBusinessName: uniqueIndex('asset_types_business_name_unique')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
  }),
);
