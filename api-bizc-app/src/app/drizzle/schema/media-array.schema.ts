import { index, pgTable, uuid, integer } from 'drizzle-orm/pg-core';
import { media } from './media.schema';
import { business } from './business.schema';
import { createBaseEntityBusinessFields } from './common-fields.schema';

export const mediaArray = pgTable(
  'media_array',
  {
    ...createBaseEntityBusinessFields(business),
    referenceId: uuid('reference_id').notNull(),
    position: integer('position').notNull(),
    mediaId: uuid('media_id')
      .notNull()
      .references(() => media.id),
  },
  (t) => ({
    businessIdIndex: index('media_array_business_id_index').on(t.businessId),
    idIndex: index('media_array_id_index').on(t.id),
    referenceIdIndex: index('media_array_reference_id_index').on(t.referenceId),
    mediaIdIndex: index('media_array_media_id_index').on(t.mediaId),
    positionIndex: index('media_array_position_index').on(t.position),
  }),
);
