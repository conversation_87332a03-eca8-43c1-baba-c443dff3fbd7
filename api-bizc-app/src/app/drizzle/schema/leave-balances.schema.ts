import {
  decimal,
  index,
  integer,
  pgTable,
  uniqueIndex,
  uuid,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { business } from './business.schema';
import { staffMembers } from './staff.schema';
import { leaveTypes } from './leave-types.schema';

export const leaveBalances = pgTable(
  'leave_balances',
  {
    ...createBaseEntityBusinessFields(business),
    employeeId: uuid('employee_id')
      .notNull()
      .references(() => staffMembers.id),
    leaveTypeId: uuid('leave_type_id')
      .notNull()
      .references(() => leaveTypes.id),
    year: integer('year').notNull(),
    entitledDays: decimal('entitled_days', {
      precision: 5,
      scale: 2,
    }).notNull(),
    usedDays: decimal('used_days', {
      precision: 5,
      scale: 2,
    })
      .default('0.00')
      .notNull(),
    remainingDays: decimal('remaining_days', {
      precision: 5,
      scale: 2,
    }).notNull(),
    carriedForward: decimal('carried_forward', {
      precision: 5,
      scale: 2,
    })
      .default('0.00')
      .notNull(),
  },
  (t) => ({
    idIndex: index('leave_balances_id_index').on(t.id),
    ...createBaseEntityBusinessIndexes(t, 'leave_balances'),
    employeeIdIndex: index('leave_balances_employee_id_index').on(t.employeeId),
    leaveTypeIdIndex: index('leave_balances_leave_type_id_index').on(
      t.leaveTypeId,
    ),
    yearIndex: index('leave_balances_year_index').on(t.year),
    uniqueEmployeeLeaveTypeYear: uniqueIndex(
      'leave_balances_employee_leave_type_year_unique',
    )
      .on(t.businessId, t.employeeId, t.leaveTypeId, t.year)
      .where(sql`${t.isDeleted} = false`),
  }),
);
