import {
  index,
  pgTable,
  text,
  timestamp,
  uuid,
  pgEnum,
  uniqueIndex,
  integer,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { users } from './users.schema';
import { business } from './business.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import {
  CampaignType,
  CampaignStatus,
  TargetAudience,
} from '../../shared/types/campaign.enum';

// Campaign Type enum
export const campaignTypeEnum = pgEnum('campaign_type', [
  CampaignType.EMAIL,
  CampaignType.SMS,
  CampaignType.WHATSAPP,
  CampaignType.DIRECT_MAIL,
  CampaignType.TELEMARKETING,
  CampaignType.OTHER,
]);

// Campaign Status enum
export const campaignStatusEnum = pgEnum('campaign_status', [
  CampaignStatus.DRAFT,
  CampaignStatus.ACTIVE,
  CampaignStatus.PAUSED,
  CampaignStatus.COMPLETED,
  CampaignStatus.CANCELLED,
]);

// Target Audience enum
export const targetAudienceEnum = pgEnum('target_audience', [
  TargetAudience.CUSTOMER,
  TargetAudience.LEADS,
  TargetAudience.SMS_NEWSLETTER,
  TargetAudience.EMAIL_NEWSLETTER,
  TargetAudience.GAME_PARTICIPANT,
  TargetAudience.MANUAL,
]);

// Campaigns Schema
export const campaigns = pgTable(
  'campaigns',
  {
    ...createBaseEntityBusinessFields(business),
    name: text('name').notNull(),
    code: text('code'),
    description: text('description'),
    campaignType: campaignTypeEnum('campaign_type').notNull(),
    status: campaignStatusEnum('status')
      .default(CampaignStatus.DRAFT)
      .notNull(),
    templateId: uuid('template_id'), // Reference to templates table - will be added in relations
    startTime: timestamp('start_time'),
    endTime: timestamp('end_time'),
    targetAudience: targetAudienceEnum('target_audience').notNull(),
    totalContacts: integer('total_contacts').default(0).notNull(),
    successCount: integer('success_count').default(0).notNull(),
    failedCount: integer('failed_count').default(0).notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'campaigns'),
    nameIndex: index('campaigns_name_index').on(t.name),
    codeIndex: index('campaigns_code_index').on(t.code),
    campaignTypeIndex: index('campaigns_campaign_type_index').on(
      t.campaignType,
    ),
    statusIndex: index('campaigns_status_index').on(t.status),
    templateIdIndex: index('campaigns_template_id_index').on(t.templateId),
    targetAudienceIndex: index('campaigns_target_audience_index').on(
      t.targetAudience,
    ),
    startTimeIndex: index('campaigns_start_time_index').on(t.startTime),
    endTimeIndex: index('campaigns_end_time_index').on(t.endTime),

    // Optimized indexes for filtering and searching
    businessNameIndex: index('campaigns_business_name_index')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
    businessStatusIndex: index('campaigns_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessTypeIndex: index('campaigns_business_type_index')
      .on(t.businessId, t.campaignType)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessName: uniqueIndex('campaigns_business_name_unique')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessCode: uniqueIndex('campaigns_business_code_unique')
      .on(t.businessId, t.code)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Campaigns Relations
export const campaignsRelations = relations(campaigns, ({ one }) => ({
  business: one(business, {
    fields: [campaigns.businessId],
    references: [business.id],
  }),
  creator: one(users, {
    fields: [campaigns.createdBy],
    references: [users.id],
    relationName: 'createdCampaigns',
  }),
  updater: one(users, {
    fields: [campaigns.updatedBy],
    references: [users.id],
    relationName: 'updatedCampaigns',
  }),
  deleter: one(users, {
    fields: [campaigns.deletedBy],
    references: [users.id],
    relationName: 'deletedCampaigns',
  }),
  // Note: templateId relation will be added when templates are unified
}));
