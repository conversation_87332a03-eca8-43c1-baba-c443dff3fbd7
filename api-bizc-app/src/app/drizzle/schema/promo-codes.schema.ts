import {
  index,
  pgTable,
  text,
  timestamp,
  pgEnum,
  uniqueIndex,
  integer,
  decimal,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { discountTypeEnum } from './common-fields.schema';

export enum PromoCodeStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  EXPIRED = 'expired',
  USED_UP = 'used_up',
}

export const promoCodeStatusEnum = pgEnum('promo_code_status', [
  PromoCodeStatus.ACTIVE,
  PromoCodeStatus.INACTIVE,
  PromoCodeStatus.EXPIRED,
  PromoCodeStatus.USED_UP,
]);

export const promoCodes = pgTable(
  'promo_codes',
  {
    ...createBaseEntityBusinessFields(business),
    code: text('code').notNull(),
    name: text('name').notNull(),
    description: text('description'),

    // Discount Configuration
    discountType: discountTypeEnum('discount_type').notNull(),
    discountValue: decimal('discount_value', {
      precision: 10,
      scale: 2,
    }).notNull(),
    maxDiscountAmount: decimal('max_discount_amount', {
      precision: 10,
      scale: 2,
    }),

    // Validity Period
    startDate: timestamp('start_date').notNull(),
    endDate: timestamp('end_date').notNull(),

    // Status and Usage
    status: promoCodeStatusEnum('status')
      .default(PromoCodeStatus.ACTIVE)
      .notNull(),
    totalUsageLimit: integer('total_usage_limit'),
    usagePerCustomer: integer('usage_per_customer'),
    usagePerDay: integer('usage_per_day'),
    usagePerMonth: integer('usage_per_month'),
    currentUsageCount: integer('current_usage_count').default(0).notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'promo_codes'),
    codeIndex: index('promo_codes_code_index').on(t.code),
    nameIndex: index('promo_codes_name_index').on(t.name),
    statusIndex: index('promo_codes_status_index').on(t.status),
    startDateIndex: index('promo_codes_start_date_index').on(t.startDate),
    endDateIndex: index('promo_codes_end_date_index').on(t.endDate),
    discountTypeIndex: index('promo_codes_discount_type_index').on(
      t.discountType,
    ),

    // Optimized composite indexes for filtering and searching
    businessCodeIndex: index('promo_codes_business_code_index')
      .on(t.businessId, t.code)
      .where(sql`${t.isDeleted} = false`),
    businessNameIndex: index('promo_codes_business_name_index')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
    businessStatusIndex: index('promo_codes_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessValidityIndex: index('promo_codes_business_validity_index')
      .on(t.businessId, t.startDate, t.endDate)
      .where(sql`${t.isDeleted} = false`),

    // Index for active promo codes within date range
    activeValidPromoCodesIndex: index('promo_codes_active_valid_index')
      .on(t.businessId, t.status, t.startDate, t.endDate)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessCode: uniqueIndex('promo_codes_business_code_unique')
      .on(t.businessId, t.code)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessName: uniqueIndex('promo_codes_business_name_unique')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
  }),
);
