import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
} from 'drizzle-orm/pg-core';
import { business } from './business.schema';
import { paymentAccountTypes } from './payment-account-types.schema';
import { StatusType } from '../../shared/types';
import { createBaseEntityBusinessFields } from './common-fields.schema';

export const paymentAccountStatusEnum = pgEnum('payment_account_status', [
  StatusType.ACTIVE,
  StatusType.INACTIVE,
]);

export const paymentAccounts = pgTable(
  'payment_accounts',
  {
    ...createBaseEntityBusinessFields(business),
    name: text('name').notNull(),
    accountNumber: text('account_number').notNull(),
    accountTypeId: uuid('account_type_id').references(
      () => paymentAccountTypes.id,
    ),
    note: text('note'),
    status: paymentAccountStatusEnum('status')
      .default(StatusType.ACTIVE)
      .notNull(),
  },
  (t) => ({
    idIndex: index('payment_accounts_id_index').on(t.id),
    businessIdIndex: index('payment_accounts_business_id_index').on(
      t.businessId,
    ),
    nameIndex: index('payment_accounts_name_index').on(t.name),
    accountNumberIndex: index('payment_accounts_account_number_index').on(
      t.accountNumber,
    ),
    accountTypeIdIndex: index('payment_accounts_account_type_id_index').on(
      t.accountTypeId,
    ),
    uniqueBusinessAccountNumber: uniqueIndex(
      'payment_accounts_business_account_number_unique',
    ).on(t.businessId, t.accountNumber),
  }),
);
