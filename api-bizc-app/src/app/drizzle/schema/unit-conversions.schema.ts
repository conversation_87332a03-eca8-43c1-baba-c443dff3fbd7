import {
  index,
  pgTable,
  uuid,
  uniqueIndex,
  decimal,
  check,
} from 'drizzle-orm/pg-core';
import { sql, gt } from 'drizzle-orm';
import { units } from './units.schema';
import { business } from './business.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

export const unitConversions = pgTable(
  'unit_conversions',
  {
    ...createBaseEntityBusinessFields(business),
    fromUnitId: uuid('from_unit_id')
      .notNull()
      .references(() => units.id, { onDelete: 'cascade' }),
    toUnitId: uuid('to_unit_id')
      .notNull()
      .references(() => units.id, { onDelete: 'cascade' }),
    conversionFactor: decimal('conversion_factor', {
      precision: 18,
      scale: 6,
    }).notNull(),
  },
  (t) => ({
    // Standard base entity indexes
    ...createBaseEntityBusinessIndexes(t, 'unit_conversions'),
    fromUnitIdIndex: index('unit_conversions_from_unit_id_index').on(
      t.fromUnitId,
    ),
    toUnitIdIndex: index('unit_conversions_to_unit_id_index').on(t.toUnitId),
    conversionFactorIndex: index('unit_conversions_conversion_factor_index').on(
      t.conversionFactor,
    ),

    // Optimized composite indexes for conversion lookups
    fromToUnitsIndex: index('unit_conversions_from_to_units_index')
      .on(t.fromUnitId, t.toUnitId)
      .where(sql`${t.isDeleted} = false`),
    toFromUnitsIndex: index('unit_conversions_to_from_units_index')
      .on(t.toUnitId, t.fromUnitId)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraint to prevent duplicate conversions
    uniqueFromToUnits: uniqueIndex('unit_conversions_from_to_unique')
      .on(t.fromUnitId, t.toUnitId)
      .where(sql`${t.isDeleted} = false`),

    // Check constraint to ensure conversion factor is positive
    positiveConversionFactor: check(
      'unit_conversions_positive_conversion_factor',
      sql`${t.conversionFactor} > 0`,
    ),
  }),
);
