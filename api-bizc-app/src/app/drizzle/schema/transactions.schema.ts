import {
  index,
  pgTable,
  text,
  timestamp,
  uuid,
  pgEnum,
  uniqueIndex,
  boolean,
  decimal,
  date,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { users } from './users.schema';
import { business } from './business.schema';
import { accounts } from './accounts.schema';
import { customers } from './customers.schema';
import { suppliers } from './suppliers.schema';
import { paymentAccounts } from './payment-accounts.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { CurrencyCode } from '../../shared/types';

// Transaction Type Enum - Comprehensive financial transaction types
export const transactionTypeEnum = pgEnum('transaction_type', [
  'sale',
  'purchase',
  'payment_received',
  'payment_made',
  'expense',
  'income',
  'transfer',
  'adjustment',
  'refund',
  'deposit',
  'withdrawal',
  'journal_entry',
  'opening_balance',
  'closing_balance',
  'tax_payment',
  'loan_payment',
  'loan_received',
  'investment',
  'dividend',
  'interest_earned',
  'interest_paid',
  'bank_charges',
  'other',
]);

// Transaction Status Enum
export const transactionStatusEnum = pgEnum('transaction_status', [
  'pending',
  'completed',
  'failed',
  'cancelled',
  'processing',
  'on_hold',
  'reversed',
  'partially_completed',
]);

// Payment Method Enum
export const paymentMethodEnum = pgEnum('payment_method', [
  'cash',
  'card',
  'bank_transfer',
  'cheque',
  'digital_wallet',
  'cryptocurrency',
  'mobile_payment',
  'wire_transfer',
  'ach_transfer',
  'direct_debit',
  'credit_note',
  'gift_card',
  'store_credit',
  'other',
]);

// Transaction Currency Code Enum (using existing CurrencyCode from shared types)
export const transactionCurrencyCodeEnum = pgEnum('transaction_currency_code', [
  CurrencyCode.USD,
  CurrencyCode.EUR,
  CurrencyCode.GBP,
  CurrencyCode.JPY,
  CurrencyCode.AUD,
  CurrencyCode.CAD,
  CurrencyCode.CHF,
  CurrencyCode.CNY,
  CurrencyCode.INR,
  CurrencyCode.LKR,
  // Add more currencies as needed
]);

// Transaction Direction Enum
export const transactionDirectionEnum = pgEnum('transaction_direction', [
  'inbound', // Money coming in
  'outbound', // Money going out
]);

export const transactions = pgTable(
  'transactions',
  {
    ...createBaseEntityBusinessFields(business),

    // Transaction identification
    transactionNumber: text('transaction_number').notNull(),
    referenceNumber: text('reference_number'),
    externalReferenceNumber: text('external_reference_number'),

    // Transaction classification
    transactionType: transactionTypeEnum('transaction_type').notNull(),
    transactionDirection: transactionDirectionEnum(
      'transaction_direction',
    ).notNull(),
    status: transactionStatusEnum('status').default('pending').notNull(),

    // Transaction details
    description: text('description').notNull(),
    notes: text('notes'),
    transactionDate: date('transaction_date').notNull(),
    dueDate: date('due_date'),
    completedDate: timestamp('completed_date'),

    // Financial details
    amount: decimal('amount', { precision: 15, scale: 2 }).notNull(),
    currency: transactionCurrencyCodeEnum('currency')
      .default(CurrencyCode.USD)
      .notNull(),
    exchangeRate: decimal('exchange_rate', { precision: 10, scale: 6 }).default(
      '1.000000',
    ),
    baseAmount: decimal('base_amount', { precision: 15, scale: 2 }), // Amount in business base currency

    // Tax information
    taxAmount: decimal('tax_amount', { precision: 15, scale: 2 }).default(
      '0.00',
    ),
    taxRate: decimal('tax_rate', { precision: 5, scale: 4 }).default('0.0000'),
    isTaxInclusive: boolean('is_tax_inclusive').default(false).notNull(),

    // Payment information
    paymentMethod: paymentMethodEnum('payment_method'),
    paymentReference: text('payment_reference'),
    paymentAccountId: uuid('payment_account_id').references(
      () => paymentAccounts.id,
    ),

    // Entity relationships
    accountId: uuid('account_id').references(() => accounts.id),
    customerId: uuid('customer_id').references(() => customers.id),
    supplierId: uuid('supplier_id').references(() => suppliers.id),

    // Related transaction (for transfers, refunds, etc.)
    relatedTransactionId: uuid('related_transaction_id').references(
      () => transactions.id,
    ),
    parentTransactionId: uuid('parent_transaction_id').references(
      () => transactions.id,
    ),

    // Additional metadata
    tags: text('tags')
      .array()
      .default(sql`'{}'`),
    attachments: uuid('attachments')
      .array()
      .default(sql`'{}'`),
    customFields: text('custom_fields'), // JSON string for flexible custom fields

    // Reconciliation
    isReconciled: boolean('is_reconciled').default(false).notNull(),
    reconciledDate: timestamp('reconciled_date'),
    reconciledBy: uuid('reconciled_by').references(() => users.id),

    // System flags
    isSystemGenerated: boolean('is_system_generated').default(false).notNull(),
    isReversed: boolean('is_reversed').default(false).notNull(),
    reversedDate: timestamp('reversed_date'),
    reversedBy: uuid('reversed_by').references(() => users.id),
    reversalReason: text('reversal_reason'),
  },
  (t) => ({
    // Standard base entity indexes
    ...createBaseEntityBusinessIndexes(t, 'transactions'),
    transactionNumberIndex: index('transactions_transaction_number_index').on(
      t.transactionNumber,
    ),
    referenceNumberIndex: index('transactions_reference_number_index').on(
      t.referenceNumber,
    ),
    externalReferenceNumberIndex: index(
      'transactions_external_reference_number_index',
    ).on(t.externalReferenceNumber),

    // Transaction classification indexes
    transactionTypeIndex: index('transactions_transaction_type_index').on(
      t.transactionType,
    ),
    transactionDirectionIndex: index(
      'transactions_transaction_direction_index',
    ).on(t.transactionDirection),
    statusIndex: index('transactions_status_index').on(t.status),

    // Date indexes for time-based queries
    transactionDateIndex: index('transactions_transaction_date_index').on(
      t.transactionDate,
    ),
    dueDateIndex: index('transactions_due_date_index').on(t.dueDate),
    completedDateIndex: index('transactions_completed_date_index').on(
      t.completedDate,
    ),

    // Financial indexes
    amountIndex: index('transactions_amount_index').on(t.amount),
    currencyIndex: index('transactions_currency_index').on(t.currency),

    // Entity relationship indexes
    accountIdIndex: index('transactions_account_id_index').on(t.accountId),
    customerIdIndex: index('transactions_customer_id_index').on(t.customerId),
    supplierIdIndex: index('transactions_supplier_id_index').on(t.supplierId),
    paymentAccountIdIndex: index('transactions_payment_account_id_index').on(
      t.paymentAccountId,
    ),
    relatedTransactionIdIndex: index(
      'transactions_related_transaction_id_index',
    ).on(t.relatedTransactionId),
    parentTransactionIdIndex: index(
      'transactions_parent_transaction_id_index',
    ).on(t.parentTransactionId),

    // Payment method index
    paymentMethodIndex: index('transactions_payment_method_index').on(
      t.paymentMethod,
    ),

    // Reconciliation indexes
    isReconciledIndex: index('transactions_is_reconciled_index').on(
      t.isReconciled,
    ),
    reconciledDateIndex: index('transactions_reconciled_date_index').on(
      t.reconciledDate,
    ),

    // System flags indexes
    isSystemGeneratedIndex: index('transactions_is_system_generated_index').on(
      t.isSystemGenerated,
    ),
    isReversedIndex: index('transactions_is_reversed_index').on(t.isReversed),

    // Optimized composite indexes for common query patterns
    businessTransactionDateIndex: index(
      'transactions_business_transaction_date_index',
    )
      .on(t.businessId, t.transactionDate, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessTypeStatusIndex: index('transactions_business_type_status_index')
      .on(t.businessId, t.transactionType, t.status, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessCustomerDateIndex: index(
      'transactions_business_customer_date_index',
    )
      .on(t.businessId, t.customerId, t.transactionDate, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessSupplierDateIndex: index(
      'transactions_business_supplier_date_index',
    )
      .on(t.businessId, t.supplierId, t.transactionDate, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessAccountDateIndex: index('transactions_business_account_date_index')
      .on(t.businessId, t.accountId, t.transactionDate, t.id)
      .where(sql`${t.isDeleted} = false`),

    // Optimized indexes for filtering and searching
    businessTransactionNumberIndex: index(
      'transactions_business_transaction_number_index',
    )
      .on(t.businessId, t.transactionNumber)
      .where(sql`${t.isDeleted} = false`),
    businessReferenceNumberIndex: index(
      'transactions_business_reference_number_index',
    )
      .on(t.businessId, t.referenceNumber)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessTransactionNumber: uniqueIndex(
      'transactions_business_transaction_number_unique',
    )
      .on(t.businessId, t.transactionNumber)
      .where(sql`${t.isDeleted} = false`),
  }),
);
