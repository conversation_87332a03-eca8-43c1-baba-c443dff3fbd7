import {
  boolean,
  date,
  index,
  integer,
  pgEnum,
  pgTable,
  text,
  time,
  timestamp,
  uuid,
  uniqueIndex,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import { staffMembers } from './staff.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

export const recurrenceTypeEnum = pgEnum('recurrence_type', [
  'none',
  'daily',
  'weekly',
  'biweekly',
  'monthly',
  'custom',
]);

export const slotStatusEnum = pgEnum('slot_status', [
  'available',
  'booked',
  'partially-booked',
  'blocked',
  'cancelled',
]);

export const timeSlots = pgTable(
  'time_slots',
  {
    ...createBaseEntityBusinessFields(business),
    staffMemberId: uuid('staff_member_id')
      .notNull()
      .references(() => staffMembers.id, { onDelete: 'cascade' }),

    // Recurrence Configuration
    recurrenceType: recurrenceTypeEnum('recurrence_type')
      .default('none')
      .notNull(),
    recurrencePattern: text('recurrence_pattern'),
    recurrenceStartDate: date('recurrence_start_date'),
    recurrenceEndDate: date('recurrence_end_date'),
    recurrenceInterval: integer('recurrence_interval').default(1),
    recurrenceDaysOfWeek: text('recurrence_days_of_week'),
    recurrenceDaysOfMonth: text('recurrence_days_of_month'),
    recurrenceWeekOfMonth: text('recurrence_week_of_month'),
    occurrenceCount: integer('occurrence_count'),
    isRecurringTemplate: boolean('is_recurring_template')
      .default(false)
      .notNull(),

    // Specific Date/Time (for non-recurring or recurring instances)
    slotDate: date('slot_date'),
    slotStartTime: time('slot_start_time'),
    slotEndTime: time('slot_end_time'),
    slotStart: timestamp('slot_start'),
    slotEnd: timestamp('slot_end'),
    durationMinutes: integer('duration_minutes'),

    // Capacity Management
    maxAppointments: integer('max_appointments').default(1).notNull(),
    bookedAppointments: integer('booked_appointments').default(0).notNull(),
    tentativeAppointments: integer('tentative_appointments')
      .default(0)
      .notNull(),
    availableAppointments: integer('available_appointments')
      .default(1)
      .notNull(),

    // Slot Status
    slotStatus: slotStatusEnum('slot_status').default('available').notNull(),
    isAvailableOnline: boolean('is_available_online').default(true).notNull(),

    // Notes and Metadata
    notes: text('notes'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'time_slots'),
    recurrenceTypeIndex: index('time_slots_recurrence_type_index').on(
      t.recurrenceType,
    ),
    slotDateIndex: index('time_slots_slot_date_index').on(t.slotDate),
    slotStartIndex: index('time_slots_slot_start_index').on(t.slotStart),
    slotEndIndex: index('time_slots_slot_end_index').on(t.slotEnd),
    slotStatusIndex: index('time_slots_slot_status_index').on(t.slotStatus),
    isAvailableOnlineIndex: index('time_slots_is_available_online_index').on(
      t.isAvailableOnline,
    ),

    // Composite indexes for efficient querying
    staffMemberDateIndex: index('time_slots_staff_member_date_index')
      .on(t.staffMemberId, t.slotDate)
      .where(sql`${t.isDeleted} = false`),
    staffMemberStatusIndex: index('time_slots_staff_member_status_index')
      .on(t.staffMemberId, t.slotStatus)
      .where(sql`${t.isDeleted} = false`),
    staffMemberAvailabilityIndex: index(
      'time_slots_staff_member_availability_index',
    )
      .on(t.staffMemberId, t.isAvailableOnline, t.slotStatus)
      .where(sql`${t.isDeleted} = false`),
    dateRangeIndex: index('time_slots_date_range_index')
      .on(t.slotDate, t.slotStart, t.slotEnd)
      .where(sql`${t.isDeleted} = false`),
    recurringTemplateIndex: index('time_slots_recurring_template_index')
      .on(t.isRecurringTemplate, t.recurrenceType)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints
    uniqueStaffMemberDateTime: uniqueIndex(
      'time_slots_staff_member_date_time_unique',
    )
      .on(t.staffMemberId, t.slotDate, t.slotStartTime, t.slotEndTime)
      .where(sql`${t.isDeleted} = false`),
  }),
);
