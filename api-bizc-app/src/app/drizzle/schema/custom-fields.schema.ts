import {
  index,
  pgTable,
  text,
  pgEnum,
  uniqueIndex,
  boolean,
  jsonb,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { CustomFieldType, EntityType } from '../../shared/types';

export const customFieldTypeEnum = pgEnum('custom_field_type', [
  CustomFieldType.TEXT,
  CustomFieldType.NUMBER,
  CustomFieldType.DATE,
  CustomFieldType.EMAIL,
  CustomFieldType.PHONE,
  CustomFieldType.URL,
  CustomFieldType.SELECT,
  CustomFieldType.MULTI_SELECT,
  CustomFieldType.CHECKBOX,
  CustomFieldType.RADIO,
  CustomFieldType.TEXTAREA,
]);

export const entityTypeEnum = pgEnum('entity_type', [
  EntityType.CUSTOMERS,
  EntityType.EXPENSES,
  EntityType.ITEMS,
  EntityType.CAMPAIGNS,
  EntityType.BOOKINGS,
  EntityType.RENTALS,
  EntityType.ROOM_BOOKINGS,
  EntityType.SMS_NEWSLETTERS,
]);

export const customFields = pgTable(
  'custom_fields',
  {
    ...createBaseEntityBusinessFields(business),
    name: text('name').notNull(),
    type: customFieldTypeEnum('type').notNull(),
    entityType: entityTypeEnum('entity_type').notNull(),
    required: boolean('required').default(false).notNull(),
    placeholder: text('placeholder'),
    options: jsonb('options').$type<string[]>(), // Array of strings for select/multi-select/radio
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'custom_fields'),
    // Entity-specific indexes
    entityTypeIndex: index('custom_fields_entity_type_index').on(t.entityType),
    nameIndex: index('custom_fields_name_index').on(t.name),
    typeIndex: index('custom_fields_type_index').on(t.type),
    requiredIndex: index('custom_fields_required_index').on(t.required),

    // Composite indexes for performance
    businessEntityTypeIndex: index('custom_fields_business_entity_type_index')
      .on(t.businessId, t.entityType)
      .where(sql`${t.isDeleted} = false`),
    businessTypeIndex: index('custom_fields_business_type_index')
      .on(t.businessId, t.type)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessEntityName: uniqueIndex(
      'custom_fields_business_entity_name_unique',
    )
      .on(t.businessId, t.entityType, t.name)
      .where(sql`${t.isDeleted} = false`),
  }),
);
