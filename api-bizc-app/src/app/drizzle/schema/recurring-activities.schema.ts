import {
  index,
  pgEnum,
  pgTable,
  text,
  uuid,
  boolean,
  integer,
  date,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { DayOfWeek } from '../../shared/types';

/**
 * Recurring Activities Schema
 *
 * This schema provides reusable recurring functionality that can be shared
 * across multiple entity types (tasks, invoices, transactions, expenses, etc.)
 * following the DRY principle and maintaining consistency across the application.
 */

// Recurring Entity Type Enum
// Defines which entity types can have recurring functionality
export enum RecurringEntityType {
  TASK = 'task',
  INVOICE = 'invoice',
  TRANSACTION = 'transaction',
  EXPENSE = 'expense',
  PAYMENT = 'payment',
  REMINDER = 'reminder',
  REPORT = 'report',
  BACKUP = 'backup',
  MAINTENANCE = 'maintenance',
  NOTIFICATION = 'notification',
  HOUSEKEEPING = 'housekeeping',
}

export const recurringEntityTypeEnum = pgEnum('recurring_entity_type', [
  RecurringEntityType.TASK,
  RecurringEntityType.INVOICE,
  RecurringEntityType.TRANSACTION,
  RecurringEntityType.EXPENSE,
  RecurringEntityType.PAYMENT,
  RecurringEntityType.REMINDER,
  RecurringEntityType.REPORT,
  RecurringEntityType.BACKUP,
  RecurringEntityType.MAINTENANCE,
  RecurringEntityType.NOTIFICATION,
  RecurringEntityType.HOUSEKEEPING,
]);

// Recurrence Pattern Enum
export enum RecurrencePattern {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  YEARLY = 'yearly',
}

export const recurrencePatternEnum = pgEnum('recurrence_pattern', [
  RecurrencePattern.DAILY,
  RecurrencePattern.WEEKLY,
  RecurrencePattern.MONTHLY,
  RecurrencePattern.YEARLY,
]);

// Recurrence End Type Enum
export enum RecurrenceEndType {
  NEVER = 'never',
  ON_DATE = 'on_date',
  AFTER_OCCURRENCES = 'after_occurrences',
}

export const recurrenceEndTypeEnum = pgEnum('recurrence_end_type', [
  RecurrenceEndType.NEVER,
  RecurrenceEndType.ON_DATE,
  RecurrenceEndType.AFTER_OCCURRENCES,
]);

// Recurring Activity Status Enum
export enum RecurringActivityStatus {
  ACTIVE = 'active',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export const recurringActivityStatusEnum = pgEnum('recurring_activity_status', [
  RecurringActivityStatus.ACTIVE,
  RecurringActivityStatus.PAUSED,
  RecurringActivityStatus.COMPLETED,
  RecurringActivityStatus.CANCELLED,
]);

// Day of Week Enum for recurring activities (reusing from shared types)
export const recurringDayOfWeekEnum = pgEnum('recurring_day_of_week', [
  DayOfWeek.MONDAY,
  DayOfWeek.TUESDAY,
  DayOfWeek.WEDNESDAY,
  DayOfWeek.THURSDAY,
  DayOfWeek.FRIDAY,
  DayOfWeek.SATURDAY,
  DayOfWeek.SUNDAY,
]);

// Main recurring activities table
export const recurringActivities = pgTable(
  'recurring_activities',
  {
    ...createBaseEntityBusinessFields(business),

    // Entity identification
    entityType: recurringEntityTypeEnum('entity_type').notNull(),
    entityId: uuid('entity_id').notNull(), // Reference to the original entity (task, invoice, etc.)

    // Recurrence configuration
    isActive: boolean('is_active').default(true).notNull(),
    status: recurringActivityStatusEnum('status')
      .default(RecurringActivityStatus.ACTIVE)
      .notNull(),

    // Core recurrence settings
    recurrencePattern: recurrencePatternEnum('recurrence_pattern').notNull(),
    recurrenceInterval: integer('recurrence_interval').default(1).notNull(), // Every X days/weeks/months/years

    // End conditions
    recurrenceEndType: recurrenceEndTypeEnum('recurrence_end_type')
      .default(RecurrenceEndType.NEVER)
      .notNull(),
    recurrenceEndDate: date('recurrence_end_date'),
    recurrenceEndAfterOccurrences: integer('recurrence_end_after_occurrences'),

    // Pattern-specific settings
    recurrenceDaysOfWeek: recurringDayOfWeekEnum(
      'recurrence_days_of_week',
    ).array(), // For weekly recurrence
    recurrenceDayOfMonth: integer('recurrence_day_of_month'), // For monthly/yearly recurrence (1-31)
    recurrenceMonthOfYear: integer('recurrence_month_of_year'), // For yearly recurrence (1-12: Jan-Dec)

    // Tracking fields
    nextOccurrenceDate: date('next_occurrence_date'), // When the next occurrence should happen
    lastOccurrenceDate: date('last_occurrence_date'), // When the last occurrence happened
    occurrenceCount: integer('occurrence_count').default(0).notNull(), // How many times it has occurred

    // Metadata
    title: text('title'), // Optional title for the recurring activity
    description: text('description'), // Optional description
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'recurring_activities'),
    // Indexes for performance
    entityTypeIndex: index('recurring_activities_entity_type_index').on(
      t.entityType,
    ),
    entityIdIndex: index('recurring_activities_entity_id_index').on(t.entityId),
    isActiveIndex: index('recurring_activities_is_active_index').on(t.isActive),
    statusIndex: index('recurring_activities_status_index').on(t.status),
    recurrencePatternIndex: index(
      'recurring_activities_recurrence_pattern_index',
    ).on(t.recurrencePattern),

    // Date-based indexes for scheduling
    nextOccurrenceDateIndex: index(
      'recurring_activities_next_occurrence_date_index',
    ).on(t.nextOccurrenceDate),
    lastOccurrenceDateIndex: index(
      'recurring_activities_last_occurrence_date_index',
    ).on(t.lastOccurrenceDate),
    recurrenceEndDateIndex: index(
      'recurring_activities_recurrence_end_date_index',
    ).on(t.recurrenceEndDate),
    recurrenceMonthOfYearIndex: index(
      'recurring_activities_recurrence_month_of_year_index',
    ).on(t.recurrenceMonthOfYear),
    recurrenceDayOfMonthIndex: index(
      'recurring_activities_recurrence_day_of_month_index',
    ).on(t.recurrenceDayOfMonth),

    // Composite indexes for common queries with soft deletion support
    businessEntityTypeIndex: index(
      'recurring_activities_business_entity_type_index',
    )
      .on(t.businessId, t.entityType)
      .where(sql`${t.isDeleted} = false`),
    businessEntityIndex: index('recurring_activities_business_entity_index')
      .on(t.businessId, t.entityType, t.entityId)
      .where(sql`${t.isDeleted} = false`),
    businessActiveIndex: index('recurring_activities_business_active_index')
      .on(t.businessId, t.isActive)
      .where(sql`${t.isDeleted} = false`),
    businessStatusIndex: index('recurring_activities_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessNextOccurrenceIndex: index(
      'recurring_activities_business_next_occurrence_index',
    )
      .on(t.businessId, t.nextOccurrenceDate)
      .where(sql`${t.isDeleted} = false`),

    // Scheduling query optimization
    activeNextOccurrenceIndex: index(
      'recurring_activities_active_next_occurrence_index',
    )
      .on(t.isActive, t.nextOccurrenceDate)
      .where(sql`${t.isDeleted} = false`),
    statusNextOccurrenceIndex: index(
      'recurring_activities_status_next_occurrence_index',
    )
      .on(t.status, t.nextOccurrenceDate)
      .where(sql`${t.isDeleted} = false`),
  }),
);
