import {
  index,
  pgTable,
  text,
  uuid,
  boolean,
  decimal,
  date,
  time,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { staffMembers } from './staff.schema';
import { business } from './business.schema';
import { assets } from './assets.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

export const assetDamage = pgTable(
  'asset_damage',
  {
    ...createBaseEntityBusinessFields(business),
    assetId: uuid('asset_id')
      .notNull()
      .references(() => assets.id),

    // Damage information
    damageType: text('damage_type').notNull(),
    amount: decimal('amount', { precision: 12, scale: 2 }).notNull(),
    incidentDate: date('incident_date').notNull(),
    incidentTime: time('incident_time').notNull(),
    details: text('details'),

    // Fix information
    isFixed: boolean('is_fixed').default(false).notNull(),
    fixedDate: date('fixed_date'),
    fixedBy: uuid('fixed_by').references(() => staffMembers.id),
    checkedBy: uuid('checked_by').references(() => staffMembers.id),
    fixedDetails: text('fixed_details'),
    fixedCost: decimal('fixed_cost', { precision: 12, scale: 2 }),
  },
  (t) => ({
    // Standard base entity indexes
    ...createBaseEntityBusinessIndexes(t, 'asset_damage'),

    // Entity-specific indexes
    assetIdIndex: index('asset_damage_asset_id_index').on(t.assetId),
    damageTypeIndex: index('asset_damage_type_index').on(t.damageType),
    incidentDateIndex: index('asset_damage_incident_date_index').on(
      t.incidentDate,
    ),
    isFixedIndex: index('asset_damage_is_fixed_index').on(t.isFixed),
    fixedByIndex: index('asset_damage_fixed_by_index').on(t.fixedBy),
    checkedByIndex: index('asset_damage_checked_by_index').on(t.checkedBy),

    // Composite indexes for common query patterns
    assetStatusIndex: index('asset_damage_asset_status_index')
      .on(t.assetId, t.isFixed)
      .where(sql`${t.isDeleted} = false`),
    businessDateIndex: index('asset_damage_business_date_index')
      .on(t.businessId, t.incidentDate)
      .where(sql`${t.isDeleted} = false`),
  }),
);
