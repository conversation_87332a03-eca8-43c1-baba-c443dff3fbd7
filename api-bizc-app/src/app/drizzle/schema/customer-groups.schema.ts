import { index, pgTable, text, pgEnum, uniqueIndex } from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { CustomerGroupStatus } from '../../shared/types';

export const customerGroupStatusEnum = pgEnum('customer_group_status', [
  CustomerGroupStatus.ACTIVE,
  CustomerGroupStatus.INACTIVE,
]);

export const customerGroups = pgTable(
  'customer_groups',
  {
    ...createBaseEntityBusinessFields(business),
    name: text('name').notNull(),
    description: text('description'),
    status: customerGroupStatusEnum('status')
      .default(CustomerGroupStatus.ACTIVE)
      .notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'customer_groups'),
    // Entity-specific indexes
    nameIndex: index('customer_groups_name_index').on(t.name),
    statusIndex: index('customer_groups_status_index').on(t.status),

    // Composite indexes for performance
    businessStatusIndex: index('customer_groups_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessNameIndex: index('customer_groups_business_name_index')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessName: uniqueIndex('customer_groups_business_name_unique')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
  }),
);
