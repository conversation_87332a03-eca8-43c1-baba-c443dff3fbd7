import {
  index,
  pgEnum,
  pgTable,
  text,
  uuid,
  uniqueIndex,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import { addresses } from './address.schema';
import { LocationType, LocationStatus } from '../../shared/types';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

// Define pgEnum types for location types and status
export const locationTypeEnum = pgEnum('location_type', [
  LocationType.OFFICE,
  LocationType.RETAIL,
  LocationType.MANUFACTURING,
  LocationType.MAINSITE,
  LocationType.VEHICLE,
  LocationType.WAREHOUSE,
]);

export const locationStatusEnum = pgEnum('location_status', [
  LocationStatus.ACTIVE,
  LocationStatus.INACTIVE,
]);

export const locations = pgTable(
  'locations',
  {
    ...createBaseEntityBusinessFields(business),
    name: text('name').notNull(),
    code: text('code').notNull(),
    addressId: uuid('address_id')
      .notNull()
      .references(() => addresses.id),
    type: locationTypeEnum('type').default(LocationType.MAINSITE).notNull(),
    status: locationStatusEnum('status')
      .default(LocationStatus.ACTIVE)
      .notNull(),
    linkedLocationIds: uuid('linked_location_ids').array(),
  },
  (t) => ({
    idIndex: index('location_id_index').on(t.id),
    ...createBaseEntityBusinessIndexes(t, 'locations'),
    codeIndex: index('location_code_index').on(t.code),
    addressIdIndex: index('location_address_id_index').on(t.addressId),
    uniqueBusinessName: uniqueIndex('location_business_name_unique')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessCode: uniqueIndex('location_business_code_unique')
      .on(t.businessId, t.code)
      .where(sql`${t.isDeleted} = false`),
  }),
);
