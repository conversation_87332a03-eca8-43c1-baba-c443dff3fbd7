import { pgTable, uniqueIndex, jsonb } from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { Amenity } from '../../shared/types/amenity.enum';

/**
 * Business Amenities table - Frontend-driven amenity management
 * This table tracks which amenities each business has available
 * Amenities are stored as an array of enum values
 */
export const businessAmenities = pgTable(
  'business_amenities',
  {
    ...createBaseEntityBusinessFields(business),
    amenities: jsonb('amenities').$type<Amenity[]>().default([]),
  },
  (t) => ({
    // Standard indexes for base entity fields
    ...createBaseEntityBusinessIndexes(t, 'business_amenities'),

    // Unique constraint - one record per business
    uniqueBusinessAmenity: uniqueIndex('business_amenities_unique')
      .on(t.businessId)
      .where(sql`${t.isDeleted} = false`),
  }),
);
