import {
  index,
  pgTable,
  text,
  timestamp,
  uuid,
  pgEnum,
  integer,
} from 'drizzle-orm/pg-core';
import { users } from './users.schema';
import { business } from './business.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { CloudProvider, MediaType } from '../../shared/types';

// TypeScript enum for Media Reference Types
export enum MediaReferenceType {
  PRODUCTS = 'products',
  ASSETS = 'assets',
  STAFF = 'staff',
  ACCOMMODATION_UNITS = 'accommodation-units',
  ACCOMMODATION_UNIT_OG = 'accommodation-unit-og',
  PACKAGES = 'packages',
  COMMENTS = 'comments',
  GAMES = 'games',
  SERVICE_CATEGORIES = 'service-categories',
  SERVICE_CATEGORIES_IMAGES = 'service-categories/images',
  SERVICE_CATEGORIES_OG_IMAGES = 'service-categories/og-images',
  OTHER = 'other',
}

// Cloud provider enum
export const cloudProviderEnum = pgEnum('cloud_provider', [
  CloudProvider.GCP,
  CloudProvider.AWS,
  CloudProvider.AZURE,
  CloudProvider.LOCAL,
]);

// Media type enum
export const mediaTypeEnum = pgEnum('media_type', [
  MediaType.IMAGE,
  MediaType.VIDEO,
  MediaType.AUDIO,
  MediaType.DOCUMENT,
  MediaType.SPREADSHEET,
  MediaType.PRESENTATION,
  MediaType.ARCHIVE,
  MediaType.PDF,
  MediaType.TEXT,
  MediaType.CODE,
  MediaType.FONT,
  MediaType.VECTOR,
  MediaType.MODEL_3D,
  MediaType.OTHER,
]);

// Reference type enum for media associations
export const mediaReferenceTypeEnum = pgEnum('media_reference_type', [
  MediaReferenceType.PRODUCTS,
  MediaReferenceType.ASSETS,
  MediaReferenceType.STAFF,
  MediaReferenceType.ACCOMMODATION_UNITS,
  MediaReferenceType.ACCOMMODATION_UNIT_OG,
  MediaReferenceType.PACKAGES,
  MediaReferenceType.COMMENTS,
  MediaReferenceType.GAMES,
  MediaReferenceType.SERVICE_CATEGORIES,
  MediaReferenceType.SERVICE_CATEGORIES_IMAGES,
  MediaReferenceType.SERVICE_CATEGORIES_OG_IMAGES,
  MediaReferenceType.OTHER,
]);

// Helper to get all media reference type values as an array for validation
export const MEDIA_REFERENCE_TYPE_VALUES = Object.values(
  MediaReferenceType,
) as MediaReferenceType[];

// Type alias for the enum values
export type MediaReferenceTypeValue = MediaReferenceType;

export const media = pgTable(
  'media',
  {
    ...createBaseEntityBusinessFields(business),
    referenceId: uuid('reference_id'),
    referenceType: mediaReferenceTypeEnum('reference_type'),
    fileName: text('file_name').notNull(),
    originalName: text('original_name').notNull(),
    publicUrl: text('public_url').notNull(),
    size: integer('size').notNull(), // File size in bytes
    mimeType: text('mime_type').notNull(),
    mediaType: mediaTypeEnum('media_type').default(MediaType.OTHER).notNull(), // Can be auto-determined from mimeType using getMediaTypeFromMimeType()
    cloudProvider: cloudProviderEnum('cloud_provider').notNull(),
    uploadedBy: uuid('uploaded_by')
      .notNull()
      .references(() => users.id),
    uploadedAt: timestamp('uploaded_at').defaultNow().notNull(),
  },
  (t) => ({
    idIndex: index('media_id_index').on(t.id),
    ...createBaseEntityBusinessIndexes(t, 'media'),
    fileNameIndex: index('media_file_name_index').on(t.fileName),
    uploadedByIndex: index('media_uploaded_by_index').on(t.uploadedBy),
    mimeTypeIndex: index('media_mime_type_index').on(t.mimeType),
    mediaTypeIndex: index('media_type_index').on(t.mediaType),
    referenceIdIndex: index('media_reference_id_index').on(t.referenceId),
    referenceTypeIndex: index('media_reference_type_index').on(t.referenceType),
    referenceCompositeIndex: index('media_reference_composite_index').on(
      t.referenceId,
      t.referenceType,
    ),
  }),
);
