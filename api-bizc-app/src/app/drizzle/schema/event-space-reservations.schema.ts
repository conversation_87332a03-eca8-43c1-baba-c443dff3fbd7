import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  timestamp,
  integer,
  decimal,
  boolean,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import { eventSpaces } from './event-spaces.schema';
import { customers } from './customers.schema';
import { assets } from './assets.schema';
import { staffMembers } from './staff.schema';
import { tasks } from './tasks.schema';
import { taxes } from './taxes.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { PaymentStatus } from '../../shared/types/common.enum';
import { paymentStatusEnum } from './common-fields.schema';

// TypeScript enums for type safety
export enum EventReservationStatus {
  INQUIRY = 'INQUIRY',
  TENTATIVE = 'TENTATIVE',
  CONFIRMED = 'CONFIRMED',
  SETUP_IN_PROGRESS = 'SETUP_IN_PROGRESS',
  EVENT_IN_PROGRESS = 'EVENT_IN_PROGRESS',
  BREAKDOWN_IN_PROGRESS = 'BREAKDOWN_IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  NO_SHOW = 'NO_SHOW',
  BLOCKED = 'BLOCKED',
}

// Event Space Reservation Status Enum
export const eventReservationStatusEnum = pgEnum('event_reservation_status', [
  EventReservationStatus.INQUIRY,
  EventReservationStatus.TENTATIVE,
  EventReservationStatus.CONFIRMED,
  EventReservationStatus.SETUP_IN_PROGRESS,
  EventReservationStatus.EVENT_IN_PROGRESS,
  EventReservationStatus.BREAKDOWN_IN_PROGRESS,
  EventReservationStatus.COMPLETED,
  EventReservationStatus.CANCELLED,
  EventReservationStatus.NO_SHOW,
  EventReservationStatus.BLOCKED,
]);

export enum EventType {
  WEDDING = 'WEDDING',
  CORPORATE_EVENT = 'CORPORATE_EVENT',
  CONFERENCE = 'CONFERENCE',
  MEETING = 'MEETING',
  BIRTHDAY_PARTY = 'BIRTHDAY_PARTY',
  ANNIVERSARY = 'ANNIVERSARY',
  GRADUATION = 'GRADUATION',
  BABY_SHOWER = 'BABY_SHOWER',
  BRIDAL_SHOWER = 'BRIDAL_SHOWER',
  SEMINAR = 'SEMINAR',
  WORKSHOP = 'WORKSHOP',
  EXHIBITION = 'EXHIBITION',
  PRODUCT_LAUNCH = 'PRODUCT_LAUNCH',
  NETWORKING_EVENT = 'NETWORKING_EVENT',
  CHARITY_EVENT = 'CHARITY_EVENT',
  CULTURAL_EVENT = 'CULTURAL_EVENT',
  RELIGIOUS_EVENT = 'RELIGIOUS_EVENT',
  SPORTS_EVENT = 'SPORTS_EVENT',
  CONCERT = 'CONCERT',
  THEATER = 'THEATER',
  OTHER = 'OTHER',
}

// Event Type Enum
export const eventTypeEnum = pgEnum('event_type', [
  EventType.WEDDING,
  EventType.CORPORATE_EVENT,
  EventType.CONFERENCE,
  EventType.MEETING,
  EventType.BIRTHDAY_PARTY,
  EventType.ANNIVERSARY,
  EventType.GRADUATION,
  EventType.BABY_SHOWER,
  EventType.BRIDAL_SHOWER,
  EventType.SEMINAR,
  EventType.WORKSHOP,
  EventType.EXHIBITION,
  EventType.PRODUCT_LAUNCH,
  EventType.NETWORKING_EVENT,
  EventType.CHARITY_EVENT,
  EventType.CULTURAL_EVENT,
  EventType.RELIGIOUS_EVENT,
  EventType.SPORTS_EVENT,
  EventType.CONCERT,
  EventType.THEATER,
  EventType.OTHER,
]);

// Payment Status Enum is imported from common-fields.schema.ts

export enum DiscountType {
  PERCENTAGE = 'PERCENTAGE',
  FIXED_AMOUNT = 'FIXED_AMOUNT',
}

export enum TaxType {
  INCLUSIVE = 'INCLUSIVE',
  EXCLUSIVE = 'EXCLUSIVE',
  OUT_OF_SCOPE = 'OUT_OF_SCOPE',
}

export enum ReservationSource {
  ONLINE = 'ONLINE',
  PHONE = 'PHONE',
  WALK_IN = 'WALK_IN',
  EMAIL = 'EMAIL',
  AGENT = 'AGENT',
  CORPORATE = 'CORPORATE',
  REPEAT_CUSTOMER = 'REPEAT_CUSTOMER',
  REFERRAL = 'REFERRAL',
  OTHER = 'OTHER',
}

// Discount Type Enum
export const discountTypeEnum = pgEnum('discount_type', [
  DiscountType.PERCENTAGE,
  DiscountType.FIXED_AMOUNT,
]);

// Tax Type Enum
export const taxTypeEnum = pgEnum('tax_type', [
  TaxType.INCLUSIVE,
  TaxType.EXCLUSIVE,
  TaxType.OUT_OF_SCOPE,
]);

// Reservation Source Enum
export const reservationSourceEnum = pgEnum('reservation_source', [
  ReservationSource.ONLINE,
  ReservationSource.PHONE,
  ReservationSource.WALK_IN,
  ReservationSource.EMAIL,
  ReservationSource.AGENT,
  ReservationSource.CORPORATE,
  ReservationSource.REPEAT_CUSTOMER,
  ReservationSource.REFERRAL,
  ReservationSource.OTHER,
]);

// Event Space Reservations table
export const eventSpaceReservations = pgTable(
  'event_space_reservations',
  {
    ...createBaseEntityBusinessFields(business),

    // Reservation identification
    reservationNumber: text('reservation_number').notNull(),
    referenceNumber: text('reference_number'), // External booking reference

    // Space and customer references
    eventSpaceId: uuid('event_space_id')
      .notNull()
      .references(() => eventSpaces.id),
    customerId: uuid('customer_id').references(() => customers.id),

    // Event details
    eventName: text('event_name').notNull(),
    eventType: eventTypeEnum('event_type').notNull(),
    eventDescription: text('event_description'),

    // Date and time scheduling
    eventStartTime: timestamp('event_start_time').notNull(),
    eventEndTime: timestamp('event_end_time').notNull(),
    setupStartTime: timestamp('setup_start_time').notNull(),
    breakdownEndTime: timestamp('breakdown_end_time').notNull(),

    // Actual times (for tracking)
    actualSetupStartTime: timestamp('actual_setup_start_time'),
    actualEventStartTime: timestamp('actual_event_start_time'),
    actualEventEndTime: timestamp('actual_event_end_time'),
    actualBreakdownEndTime: timestamp('actual_breakdown_end_time'),

    // Occupancy and setup
    expectedGuestCount: integer('expected_guest_count').notNull(),
    confirmedGuestCount: integer('confirmed_guest_count').default(0),

    // Guest management preferences
    requiresIndividualGuestTracking: boolean(
      'requires_individual_guest_tracking',
    )
      .default(false)
      .notNull(), // For weddings/large events, often false. For corporate/small events, might be true
    guestListDeadline: timestamp('guest_list_deadline'), // When final guest count/list is due
    allowWalkIns: boolean('allow_walk_ins').default(false).notNull(), // Whether to allow additional guests on event day

    // Reservation status and type
    status: eventReservationStatusEnum('status')
      .default(EventReservationStatus.INQUIRY)
      .notNull(),
    reservationSource: reservationSourceEnum('reservation_source'),

    // Payment information
    paymentStatus: paymentStatusEnum('payment_status')
      .default(PaymentStatus.PENDING)
      .notNull(),
    subtotal: decimal('subtotal', { precision: 12, scale: 2 })
      .default('0.00')
      .notNull(),
    total: decimal('total', { precision: 12, scale: 2 })
      .default('0.00')
      .notNull(),
    depositRequired: decimal('deposit_required', {
      precision: 12,
      scale: 2,
    }).default('0.00'),
    depositPaid: decimal('deposit_paid', { precision: 12, scale: 2 }).default(
      '0.00',
    ),
    balanceDue: decimal('balance_due', { precision: 12, scale: 2 }).default(
      '0.00',
    ),

    // Discount information
    discountType: discountTypeEnum('discount_type'),
    discountValue: decimal('discount_value', {
      precision: 12,
      scale: 2,
    }).default('0.00'),
    discountAmount: decimal('discount_amount', {
      precision: 12,
      scale: 2,
    }).default('0.00'),
    discountReason: text('discount_reason'),

    // Tax information
    taxType: taxTypeEnum('tax_type').default(TaxType.INCLUSIVE).notNull(),
    defaultTaxRateId: uuid('default_tax_rate_id').references(() => taxes.id),
    taxAmount: decimal('tax_amount', {
      precision: 12,
      scale: 2,
    }).default('0.00'),

    // Event metadata
    notes: text('notes'),
    cancellationReason: text('cancellation_reason'),
    cancellationDate: timestamp('cancellation_date'),

    // Communication tracking
    confirmationSent: boolean('confirmation_sent').default(false).notNull(),
    confirmationSentAt: timestamp('confirmation_sent_at'),
    reminderSent: boolean('reminder_sent').default(false).notNull(),
    reminderSentAt: timestamp('reminder_sent_at'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'event_space_reservations'),
    reservationNumberIndex: index('event_reservations_number_index').on(
      t.reservationNumber,
    ),
    referenceNumberIndex: index('event_reservations_reference_index').on(
      t.referenceNumber,
    ),

    // Foreign key indexes
    eventSpaceIdIndex: index('event_reservations_space_id_index').on(
      t.eventSpaceId,
    ),
    customerIdIndex: index('event_reservations_customer_id_index').on(
      t.customerId,
    ),

    // Event details indexes
    eventNameIndex: index('event_reservations_event_name_index').on(
      t.eventName,
    ),
    eventTypeIndex: index('event_reservations_event_type_index').on(
      t.eventType,
    ),

    // Date and time indexes for availability queries
    eventStartTimeIndex: index('event_reservations_event_start_time_index').on(
      t.eventStartTime,
    ),
    eventEndTimeIndex: index('event_reservations_event_end_time_index').on(
      t.eventEndTime,
    ),
    setupStartTimeIndex: index('event_reservations_setup_start_time_index').on(
      t.setupStartTime,
    ),
    breakdownEndTimeIndex: index(
      'event_reservations_breakdown_end_time_index',
    ).on(t.breakdownEndTime),

    // Status and type indexes
    statusIndex: index('event_reservations_status_index').on(t.status),
    paymentStatusIndex: index('event_reservations_payment_status_index').on(
      t.paymentStatus,
    ),
    reservationSourceIndex: index('event_reservations_source_index').on(
      t.reservationSource,
    ),

    // Guest management indexes
    requiresIndividualGuestTrackingIndex: index(
      'event_reservations_requires_individual_guest_tracking_index',
    ).on(t.requiresIndividualGuestTracking),
    guestListDeadlineIndex: index(
      'event_reservations_guest_list_deadline_index',
    ).on(t.guestListDeadline),
    allowWalkInsIndex: index('event_reservations_allow_walk_ins_index').on(
      t.allowWalkIns,
    ),

    // Communication indexes
    confirmationSentIndex: index(
      'event_reservations_confirmation_sent_index',
    ).on(t.confirmationSent),

    // Tax rate indexes
    defaultTaxRateIdIndex: index('event_reservations_tax_rate_index').on(
      t.defaultTaxRateId,
    ),

    // Composite indexes for common queries
    businessStatusIndex: index('event_reservations_business_status_index').on(
      t.businessId,
      t.status,
    ),
    businessSpaceStatusIndex: index(
      'event_reservations_business_space_status_index',
    ).on(t.businessId, t.eventSpaceId, t.status),
    businessEventDateIndex: index(
      'event_reservations_business_event_date_index',
    ).on(t.businessId, t.eventStartTime, t.eventEndTime),
    spaceAvailabilityIndex: index('event_reservations_space_availability').on(
      t.eventSpaceId,
      t.setupStartTime,
      t.breakdownEndTime,
      t.status,
    ),
    customerReservationsIndex: index('event_reservations_customer_index').on(
      t.customerId,
      t.status,
      t.eventStartTime,
    ),
    paymentDueIndex: index('event_reservations_payment_due_index').on(
      t.businessId,
      t.paymentStatus,
      t.eventStartTime,
    ),

    // Unique constraints with soft deletion support
    uniqueBusinessReservationNumber: uniqueIndex(
      'event_reservations_business_number_unique',
    )
      .on(t.businessId, t.reservationNumber)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessReferenceNumber: uniqueIndex(
      'event_reservations_business_reference_unique',
    )
      .on(t.businessId, t.referenceNumber)
      .where(sql`${t.isDeleted} = false`),

    // Availability check constraint (overlapping reservations for same space)
    // Note: This should be handled at application level or with more complex constraints
    uniqueSpaceTimeSlot: index('event_reservations_space_time_overlap_check')
      .on(t.eventSpaceId, t.setupStartTime, t.breakdownEndTime, t.status)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Junction table for event space reservation - asset assignments (many-to-many)
export const eventSpaceReservationAssets = pgTable(
  'event_space_reservation_assets',
  {
    ...createBaseEntityBusinessFields(business),
    reservationId: uuid('reservation_id')
      .notNull()
      .references(() => eventSpaceReservations.id, {
        onDelete: 'cascade',
      }),
    assetId: uuid('asset_id')
      .notNull()
      .references(() => assets.id, {
        onDelete: 'cascade',
      }),

    // Assignment details
    notes: text('notes'), // Notes about this specific asset assignment
    assignedById: uuid('assigned_by_id')
      .notNull()
      .references(() => staffMembers.id),
    assignedAt: timestamp('assigned_at').defaultNow().notNull(),
    returnedAt: timestamp('returned_at'),

    // Asset condition tracking
    conditionOnAssignment: text('condition_on_assignment'), // Condition when assigned
    conditionOnReturn: text('condition_on_return'), // Condition when returned
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'event_space_reservation_assets'),
    // Primary indexes
    reservationIdIndex: index(
      'event_reservation_assets_reservation_id_index',
    ).on(t.reservationId),
    assetIdIndex: index('event_reservation_assets_asset_id_index').on(
      t.assetId,
    ),
    assignedByIdIndex: index(
      'event_reservation_assets_assigned_by_id_index',
    ).on(t.assignedById),

    // Time-based indexes
    assignedAtIndex: index('event_reservation_assets_assigned_at_index').on(
      t.assignedAt,
    ),
    returnedAtIndex: index('event_reservation_assets_returned_at_index').on(
      t.returnedAt,
    ),

    // Composite indexes for common queries
    reservationAssetIndex: index(
      'event_reservation_assets_reservation_asset_index',
    ).on(t.reservationId, t.assetId),
    assetAssignmentStatusIndex: index(
      'event_reservation_assets_assignment_status_index',
    ).on(t.assetId, t.assignedAt, t.returnedAt),

    // Unique constraints
    uniqueReservationAsset: uniqueIndex('event_reservation_assets_unique')
      .on(t.reservationId, t.assetId)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Junction table for event space reservation - task assignments (many-to-many)
export const eventSpaceReservationTasks = pgTable(
  'event_space_reservation_tasks',
  {
    ...createBaseEntityBusinessFields(business),
    reservationId: uuid('reservation_id')
      .notNull()
      .references(() => eventSpaceReservations.id, {
        onDelete: 'cascade',
      }),
    staffMemberId: uuid('staff_member_id')
      .notNull()
      .references(() => staffMembers.id, {
        onDelete: 'cascade',
      }),
    taskId: uuid('task_id')
      .notNull()
      .references(() => tasks.id, {
        onDelete: 'cascade',
      }),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'event_space_reservation_tasks'),
    // Primary indexes
    reservationIdIndex: index(
      'event_reservation_tasks_reservation_id_index',
    ).on(t.reservationId),
    staffMemberIdIndex: index(
      'event_reservation_tasks_staff_member_id_index',
    ).on(t.staffMemberId),
    taskIdIndex: index('event_reservation_tasks_task_id_index').on(t.taskId),

    // Composite indexes for common queries
    reservationStaffTaskIndex: index(
      'event_reservation_tasks_reservation_staff_task_index',
    ).on(t.reservationId, t.staffMemberId, t.taskId),
    staffTaskIndex: index('event_reservation_tasks_staff_task_index').on(
      t.staffMemberId,
      t.taskId,
    ),
    reservationTaskIndex: index(
      'event_reservation_tasks_reservation_task_index',
    ).on(t.reservationId, t.taskId),

    // Unique constraints
    uniqueReservationStaffTask: uniqueIndex('event_reservation_tasks_unique')
      .on(t.reservationId, t.staffMemberId, t.taskId)
      .where(sql`${t.isDeleted} = false`),
  }),
);
