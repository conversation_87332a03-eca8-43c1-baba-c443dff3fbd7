import {
  pgTable,
  text,
  uuid,
  decimal,
  index,
  uniqueIndex,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';

import { business } from './business.schema';
import { employeeSalaries } from './employee-salary.schema';
import { deductionTypes } from './deduction-types.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

/**
 * Employee Salary Deductions Table
 * Links employee salaries with their deductions and stores specific deduction amounts
 */
export const employeeSalaryDeductions = pgTable(
  'employee_salary_deductions',
  {
    ...createBaseEntityBusinessFields(business),
    employeeSalaryId: uuid('employee_salary_id')
      .notNull()
      .references(() => employeeSalaries.id),
    deductionTypeId: uuid('deduction_type_id')
      .notNull()
      .references(() => deductionTypes.id),
    amount: decimal('amount', { precision: 12, scale: 2 }),
    notes: text('notes'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'employee_salary_deductions'),
    employeeSalaryIdIndex: index(
      'employee_salary_deductions_employee_salary_id_index',
    ).on(t.employeeSalaryId),
    deductionTypeIdIndex: index(
      'employee_salary_deductions_deduction_type_id_index',
    ).on(t.deductionTypeId),
    uniqueEmployeeSalaryDeductionActive: uniqueIndex(
      'employee_salary_deductions_employee_salary_deduction_active_unique',
    )
      .on(t.employeeSalaryId, t.deductionTypeId)
      .where(sql`${t.isDeleted} = false`),
  }),
);
