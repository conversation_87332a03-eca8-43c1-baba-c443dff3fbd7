import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  decimal,
  date,
  timestamp,
  boolean,
} from 'drizzle-orm/pg-core';
import { business } from './business.schema';
import { assets } from './assets.schema';
import { accounts } from './accounts.schema';
import { paymentMethods } from './payment-methods.schema';
import { taxes } from './taxes.schema';
import { suppliers } from './suppliers.schema';
import { staffMembers } from './staff.schema';
import { RepairStatus, RepairPriority, RepairType } from '../../shared/types';
import { amountTypeEnum, AmountType } from './expenses.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

// Repair Status Enum
export const repairStatusEnum = pgEnum('repair_status', [
  RepairStatus.REPORTED,
  RepairStatus.DIAGNOSED,
  RepairStatus.APPROVED,
  RepairStatus.IN_PROGRESS,
  RepairStatus.COMPLETED,
  RepairStatus.ON_HOLD,
  RepairStatus.CANCELLED,
]);

// Repair Priority Enum
export const repairPriorityEnum = pgEnum('repair_priority', [
  RepairPriority.LOW,
  RepairPriority.NORMAL,
  RepairPriority.HIGH,
  RepairPriority.URGENT,
  RepairPriority.CRITICAL,
]);

// Repair Type Enum
export const repairTypeEnum = pgEnum('repair_type', [
  RepairType.EMERGENCY,
  RepairType.SCHEDULED,
  RepairType.CORRECTIVE,
  RepairType.BREAKDOWN,
  RepairType.WARRANTY,
]);

export const assetRepairOrders = pgTable(
  'asset_repair_orders',
  {
    ...createBaseEntityBusinessFields(business),
    assetId: uuid('asset_id')
      .notNull()
      .references(() => assets.id, { onDelete: 'cascade' }),

    // Repair Order Information
    repairOrderNumber: text('repair_order_number').notNull(),
    title: text('title').notNull(),
    problemDescription: text('problem_description').notNull(),
    diagnosisNotes: text('diagnosis_notes'),
    repairSolution: text('repair_solution'),

    // Issue Details
    repairType: repairTypeEnum('repair_type').default(RepairType.CORRECTIVE),

    // Status and Priority
    status: repairStatusEnum('status').default(RepairStatus.REPORTED),
    priority: repairPriorityEnum('priority').default(RepairPriority.NORMAL),

    // Dates and Timeline
    reportedAt: timestamp('reported_at').defaultNow().notNull(),
    targetCompletionDate: timestamp('target_completion_date'),
    actualStartDate: timestamp('actual_start_date'),
    completedAt: timestamp('actual_completion_date'),
    completionNotes: text('completion_notes'),

    // Staff Assignment
    reportedBy: uuid('reported_by')
      .notNull()
      .references(() => staffMembers.id),
    assignedTo: uuid('assigned_to').references(() => staffMembers.id),

    // External Support
    supplierId: uuid('supplier_id').references(() => suppliers.id),
    isWarrantyRepair: boolean('is_warranty_repair').default(false),
    warrantyClaimNumber: text('warranty_claim_number'),

    // Financial Information
    expenseAccountId: uuid('expense_account_id')
      .notNull()
      .references(() => accounts.id),
    paymentAccountId: uuid('payment_account_id').references(() => accounts.id),
    paymentDate: date('payment_date'),
    paymentMethodId: uuid('payment_method_id').references(
      () => paymentMethods.id,
    ),
    paymentReferenceNumber: text('payment_reference_number'),

    // Amount calculation
    amountType: amountTypeEnum('amount_type')
      .default(AmountType.EXCLUSIVE_OF_TAX)
      .notNull(),

    // Totals
    subtotal: decimal('subtotal', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    total: decimal('total', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    taxId: uuid('tax_id').references(() => taxes.id),

    qualityCheckBy: uuid('quality_check_by').references(() => staffMembers.id),
  },
  (t) => ({
    // Standard base entity indexes
    ...createBaseEntityBusinessIndexes(t, 'asset_repair_orders'),

    // Entity-specific indexes
    assetIdIndex: index('asset_repair_orders_asset_id_index').on(t.assetId),
    repairOrderNumberIndex: index(
      'asset_repair_orders_repair_order_number_index',
    ).on(t.repairOrderNumber),
    titleIndex: index('asset_repair_orders_title_index').on(t.title),
    statusIndex: index('asset_repair_orders_status_index').on(t.status),
    priorityIndex: index('asset_repair_orders_priority_index').on(t.priority),
    repairTypeIndex: index('asset_repair_orders_repair_type_index').on(
      t.repairType,
    ),
    reportedAtIndex: index('asset_repair_orders_reported_at_index').on(
      t.reportedAt,
    ),
    targetCompletionDateIndex: index(
      'asset_repair_orders_target_completion_date_index',
    ).on(t.targetCompletionDate),
    actualStartDateIndex: index(
      'asset_repair_orders_actual_start_date_index',
    ).on(t.actualStartDate),
    completedAtIndex: index('asset_repair_orders_completed_at_index').on(
      t.completedAt,
    ),
    reportedByIndex: index('asset_repair_orders_reported_by_index').on(
      t.reportedBy,
    ),
    assignedToIndex: index('asset_repair_orders_assigned_to_index').on(
      t.assignedTo,
    ),
    supplierIdIndex: index('asset_repair_orders_supplier_id_index').on(
      t.supplierId,
    ),
    expenseAccountIdIndex: index(
      'asset_repair_orders_expense_account_id_index',
    ).on(t.expenseAccountId),
    paymentAccountIdIndex: index(
      'asset_repair_orders_payment_account_id_index',
    ).on(t.paymentAccountId),
    paymentDateIndex: index('asset_repair_orders_payment_date_index').on(
      t.paymentDate,
    ),
    paymentMethodIdIndex: index(
      'asset_repair_orders_payment_method_id_index',
    ).on(t.paymentMethodId),
    taxIdIndex: index('asset_repair_orders_tax_id_index').on(t.taxId),
    qualityCheckByIndex: index('asset_repair_orders_quality_check_by_index').on(
      t.qualityCheckBy,
    ),
    isWarrantyRepairIndex: index(
      'asset_repair_orders_is_warranty_repair_index',
    ).on(t.isWarrantyRepair),
  }),
);
