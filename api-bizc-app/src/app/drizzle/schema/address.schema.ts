import {
  index,
  pgEnum,
  pgTable,
  text,
  boolean,
  uuid,
} from 'drizzle-orm/pg-core';
import { AddressType } from '../../shared/types';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { business } from './business.schema';

export const addressTypeEnum = pgEnum('address_type', [
  AddressType.BUSINESS,
  AddressType.SHIPPING,
  AddressType.LOCATION,
  AddressType.BILLING,
  AddressType.HOME,
  AddressType.EMERGENCY_CONTACT,
  AddressType.OTHER,
]);

export const addresses = pgTable(
  'addresses',
  {
    ...createBaseEntityBusinessFields(business),
    street: text('street').notNull(),
    city: text('city').notNull(),
    state: text('state').notNull(),
    zipCode: text('zip_code').notNull(),
    country: text('country').notNull(),
    addressType: addressTypeEnum('address_type')
      .default(AddressType.BUSINESS)
      .notNull(),
    userId: uuid('user_id'),
    isDefault: boolean('is_default').default(false),
  },
  (t) => ({
    // Standard indexes for base entity fields
    ...createBaseEntityBusinessIndexes(t, 'addresses'),

    // Entity-specific indexes
    userIdIndex: index('addresses_user_id_index').on(t.userId),
    addressTypeIndex: index('addresses_address_type_index').on(t.addressType),
    isDefaultIndex: index('addresses_is_default_index').on(t.isDefault),
  }),
);
