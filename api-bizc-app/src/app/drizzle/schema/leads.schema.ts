import {
  boolean,
  decimal,
  index,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uuid,
  uniqueIndex,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import { staffMembers } from './staff.schema';
import { addresses } from './address.schema';
import { media } from './media.schema';
import { locations } from './locations.schema';
import {
  personalNameFields,
  extendedContactInfoFields,
  locationAllocationFields,
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import {
  LeadType,
  LeadStatus,
  LeadSource,
  LeadPriority,
} from '../../shared/types';

export const leadTypeEnum = pgEnum('lead_type', [
  LeadType.BUSINESS,
  LeadType.INDIVIDUAL,
]);

export const leadStatusEnum = pgEnum('lead_status', [
  LeadStatus.NEW,
  LeadStatus.CONTACTED,
  LeadStatus.QUALIFIED,
  LeadStatus.PROPOSAL,
  LeadStatus.NEGOTIATION,
  LeadStatus.CLOSED_WON,
  LeadStatus.CLOSED_LOST,
]);

export const leadSourceEnum = pgEnum('lead_source', [
  LeadSource.WEBSITE,
  LeadSource.SOCIAL_MEDIA,
  LeadSource.REFERRAL,
  LeadSource.TRADE_SHOW,
  LeadSource.COLD_CALL,
  LeadSource.EMAIL_MARKETING,
  LeadSource.PARTNER,
  LeadSource.WEBINAR,
  LeadSource.OTHER,
]);

export const leadPriorityEnum = pgEnum('lead_priority', [
  LeadPriority.LOW,
  LeadPriority.MEDIUM,
  LeadPriority.HIGH,
]);

export const leads = pgTable(
  'leads',
  {
    ...createBaseEntityBusinessFields(business),

    // Personal Information (using reusable fields)
    ...personalNameFields,
    leadDisplayName: text('lead_display_name').notNull(),
    companyName: text('company_name'),
    leadType: leadTypeEnum('lead_type').default(LeadType.INDIVIDUAL),

    // Contact Information (using reusable fields)
    ...extendedContactInfoFields,

    // Lead Management
    leadStatus: leadStatusEnum('lead_status').default(LeadStatus.NEW).notNull(),
    leadSource: leadSourceEnum('lead_source'),
    priority: leadPriorityEnum('priority').default(LeadPriority.MEDIUM),
    estimatedValue: decimal('estimated_value', { precision: 15, scale: 2 }),
    estimatedClosingDate: timestamp('estimated_closing_date'),
    isConverted: boolean('is_converted').default(false).notNull(),

    // Location allocation (using reusable fields)
    ...locationAllocationFields,

    // References to other schemas
    leadOwnerId: uuid('lead_owner_id').references(() => staffMembers.id),
    addressId: uuid('address_id').references(() => addresses.id),
    profileImageId: uuid('profile_image_id').references(() => media.id),

    // Additional Information
    notes: text('notes'),
    lastContactDate: timestamp('last_contact_date'),
  },
  (t) => ({
    idIndex: index('leads_id_index').on(t.id),
    ...createBaseEntityBusinessIndexes(t, 'leads'),
    leadDisplayNameIndex: index('leads_display_name_index').on(
      t.leadDisplayName,
    ),
    emailIndex: index('leads_email_index').on(t.email),
    phoneNumberIndex: index('leads_phone_number_index').on(t.phoneNumber),
    mobileNumberIndex: index('leads_mobile_number_index').on(t.mobileNumber),
    companyNameIndex: index('leads_company_name_index').on(t.companyName),
    leadStatusIndex: index('leads_status_index').on(t.leadStatus),
    leadSourceIndex: index('leads_source_index').on(t.leadSource),
    priorityIndex: index('leads_priority_index').on(t.priority),
    leadOwnerIdIndex: index('leads_owner_id_index').on(t.leadOwnerId),
    addressIdIndex: index('leads_address_id_index').on(t.addressId),
    profileImageIdIndex: index('leads_profile_image_id_index').on(
      t.profileImageId,
    ),
    isConverted: index('leads_is_converted_index').on(t.isConverted),
    isAllocatedToAllLocations: index(
      'leads_is_allocated_to_all_locations_index',
    ).on(t.isAllocatedToAllLocations),
    lastContactDateIndex: index('leads_last_contact_date_index').on(
      t.lastContactDate,
    ),
    estimatedClosingDateIndex: index('leads_estimated_closing_date_index').on(
      t.estimatedClosingDate,
    ),

    // Optimized composite indexes with soft deletion support
    businessDisplayNameIndex: index('leads_business_display_name_index')
      .on(t.businessId, t.leadDisplayName)
      .where(sql`${t.isDeleted} = false`),
    businessStatusIndex: index('leads_business_status_index')
      .on(t.businessId, t.leadStatus)
      .where(sql`${t.isDeleted} = false`),
    businessSourceIndex: index('leads_business_source_index')
      .on(t.businessId, t.leadSource)
      .where(sql`${t.isDeleted} = false`),
    businessOwnerIndex: index('leads_business_owner_index')
      .on(t.businessId, t.leadOwnerId)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessDisplayName: uniqueIndex('leads_business_display_name_unique')
      .on(t.businessId, t.leadDisplayName)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessEmail: uniqueIndex('leads_business_email_unique')
      .on(t.businessId, t.email)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Junction table for lead locations (many-to-many relationship)
export const leadLocations = pgTable(
  'lead_locations',
  {
    ...createBaseEntityBusinessFields(business),
    leadId: uuid('lead_id')
      .notNull()
      .references(() => leads.id, { onDelete: 'cascade' }),
    locationId: uuid('location_id')
      .notNull()
      .references(() => locations.id, { onDelete: 'cascade' }),
  },
  (t) => ({
    leadIdIndex: index('lead_locations_lead_id_index').on(t.leadId),
    locationIdIndex: index('lead_locations_location_id_index').on(t.locationId),
    uniqueLeadLocation: uniqueIndex('lead_locations_unique').on(
      t.leadId,
      t.locationId,
    ),
  }),
);
