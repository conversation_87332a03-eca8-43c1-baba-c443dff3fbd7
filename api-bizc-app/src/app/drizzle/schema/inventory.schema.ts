import {
  date,
  decimal,
  index,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uuid,
  uniqueIndex,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import { locations } from './locations.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

// Inventory Transaction Type Enum
export enum InventoryTransactionType {
  RECEIPT = 'receipt',
  ISSUE = 'issue',
  TRANSFER_IN = 'transfer_in',
  TRANSFER_OUT = 'transfer_out',
  ADJUSTMENT = 'adjustment',
  COUNT = 'count',
}

// Serial Number Status Enum
export enum SerialNumberStatus {
  AVAILABLE = 'available',
  SOLD = 'sold',
  RESERVED = 'reserved',
  DAMAGED = 'damaged',
  RETURNED = 'returned',
  WARRANTY = 'warranty',
}

// Inventory Reservation Type Enum
export enum InventoryReservationType {
  QUOTATION = 'quotation',
  SALES_ORDER = 'sales_order',
  TRANSFER = 'transfer',
  HOLD = 'hold',
}

// Reservation Status Enum
export enum ReservationStatus {
  ACTIVE = 'active',
  EXPIRED = 'expired',
  CANCELLED = 'cancelled',
  FULFILLED = 'fulfilled',
}

// Stock Transfer Status Enum
export enum StockTransferStatus {
  DRAFT = 'draft',
  IN_TRANSIT = 'in_transit',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

// Stock Adjustment Type Enum
export enum StockAdjustmentType {
  PHYSICAL_COUNT = 'physical_count',
  DAMAGE = 'damage',
  EXPIRY = 'expiry',
  CORRECTION = 'correction',
  OTHER = 'other',
}

// Stock Adjustment Status Enum
export enum StockAdjustmentStatus {
  DRAFT = 'draft',
  APPROVED = 'approved',
  POSTED = 'posted',
  CANCELLED = 'cancelled',
}

export const inventoryTransactionTypeEnum = pgEnum(
  'inventory_transaction_type',
  [
    InventoryTransactionType.RECEIPT,
    InventoryTransactionType.ISSUE,
    InventoryTransactionType.TRANSFER_IN,
    InventoryTransactionType.TRANSFER_OUT,
    InventoryTransactionType.ADJUSTMENT,
    InventoryTransactionType.COUNT,
  ],
);

export const serialNumberStatusEnum = pgEnum('serial_number_status', [
  SerialNumberStatus.AVAILABLE,
  SerialNumberStatus.SOLD,
  SerialNumberStatus.RESERVED,
  SerialNumberStatus.DAMAGED,
  SerialNumberStatus.RETURNED,
  SerialNumberStatus.WARRANTY,
]);

export const inventoryReservationTypeEnum = pgEnum(
  'inventory_reservation_type',
  [
    InventoryReservationType.QUOTATION,
    InventoryReservationType.SALES_ORDER,
    InventoryReservationType.TRANSFER,
    InventoryReservationType.HOLD,
  ],
);

export const reservationStatusEnum = pgEnum('reservation_status', [
  ReservationStatus.ACTIVE,
  ReservationStatus.EXPIRED,
  ReservationStatus.CANCELLED,
  ReservationStatus.FULFILLED,
]);

export const stockTransferStatusEnum = pgEnum('stock_transfer_status', [
  StockTransferStatus.DRAFT,
  StockTransferStatus.IN_TRANSIT,
  StockTransferStatus.COMPLETED,
  StockTransferStatus.CANCELLED,
]);

export const stockAdjustmentTypeEnum = pgEnum('stock_adjustment_type', [
  StockAdjustmentType.PHYSICAL_COUNT,
  StockAdjustmentType.DAMAGE,
  StockAdjustmentType.EXPIRY,
  StockAdjustmentType.CORRECTION,
  StockAdjustmentType.OTHER,
]);

export const stockAdjustmentStatusEnum = pgEnum('stock_adjustment_status', [
  StockAdjustmentStatus.DRAFT,
  StockAdjustmentStatus.APPROVED,
  StockAdjustmentStatus.POSTED,
  StockAdjustmentStatus.CANCELLED,
]);

// Inventory table
export const inventory = pgTable(
  'inventory',
  {
    ...createBaseEntityBusinessFields(business),
    productId: uuid('product_id').notNull(), // References PRODUCTS table
    variantId: uuid('variant_id'), // References PRODUCT_VARIANTS table
    locationId: uuid('location_id')
      .notNull()
      .references(() => locations.id),
    quantityOnHand: decimal('quantity_on_hand', { precision: 15, scale: 3 })
      .default('0.000')
      .notNull(),
    quantityReserved: decimal('quantity_reserved', { precision: 15, scale: 3 })
      .default('0.000')
      .notNull(),
    quantityAvailable: decimal('quantity_available', {
      precision: 15,
      scale: 3,
    })
      .default('0.000')
      .notNull(),
    lastCountDate: timestamp('last_count_date'),
  },
  (t) => ({
    // Indexes for performance
    ...createBaseEntityBusinessIndexes(t, 'inventory'),
    productIdIndex: index('inventory_product_id_index').on(t.productId),
    variantIdIndex: index('inventory_variant_id_index').on(t.variantId),
    locationIdIndex: index('inventory_location_id_index').on(t.locationId),
    lastCountDateIndex: index('inventory_last_count_date_index').on(
      t.lastCountDate,
    ),

    // Composite indexes for common query patterns
    businessProductLocationIndex: index(
      'inventory_business_product_location_index',
    )
      .on(t.businessId, t.productId, t.locationId)
      .where(sql`${t.isDeleted} = false`),
    businessLocationIndex: index('inventory_business_location_index')
      .on(t.businessId, t.locationId)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraint for product-variant-location combination
    uniqueProductVariantLocation: uniqueIndex(
      'inventory_product_variant_location_unique',
    )
      .on(t.businessId, t.productId, t.variantId, t.locationId)
      .where(sql`${t.isDeleted} = false`),

    // Audit indexes
    createdAtIndex: index('inventory_created_at_index').on(t.createdAt),
    updatedAtIndex: index('inventory_updated_at_index').on(t.updatedAt),
  }),
);

// Inventory Transactions table
export const inventoryTransactions = pgTable(
  'inventory_transactions',
  {
    ...createBaseEntityBusinessFields(business),
    inventoryId: uuid('inventory_id')
      .notNull()
      .references(() => inventory.id, { onDelete: 'cascade' }),
    serialId: uuid('serial_id'), // References SERIAL_NUMBERS table
    batchId: uuid('batch_id'), // References BATCH_NUMBERS table
    transactionType: inventoryTransactionTypeEnum('transaction_type').notNull(),
    quantity: decimal('quantity', { precision: 15, scale: 3 }).notNull(),
    unitCost: decimal('unit_cost', { precision: 10, scale: 2 })
      .default('0.00')
      .notNull(),
    referenceType: text('reference_type'),
    referenceId: uuid('reference_id'),
    reasonCode: text('reason_code'),
    notes: text('notes'),
    transactionDate: timestamp('transaction_date').notNull(),
  },
  (t) => ({
    // Indexes for performance
    ...createBaseEntityBusinessIndexes(t, 'inventory_transactions'),
    inventoryIdIndex: index('inventory_transactions_inventory_id_index').on(
      t.inventoryId,
    ),
    serialIdIndex: index('inventory_transactions_serial_id_index').on(
      t.serialId,
    ),
    batchIdIndex: index('inventory_transactions_batch_id_index').on(t.batchId),
    transactionTypeIndex: index(
      'inventory_transactions_transaction_type_index',
    ).on(t.transactionType),
    transactionDateIndex: index(
      'inventory_transactions_transaction_date_index',
    ).on(t.transactionDate),
    referenceTypeIndex: index('inventory_transactions_reference_type_index').on(
      t.referenceType,
    ),
    referenceIdIndex: index('inventory_transactions_reference_id_index').on(
      t.referenceId,
    ),

    // Composite indexes for common query patterns
    businessTransactionDateIndex: index(
      'inventory_transactions_business_transaction_date_index',
    ).on(t.businessId, t.transactionDate),
    businessInventoryDateIndex: index(
      'inventory_transactions_business_inventory_date_index',
    ).on(t.businessId, t.inventoryId, t.transactionDate),
    businessTypeIndex: index('inventory_transactions_business_type_index').on(
      t.businessId,
      t.transactionType,
    ),

    // Audit indexes
    createdAtIndex: index('inventory_transactions_created_at_index').on(
      t.createdAt,
    ),
  }),
);

// Serial Numbers table
export const serialNumbers = pgTable(
  'serial_numbers',
  {
    ...createBaseEntityBusinessFields(business),
    productId: uuid('product_id').notNull(), // References PRODUCTS table
    variantId: uuid('variant_id'), // References PRODUCT_VARIANTS table
    serialNumber: text('serial_number').notNull(),
    imeiNumber: text('imei_number'),
    imei2Number: text('imei2_number'),
    status: serialNumberStatusEnum('status')
      .default(SerialNumberStatus.AVAILABLE)
      .notNull(),
    locationId: uuid('location_id').references(() => locations.id),
    purchaseOrderId: uuid('purchase_order_id'), // References PURCHASE_ORDERS table
    salesOrderId: uuid('sales_order_id'), // References SALES_ORDERS table
    customerId: uuid('customer_id'), // References CUSTOMERS table
    purchaseDate: date('purchase_date'),
    salesDate: date('sales_date'),
    warrantyExpiry: date('warranty_expiry'),
    purchaseCost: decimal('purchase_cost', { precision: 10, scale: 2 })
      .default('0.00')
      .notNull(),
    sellingPrice: decimal('selling_price', { precision: 10, scale: 2 })
      .default('0.00')
      .notNull(),
    notes: text('notes'),
  },
  (t) => ({
    // Indexes for performance
    ...createBaseEntityBusinessIndexes(t, 'serial_numbers'),
    productIdIndex: index('serial_numbers_product_id_index').on(t.productId),
    variantIdIndex: index('serial_numbers_variant_id_index').on(t.variantId),
    locationIdIndex: index('serial_numbers_location_id_index').on(t.locationId),
    customerIdIndex: index('serial_numbers_customer_id_index').on(t.customerId),
    statusIndex: index('serial_numbers_status_index').on(t.status),
    purchaseDateIndex: index('serial_numbers_purchase_date_index').on(
      t.purchaseDate,
    ),
    salesDateIndex: index('serial_numbers_sales_date_index').on(t.salesDate),
    warrantyExpiryIndex: index('serial_numbers_warranty_expiry_index').on(
      t.warrantyExpiry,
    ),

    // Unique constraints
    uniqueSerialNumber: uniqueIndex('serial_numbers_serial_number_unique')
      .on(t.businessId, t.serialNumber)
      .where(sql`${t.isDeleted} = false`),
    uniqueImeiNumber: uniqueIndex('serial_numbers_imei_number_unique')
      .on(t.businessId, t.imeiNumber)
      .where(sql`${t.imeiNumber} IS NOT NULL AND ${t.isDeleted} = false`),

    // Composite indexes for common query patterns
    businessProductIndex: index('serial_numbers_business_product_index')
      .on(t.businessId, t.productId)
      .where(sql`${t.isDeleted} = false`),
    businessStatusIndex: index('serial_numbers_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessLocationIndex: index('serial_numbers_business_location_index')
      .on(t.businessId, t.locationId)
      .where(sql`${t.isDeleted} = false`),

    // Audit indexes
    createdAtIndex: index('serial_numbers_created_at_index').on(t.createdAt),
    updatedAtIndex: index('serial_numbers_updated_at_index').on(t.updatedAt),
  }),
);

// Batch Numbers table
export const batchNumbers = pgTable(
  'batch_numbers',
  {
    ...createBaseEntityBusinessFields(business),
    productId: uuid('product_id').notNull(), // References PRODUCTS table
    variantId: uuid('variant_id'), // References PRODUCT_VARIANTS table
    batchNumber: text('batch_number').notNull(),
    manufactureDate: date('manufacture_date'),
    expiryDate: date('expiry_date'),
    quantity: decimal('quantity', { precision: 15, scale: 3 })
      .default('0.000')
      .notNull(),
    availableQuantity: decimal('available_quantity', {
      precision: 15,
      scale: 3,
    })
      .default('0.000')
      .notNull(),
    locationId: uuid('location_id').references(() => locations.id),
    notes: text('notes'),
  },
  (t) => ({
    // Indexes for performance
    ...createBaseEntityBusinessIndexes(t, 'batch_numbers'),
    productIdIndex: index('inventory_batch_numbers_product_id_index').on(
      t.productId,
    ),
    variantIdIndex: index('inventory_batch_numbers_variant_id_index').on(
      t.variantId,
    ),
    locationIdIndex: index('inventory_batch_numbers_location_id_index').on(
      t.locationId,
    ),
    manufactureDateIndex: index(
      'inventory_batch_numbers_manufacture_date_index',
    ).on(t.manufactureDate),
    expiryDateIndex: index('inventory_batch_numbers_expiry_date_index').on(
      t.expiryDate,
    ),

    // Unique constraint
    uniqueBatchNumber: uniqueIndex(
      'inventory_batch_numbers_batch_number_unique',
    )
      .on(t.businessId, t.batchNumber)
      .where(sql`${t.isDeleted} = false`),

    // Composite indexes for common query patterns
    businessProductIndex: index(
      'inventory_batch_numbers_business_product_index',
    )
      .on(t.businessId, t.productId)
      .where(sql`${t.isDeleted} = false`),
    businessLocationIndex: index(
      'inventory_batch_numbers_business_location_index',
    )
      .on(t.businessId, t.locationId)
      .where(sql`${t.isDeleted} = false`),
    businessExpiryIndex: index('inventory_batch_numbers_business_expiry_index')
      .on(t.businessId, t.expiryDate)
      .where(sql`${t.isDeleted} = false`),

    // Audit indexes
    createdAtIndex: index('inventory_batch_numbers_created_at_index').on(
      t.createdAt,
    ),
    updatedAtIndex: index('inventory_batch_numbers_updated_at_index').on(
      t.updatedAt,
    ),
  }),
);

// Inventory Reservations table
export const inventoryReservations = pgTable(
  'inventory_reservations',
  {
    ...createBaseEntityBusinessFields(business),
    productId: uuid('product_id').notNull(), // References PRODUCTS table
    variantId: uuid('variant_id'), // References PRODUCT_VARIANTS table
    locationId: uuid('location_id')
      .notNull()
      .references(() => locations.id),
    reservationType: inventoryReservationTypeEnum('reservation_type').notNull(),
    referenceId: uuid('reference_id').notNull(),
    quantityReserved: decimal('quantity_reserved', {
      precision: 15,
      scale: 3,
    }).notNull(),
    expiryDate: date('expiry_date'),
    status: reservationStatusEnum('status')
      .default(ReservationStatus.ACTIVE)
      .notNull(),
    notes: text('notes'),
  },
  (t) => ({
    // Indexes for performance
    ...createBaseEntityBusinessIndexes(t, 'inventory_reservations'),
    productIdIndex: index('inventory_reservations_product_id_index').on(
      t.productId,
    ),
    variantIdIndex: index('inventory_reservations_variant_id_index').on(
      t.variantId,
    ),
    locationIdIndex: index('inventory_reservations_location_id_index').on(
      t.locationId,
    ),
    reservationTypeIndex: index(
      'inventory_reservations_reservation_type_index',
    ).on(t.reservationType),
    referenceIdIndex: index('inventory_reservations_reference_id_index').on(
      t.referenceId,
    ),
    statusIndex: index('inventory_reservations_status_index').on(t.status),
    expiryDateIndex: index('inventory_reservations_expiry_date_index').on(
      t.expiryDate,
    ),

    // Composite indexes for common query patterns
    businessProductLocationIndex: index(
      'inventory_reservations_business_product_location_index',
    )
      .on(t.businessId, t.productId, t.locationId)
      .where(sql`${t.isDeleted} = false`),
    businessStatusIndex: index('inventory_reservations_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessTypeIndex: index('inventory_reservations_business_type_index')
      .on(t.businessId, t.reservationType)
      .where(sql`${t.isDeleted} = false`),

    // Audit indexes
    createdAtIndex: index('inventory_reservations_created_at_index').on(
      t.createdAt,
    ),
    updatedAtIndex: index('inventory_reservations_updated_at_index').on(
      t.updatedAt,
    ),
  }),
);

// Stock Transfers table
export const stockTransfers = pgTable(
  'stock_transfers',
  {
    ...createBaseEntityBusinessFields(business),
    transferNumber: text('transfer_number').notNull(),
    fromLocationId: uuid('from_location_id')
      .notNull()
      .references(() => locations.id),
    toLocationId: uuid('to_location_id')
      .notNull()
      .references(() => locations.id),
    transferDate: date('transfer_date').notNull(),
    status: stockTransferStatusEnum('status')
      .default(StockTransferStatus.DRAFT)
      .notNull(),
    transferReason: text('transfer_reason'),
    notes: text('notes'),
    requestedBy: uuid('requested_by').notNull(),
    approvedBy: uuid('approved_by'),
    shippedBy: uuid('shipped_by'),
    receivedBy: uuid('received_by'),
    shippedDate: timestamp('shipped_date'),
    receivedDate: timestamp('received_date'),
  },
  (t) => ({
    // Indexes for performance
    ...createBaseEntityBusinessIndexes(t, 'stock_transfers'),
    fromLocationIdIndex: index('stock_transfers_from_location_id_index').on(
      t.fromLocationId,
    ),
    toLocationIdIndex: index('stock_transfers_to_location_id_index').on(
      t.toLocationId,
    ),
    requestedByIndex: index('stock_transfers_requested_by_index').on(
      t.requestedBy,
    ),
    approvedByIndex: index('stock_transfers_approved_by_index').on(
      t.approvedBy,
    ),
    shippedByIndex: index('stock_transfers_shipped_by_index').on(t.shippedBy),
    receivedByIndex: index('stock_transfers_received_by_index').on(
      t.receivedBy,
    ),
    statusIndex: index('stock_transfers_status_index').on(t.status),
    transferDateIndex: index('stock_transfers_transfer_date_index').on(
      t.transferDate,
    ),
    shippedDateIndex: index('stock_transfers_shipped_date_index').on(
      t.shippedDate,
    ),
    receivedDateIndex: index('stock_transfers_received_date_index').on(
      t.receivedDate,
    ),

    // Unique constraint
    uniqueTransferNumber: uniqueIndex('stock_transfers_transfer_number_unique')
      .on(t.businessId, t.transferNumber)
      .where(sql`${t.isDeleted} = false`),

    // Composite indexes for common query patterns
    businessStatusIndex: index('stock_transfers_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessDateIndex: index('stock_transfers_business_date_index')
      .on(t.businessId, t.transferDate)
      .where(sql`${t.isDeleted} = false`),
    businessFromLocationIndex: index(
      'stock_transfers_business_from_location_index',
    )
      .on(t.businessId, t.fromLocationId)
      .where(sql`${t.isDeleted} = false`),
    businessToLocationIndex: index('stock_transfers_business_to_location_index')
      .on(t.businessId, t.toLocationId)
      .where(sql`${t.isDeleted} = false`),

    // Audit indexes
    createdAtIndex: index('stock_transfers_created_at_index').on(t.createdAt),
    updatedAtIndex: index('stock_transfers_updated_at_index').on(t.updatedAt),
  }),
);

// Stock Adjustments table
export const stockAdjustments = pgTable(
  'stock_adjustments',
  {
    ...createBaseEntityBusinessFields(business),
    adjustmentNumber: text('adjustment_number').notNull(),
    locationId: uuid('location_id')
      .notNull()
      .references(() => locations.id),
    adjustmentDate: date('adjustment_date').notNull(),
    adjustmentType: stockAdjustmentTypeEnum('adjustment_type').notNull(),
    status: stockAdjustmentStatusEnum('status')
      .default(StockAdjustmentStatus.DRAFT)
      .notNull(),
    reason: text('reason'),
    notes: text('notes'),
    approvedBy: uuid('approved_by'),
    approvedDate: timestamp('approved_date'),
    postedDate: timestamp('posted_date'),
  },
  (t) => ({
    // Indexes for performance
    ...createBaseEntityBusinessIndexes(t, 'stock_adjustments'),
    locationIdIndex: index('stock_adjustments_location_id_index').on(
      t.locationId,
    ),
    approvedByIndex: index('stock_adjustments_approved_by_index').on(
      t.approvedBy,
    ),
    adjustmentTypeIndex: index('stock_adjustments_adjustment_type_index').on(
      t.adjustmentType,
    ),
    statusIndex: index('stock_adjustments_status_index').on(t.status),
    adjustmentDateIndex: index('stock_adjustments_adjustment_date_index').on(
      t.adjustmentDate,
    ),
    approvedDateIndex: index('stock_adjustments_approved_date_index').on(
      t.approvedDate,
    ),
    postedDateIndex: index('stock_adjustments_posted_date_index').on(
      t.postedDate,
    ),

    // Unique constraint
    uniqueAdjustmentNumber: uniqueIndex(
      'stock_adjustments_adjustment_number_unique',
    )
      .on(t.businessId, t.adjustmentNumber)
      .where(sql`${t.isDeleted} = false`),

    // Composite indexes for common query patterns
    businessStatusIndex: index('stock_adjustments_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessTypeIndex: index('stock_adjustments_business_type_index')
      .on(t.businessId, t.adjustmentType)
      .where(sql`${t.isDeleted} = false`),
    businessDateIndex: index('stock_adjustments_business_date_index')
      .on(t.businessId, t.adjustmentDate)
      .where(sql`${t.isDeleted} = false`),
    businessLocationIndex: index('stock_adjustments_business_location_index')
      .on(t.businessId, t.locationId)
      .where(sql`${t.isDeleted} = false`),

    // Audit indexes
    createdAtIndex: index('stock_adjustments_created_at_index').on(t.createdAt),
    updatedAtIndex: index('stock_adjustments_updated_at_index').on(t.updatedAt),
  }),
);
