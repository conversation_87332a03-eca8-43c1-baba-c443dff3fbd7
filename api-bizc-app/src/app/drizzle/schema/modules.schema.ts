import { pgEnum, pgTable, index, uniqueIndex } from 'drizzle-orm/pg-core';
import { ModuleType } from '../../shared/types';
import { business } from './business.schema';
import { createBaseEntityBusinessFields } from './common-fields.schema';

// Module types enum - combining previous module and submodule types
export const moduleTypeEnum = pgEnum('module_type', [
  // Main modules
  ModuleType.HRM,
  ModuleType.MARKETING,
  ModuleType.ASSETS,
  ModuleType.PAYMENT_ACCOUNT,
  ModuleType.INVENTORY,
  ModuleType.FINANCE,
  ModuleType.CRM,
  ModuleType.PROJECT,
  ModuleType.SUPPORT,

  // Former submodules now as modules
  ModuleType.ATTENDANCE,
  ModuleType.PAYROLL,
  ModuleType.RECRUITMENT,
  ModuleType.PERFORMANCE,
  ModuleType.LEAVE,
  ModuleType.TRAINING,
  ModuleType.CAMPAIGN,
  ModuleType.SOCIAL_MEDIA,
  ModuleType.EMAIL_MARKETING,
  ModuleType.SEO,
  ModuleType.FIXED_ASSETS,
  ModuleType.DIGITAL_ASSETS,
  ModuleType.MAINTENANCE,
  ModuleType.BANK_ACCOUNTS,
  ModuleType.PAYMENT_GATEWAYS,
  ModuleType.TRANSACTIONS,

  // Additional modules
  ModuleType.RETAIL_POS,
  ModuleType.RESTAURANT_POS,
  ModuleType.SALES,
  ModuleType.WORKORDERS,
  ModuleType.VEHICLES,
  ModuleType.VEHICLE_RESERVATIONS,
]);

// Junction table for business modules (many-to-many relationship)
export const businessModules = pgTable(
  'business_modules',
  {
    ...createBaseEntityBusinessFields(business),
    moduleType: moduleTypeEnum('module_type').notNull(),
  },
  (t) => ({
    businessIdIndex: index('business_modules_business_id_index').on(
      t.businessId,
    ),
    moduleTypeIndex: index('business_modules_module_type_index').on(
      t.moduleType,
    ),
    uniqueBusinessModule: uniqueIndex('business_modules_unique').on(
      t.businessId,
      t.moduleType,
    ),
  }),
);
