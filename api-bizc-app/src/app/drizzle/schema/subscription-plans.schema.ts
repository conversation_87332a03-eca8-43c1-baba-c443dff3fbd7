import {
  index,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uuid,
  integer,
  date,
  decimal,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import { users } from './users.schema';
import { accounts } from './accounts.schema';
import {
  products,
  productVariants,
  standardUnitOfMeasureEnum,
} from './products.schema';
import { services } from './services.schema';
import { units } from './units.schema';
import { customers } from './customers.schema';
import { customerGroups } from './customer-groups.schema';
import {
  pricingFields,
  auditFields,
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { DayOfWeek, StandardUnitOfMeasure } from '../../shared/types';

/**
 * Subscription Plans Schema
 *
 * This schema defines subscription plans with recurring billing functionality.
 * It includes plan details, status management, and comprehensive recurrence settings
 * for flexible subscription billing patterns.
 */

// Subscription Plan Status Enum
export enum SubscriptionPlanStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export const subscriptionPlanStatusEnum = pgEnum('subscription_plan_status', [
  SubscriptionPlanStatus.ACTIVE,
  SubscriptionPlanStatus.INACTIVE,
]);

// Recurrence Pattern Enum
export enum RecurrencePattern {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  YEARLY = 'yearly',
}

export const recurrencePatternEnum = pgEnum('recurrence_pattern', [
  RecurrencePattern.DAILY,
  RecurrencePattern.WEEKLY,
  RecurrencePattern.MONTHLY,
  RecurrencePattern.YEARLY,
]);

// Recurrence End Type Enum
export enum RecurrenceEndType {
  NEVER = 'never',
  ON_DATE = 'on_date',
  AFTER_OCCURRENCES = 'after_occurrences',
}

export const recurrenceEndTypeEnum = pgEnum('recurrence_end_type', [
  RecurrenceEndType.NEVER,
  RecurrenceEndType.ON_DATE,
  RecurrenceEndType.AFTER_OCCURRENCES,
]);

// Day of Week Enum for recurring activities
export const recurringDayOfWeekEnum = pgEnum('recurring_day_of_week', [
  DayOfWeek.MONDAY,
  DayOfWeek.TUESDAY,
  DayOfWeek.WEDNESDAY,
  DayOfWeek.THURSDAY,
  DayOfWeek.FRIDAY,
  DayOfWeek.SATURDAY,
  DayOfWeek.SUNDAY,
]);

// Main subscription plans table
export const subscriptionPlans = pgTable(
  'subscription_plans',
  {
    ...createBaseEntityBusinessFields(business),

    // Plan basic information
    name: text('name').notNull(),
    description: text('description'),
    status: subscriptionPlanStatusEnum('status')
      .default(SubscriptionPlanStatus.ACTIVE)
      .notNull(),

    // Financial fields
    ...pricingFields,
    standardCost: decimal('standard_cost', {
      precision: 15,
      scale: 2,
    }).notNull(),
    incomeAccountId: uuid('income_account_id')
      .notNull()
      .references(() => accounts.id),
    expenseAccountId: uuid('expense_account_id').references(() => accounts.id),

    // Payment terms
    netDays: integer('net_days').default(30).notNull(), // Payment due in X days

    // Core recurrence settings
    recurrencePattern: recurrencePatternEnum('recurrence_pattern').notNull(),
    recurrenceInterval: integer('recurrence_interval').default(1).notNull(), // Every X days/weeks/months/years

    // End conditions
    recurrenceEndType: recurrenceEndTypeEnum('recurrence_end_type')
      .default(RecurrenceEndType.NEVER)
      .notNull(),
    recurrenceEndDate: date('recurrence_end_date'),
    recurrenceEndAfterOccurrences: integer('recurrence_end_after_occurrences'),

    // Pattern-specific settings
    recurrenceDaysOfWeek: recurringDayOfWeekEnum(
      'recurrence_days_of_week',
    ).array(), // For weekly recurrence
    recurrenceDayOfMonth: integer('recurrence_day_of_month'), // For monthly/yearly recurrence (1-31)
    recurrenceMonthOfYear: integer('recurrence_month_of_year'), // For yearly recurrence (1-12: Jan-Dec)

    // Tracking fields
    nextOccurrenceDate: date('next_occurrence_date'), // When the next occurrence should happen
    lastOccurrenceDate: date('last_occurrence_date'), // When the last occurrence happened
    occurrenceCount: integer('occurrence_count').default(0).notNull(), // How many times it has occurred

    // Additional deletion fields
    deletedBy: uuid('deleted_by').references(() => users.id),
    deletedAt: timestamp('deleted_at'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'subscription_plans'),

    // Status and pattern indexes
    statusIndex: index('subscription_plans_status_index').on(t.status),
    recurrencePatternIndex: index(
      'subscription_plans_recurrence_pattern_index',
    ).on(t.recurrencePattern),

    // Financial indexes
    basePriceIndex: index('subscription_plans_base_price_index').on(
      t.basePrice,
    ),
    taxTypeIndex: index('subscription_plans_tax_type_index').on(t.taxType),
    taxIdIndex: index('subscription_plans_tax_id_index').on(t.taxId),
    incomeAccountIdIndex: index(
      'subscription_plans_income_account_id_index',
    ).on(t.incomeAccountId),
    expenseAccountIdIndex: index(
      'subscription_plans_expense_account_id_index',
    ).on(t.expenseAccountId),
    netDaysIndex: index('subscription_plans_net_days_index').on(t.netDays),

    // Date-based indexes for scheduling
    nextOccurrenceDateIndex: index(
      'subscription_plans_next_occurrence_date_index',
    ).on(t.nextOccurrenceDate),
    lastOccurrenceDateIndex: index(
      'subscription_plans_last_occurrence_date_index',
    ).on(t.lastOccurrenceDate),
    recurrenceEndDateIndex: index(
      'subscription_plans_recurrence_end_date_index',
    ).on(t.recurrenceEndDate),

    // Month/day indexes for yearly/monthly patterns
    recurrenceMonthOfYearIndex: index(
      'subscription_plans_recurrence_month_of_year_index',
    ).on(t.recurrenceMonthOfYear),
    recurrenceDayOfMonthIndex: index(
      'subscription_plans_recurrence_day_of_month_index',
    ).on(t.recurrenceDayOfMonth),

    // Composite indexes for common queries with soft deletion support
    businessStatusIndex: index('subscription_plans_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessNextOccurrenceIndex: index(
      'subscription_plans_business_next_occurrence_index',
    )
      .on(t.businessId, t.nextOccurrenceDate)
      .where(sql`${t.isDeleted} = false`),

    // Scheduling query optimization
    activeNextOccurrenceIndex: index(
      'subscription_plans_active_next_occurrence_index',
    )
      .on(t.status, t.nextOccurrenceDate)
      .where(sql`${t.isDeleted} = false`),
    statusNextOccurrenceIndex: index(
      'subscription_plans_status_next_occurrence_index',
    )
      .on(t.status, t.nextOccurrenceDate)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Junction table for subscription plan products (many-to-many relationship)
export const subscriptionPlanProducts = pgTable(
  'subscription_plan_products',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    subscriptionPlanId: uuid('subscription_plan_id')
      .notNull()
      .references(() => subscriptionPlans.id, { onDelete: 'cascade' }),
    productId: uuid('product_id')
      .notNull()
      .references(() => products.id, { onDelete: 'cascade' }),
    variantId: uuid('variant_id').references(() => productVariants.id, {
      onDelete: 'cascade',
    }),

    // Quantity and unit information
    quantity: decimal('quantity', { precision: 10, scale: 3 }).notNull(),
    standardUnitOfMeasure: standardUnitOfMeasureEnum('standard_unit_of_measure')
      .default(StandardUnitOfMeasure.PIECES)
      .notNull(),
    customUnitId: uuid('custom_unit_id').references(() => units.id),

    // Standard audit fields
    ...auditFields,
  },
  (t) => ({
    subscriptionPlanIdIndex: index(
      'subscription_plan_products_subscription_plan_id_index',
    ).on(t.subscriptionPlanId),
    productIdIndex: index('subscription_plan_products_product_id_index').on(
      t.productId,
    ),
    variantIdIndex: index('subscription_plan_products_variant_id_index').on(
      t.variantId,
    ),
    customUnitIdIndex: index(
      'subscription_plan_products_custom_unit_id_index',
    ).on(t.customUnitId),
    uniqueSubscriptionPlanProduct: index(
      'subscription_plan_products_unique',
    ).on(t.subscriptionPlanId, t.productId, t.variantId),
  }),
);

// Junction table for subscription plan services (many-to-many relationship)
export const subscriptionPlanServices = pgTable(
  'subscription_plan_services',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    subscriptionPlanId: uuid('subscription_plan_id')
      .notNull()
      .references(() => subscriptionPlans.id, { onDelete: 'cascade' }),
    serviceId: uuid('service_id')
      .notNull()
      .references(() => services.id, { onDelete: 'cascade' }),

    // Standard audit fields
    ...auditFields,
  },
  (t) => ({
    subscriptionPlanIdIndex: index(
      'subscription_plan_services_subscription_plan_id_index',
    ).on(t.subscriptionPlanId),
    serviceIdIndex: index('subscription_plan_services_service_id_index').on(
      t.serviceId,
    ),
    uniqueSubscriptionPlanService: index(
      'subscription_plan_services_unique',
    ).on(t.subscriptionPlanId, t.serviceId),
  }),
);

// Junction table for subscription plan customers (many-to-many relationship)
export const subscriptionPlanCustomers = pgTable(
  'subscription_plan_customers',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    subscriptionPlanId: uuid('subscription_plan_id')
      .notNull()
      .references(() => subscriptionPlans.id, { onDelete: 'cascade' }),
    customerId: uuid('customer_id')
      .notNull()
      .references(() => customers.id, { onDelete: 'cascade' }),

    // Notes for this customer assignment
    notes: text('notes'),

    // Standard audit fields
    ...auditFields,
  },
  (t) => ({
    subscriptionPlanIdIndex: index(
      'subscription_plan_customers_subscription_plan_id_index',
    ).on(t.subscriptionPlanId),
    customerIdIndex: index('subscription_plan_customers_customer_id_index').on(
      t.customerId,
    ),
    uniqueSubscriptionPlanCustomer: index(
      'subscription_plan_customers_unique',
    ).on(t.subscriptionPlanId, t.customerId),
  }),
);

// Junction table for subscription plan customer groups (many-to-many relationship)
export const subscriptionPlanCustomerGroups = pgTable(
  'subscription_plan_customer_groups',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    subscriptionPlanId: uuid('subscription_plan_id')
      .notNull()
      .references(() => subscriptionPlans.id, { onDelete: 'cascade' }),
    customerGroupId: uuid('customer_group_id')
      .notNull()
      .references(() => customerGroups.id, { onDelete: 'cascade' }),

    // Notes for this customer group assignment
    notes: text('notes'),

    // Standard audit fields
    ...auditFields,
  },
  (t) => ({
    subscriptionPlanIdIndex: index(
      'subscription_plan_customer_groups_subscription_plan_id_index',
    ).on(t.subscriptionPlanId),
    customerGroupIdIndex: index(
      'subscription_plan_customer_groups_customer_group_id_index',
    ).on(t.customerGroupId),
    uniqueSubscriptionPlanCustomerGroup: index(
      'subscription_plan_customer_groups_unique',
    ).on(t.subscriptionPlanId, t.customerGroupId),
  }),
);
