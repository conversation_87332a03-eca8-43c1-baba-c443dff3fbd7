import {
  pgTable,
  text,
  uuid,
  decimal,
  index,
  uniqueIndex,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';

import { business } from './business.schema';
import { employeeSalaries } from './employee-salary.schema';
import { allowanceTypes } from './allowance-types.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

/**
 * Employee Salary Allowances Table
 * Links employee salaries with their allowances and stores specific allowance amounts
 */
export const employeeSalaryAllowances = pgTable(
  'employee_salary_allowances',
  {
    ...createBaseEntityBusinessFields(business),
    employeeSalaryId: uuid('employee_salary_id')
      .notNull()
      .references(() => employeeSalaries.id),
    allowanceTypeId: uuid('allowance_type_id')
      .notNull()
      .references(() => allowanceTypes.id),
    amount: decimal('amount', { precision: 12, scale: 2 }),
    notes: text('notes'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'employee_salary_allowances'),
    employeeSalaryIdIndex: index(
      'employee_salary_allowances_employee_salary_id_index',
    ).on(t.employeeSalaryId),
    allowanceTypeIdIndex: index(
      'employee_salary_allowances_allowance_type_id_index',
    ).on(t.allowanceTypeId),
    uniqueEmployeeSalaryAllowanceActive: uniqueIndex(
      'employee_salary_allowances_employee_salary_allowance_active_unique',
    )
      .on(t.employeeSalaryId, t.allowanceTypeId)
      .where(sql`${t.isDeleted} = false`),
  }),
);
