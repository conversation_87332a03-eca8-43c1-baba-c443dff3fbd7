import {
  date,
  decimal,
  index,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uuid,
} from 'drizzle-orm/pg-core';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { business } from './business.schema';
import { staffMembers } from './staff.schema';
import { leaveTypes } from './leave-types.schema';
import { LeaveRequestStatus } from '../../shared/types';

export const leaveRequestStatusEnum = pgEnum('leave_request_status', [
  LeaveRequestStatus.DRAFT,
  LeaveRequestStatus.SUBMITTED,
  LeaveRequestStatus.PENDING,
  LeaveRequestStatus.APPROVED,
  LeaveRequestStatus.REJECTED,
  LeaveRequestStatus.CANCELLED,
  LeaveRequestStatus.REVOKED,
  LeaveRequestStatus.EXPIRED,
  LeaveRequestStatus.PARTIALLY_APPROVED,
  LeaveRequestStatus.ON_HOLD,
]);

export const leaveRequests = pgTable(
  'leave_requests',
  {
    ...createBaseEntityBusinessFields(business),
    employeeId: uuid('employee_id')
      .notNull()
      .references(() => staffMembers.id),
    leaveTypeId: uuid('leave_type_id')
      .notNull()
      .references(() => leaveTypes.id),
    startDate: date('start_date').notNull(),
    endDate: date('end_date').notNull(),
    daysRequested: decimal('days_requested', {
      precision: 5,
      scale: 2,
    }).notNull(),
    reason: text('reason'),
    status: leaveRequestStatusEnum('status')
      .default(LeaveRequestStatus.DRAFT)
      .notNull(),
    approvedBy: uuid('approved_by').references(() => staffMembers.id),
    approvalDate: timestamp('approval_date'),
    comments: text('comments'),
  },
  (t) => ({
    idIndex: index('leave_requests_id_index').on(t.id),
    ...createBaseEntityBusinessIndexes(t, 'leave_requests'),
    employeeIdIndex: index('leave_requests_employee_id_index').on(t.employeeId),
    leaveTypeIdIndex: index('leave_requests_leave_type_id_index').on(
      t.leaveTypeId,
    ),
    statusIndex: index('leave_requests_status_index').on(t.status),
    startDateIndex: index('leave_requests_start_date_index').on(t.startDate),
    endDateIndex: index('leave_requests_end_date_index').on(t.endDate),
    approvedByIndex: index('leave_requests_approved_by_index').on(t.approvedBy),
  }),
);
