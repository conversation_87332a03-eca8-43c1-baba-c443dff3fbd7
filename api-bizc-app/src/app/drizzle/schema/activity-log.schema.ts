import {
  pgTable,
  pgEnum,
  uuid,
  text,
  timestamp,
  index,
  jsonb,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { users } from './users.schema';
import { ActivityType, EntityType } from '../../shared/types';

// Activity Type enum
export const activityTypeEnum = pgEnum('activity_type', [
  ActivityType.CREATE,
  ActivityType.UPDATE,
  ActivityType.DELETE,
  ActivityType.RESTORE,
  ActivityType.STATUS_CHANGE,
  ActivityType.BULK_UPDATE,
  ActivityType.BULK_DELETE,
]);

// Entity Type enum
export const entityTypeEnum = pgEnum('entity_type', [
  EntityType.DESIGNATION,
  EntityType.USER,
  EntityType.BUSINESS,
  EntityType.ASSET,
  EntityType.CATEGORY,
  EntityType.PRODUCT,
  EntityType.SERVICE,
  EntityType.CUSTOMER,
  EntityType.ORDER,
  EntityType.INVOICE,
  EntityType.PAYMENT,
  EntityType.STAFF,
  EntityType.DEPARTMENT,
  EntityType.LOCATION,
  EntityType.SUPPLIER,
  EntityType.VEHICLE,
  EntityType.RESERVATION,
  EntityType.CAMPAIGN,
  EntityType.TEMPLATE,
  EntityType.WORK_ORDER,
  EntityType.PROJECT,
  EntityType.TASK,
  EntityType.EXPENSE,
  EntityType.LEAD,
  EntityType.ESTIMATE,
  EntityType.MEETING,
  // Add more entity types as your system grows
]);

// Activity Log table for detailed audit trail
export const activityLogs = pgTable(
  'activity_logs',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    entityType: entityTypeEnum('entity_type').notNull(),
    entityId: uuid('entity_id').notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    activityType: activityTypeEnum('activity_type').notNull(),
    userId: uuid('user_id')
      .notNull()
      .references(() => users.id),
    changes: jsonb('changes'), // Store before/after values
    metadata: jsonb('metadata'), // Additional context
    ipAddress: text('ip_address'),
    userAgent: text('user_agent'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
  },
  (t) => ({
    idIndex: index('activity_logs_id_index').on(t.id),
    entityIndex: index('activity_logs_entity_index').on(
      t.entityType,
      t.entityId,
    ),
    businessIdIndex: index('activity_logs_business_id_index').on(t.businessId),
    userIdIndex: index('activity_logs_user_id_index').on(t.userId),
    createdAtIndex: index('activity_logs_created_at_index').on(t.createdAt),
    activityTypeIndex: index('activity_logs_activity_type_index').on(
      t.activityType,
    ),
  }),
);

// Activity Logs Relations
export const activityLogsRelations = relations(activityLogs, ({ one }) => ({
  business: one(business, {
    fields: [activityLogs.businessId],
    references: [business.id],
  }),
  user: one(users, {
    fields: [activityLogs.userId],
    references: [users.id],
  }),
}));
