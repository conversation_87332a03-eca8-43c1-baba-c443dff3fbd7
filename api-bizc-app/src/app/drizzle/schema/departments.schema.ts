import {
  index,
  pgEnum,
  pgTable,
  text,
  uuid,
  uniqueIndex,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import {
  createBaseEntityBusinessFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { DepartmentStatus } from '@app/shared/types/department.enum';

export const departmentStatusEnum = pgEnum('department_status', [
  DepartmentStatus.ACTIVE,
  DepartmentStatus.INACTIVE,
]);

export const departments = pgTable(
  'departments',
  {
    ...createBaseEntityBusinessFields(business),
    name: text('name').notNull(),
    parentId: uuid('parent_id').references(() => departments.id),
    description: text('description'),
    status: departmentStatusEnum('status')
      .default(DepartmentStatus.ACTIVE)
      .notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'departments'),
    // Entity-specific indexes
    nameIndex: index('departments_name_index').on(t.name),
    parentIdIndex: index('departments_parent_id_index').on(t.parentId),
    statusIndex: index('departments_status_index').on(t.status),

    // Composite indexes for performance
    businessStatusIndex: index('departments_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessParentIndex: index('departments_business_parent_index')
      .on(t.businessId, t.parentId)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessName: uniqueIndex('departments_business_name_unique')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
  }),
);
