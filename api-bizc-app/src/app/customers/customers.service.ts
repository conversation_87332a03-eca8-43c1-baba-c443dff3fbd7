import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateCustomerDto } from './dto/create-customer.dto';
import { UpdateCustomerDto } from './dto/update-customer.dto';
import { CustomerDto } from './dto/customer.dto';
import { CustomerSlimDto } from './dto/customer-slim.dto';
import { CustomerAutocompleteDto } from './dto/customer-autocomplete.dto';
import { CustomerListDto } from './dto/customer-list.dto';
import {
  customers,
  customerLocations,
  customerRewards,
} from '../drizzle/schema/customers.schema';
import { addresses } from '../drizzle/schema/address.schema';
import { customerGroups } from '../drizzle/schema/customer-groups.schema';
import { users } from '../drizzle/schema/users.schema';
import { locations } from '../drizzle/schema/locations.schema';
import { AddressService } from '../address/address.service';
import { CustomerRewardsService } from '../customer-rewards/customer-rewards.service';
import {
  eq,
  and,
  isNull,
  ilike,
  sql,
  gte,
  lte,
  or,
  desc,
  asc,
  count,
  inArray,
} from 'drizzle-orm';
import { CustomerStatus } from '../shared/types/customer.enum';
import { AddressType } from '../shared/types';

@Injectable()
export class CustomersService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private addressService: AddressService,
    private customerRewardsService: CustomerRewardsService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createCustomerDto: CreateCustomerDto,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a customer with the same display name already exists for this business
      const existingCustomer = await this.db
        .select()
        .from(customers)
        .where(
          and(
            eq(customers.businessId, businessId),
            ilike(
              customers.customerDisplayName,
              createCustomerDto.customerDisplayName,
            ),
            isNull(customers.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (existingCustomer) {
        throw new ConflictException(
          `Customer with display name "${createCustomerDto.customerDisplayName}" already exists`,
        );
      }

      // Check if email is provided and already exists for this business
      if (createCustomerDto.email) {
        const existingEmailCustomer = await this.db
          .select()
          .from(customers)
          .where(
            and(
              eq(customers.businessId, businessId),
              ilike(customers.email, createCustomerDto.email),
              isNull(customers.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (existingEmailCustomer) {
          throw new ConflictException(
            `Customer with email "${createCustomerDto.email}" already exists`,
          );
        }
      }

      // Validate customer group if provided
      if (createCustomerDto.customerGroupId) {
        const customerGroup = await this.db
          .select()
          .from(customerGroups)
          .where(
            and(
              eq(customerGroups.id, createCustomerDto.customerGroupId),
              eq(customerGroups.businessId, businessId),
              isNull(customerGroups.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (!customerGroup) {
          throw new BadRequestException('Invalid customer group ID');
        }
      }

      // Create addresses if provided
      let billingAddressId: string | undefined;
      let shippingAddressId: string | undefined;

      if (createCustomerDto.billingAddress) {
        const billingAddressResult = await this.addressService.create(userId, {
          ...createCustomerDto.billingAddress,
          addressType: AddressType.BILLING,
          businessId,
        });
        billingAddressId = billingAddressResult.id;
      }

      if (createCustomerDto.shippingAddress) {
        const shippingAddressResult = await this.addressService.create(userId, {
          ...createCustomerDto.shippingAddress,
          addressType: AddressType.SHIPPING,
          businessId,
        });
        shippingAddressId = shippingAddressResult.id;
      }

      // Insert new customer
      const [customer] = await this.db
        .insert(customers)
        .values({
          businessId,
          title: createCustomerDto.title,
          firstName: createCustomerDto.firstName,
          middleName: createCustomerDto.middleName,
          lastName: createCustomerDto.lastName,
          suffix: createCustomerDto.suffix,
          customerDisplayName: createCustomerDto.customerDisplayName,
          companyName: createCustomerDto.companyName,
          email: createCustomerDto.email,
          phoneNumber: createCustomerDto.phoneNumber,
          mobileNumber: createCustomerDto.mobileNumber,
          fax: createCustomerDto.fax,
          other: createCustomerDto.other,
          website: createCustomerDto.website,
          isSubCustomer: createCustomerDto.isSubCustomer ?? false,
          isAllocatedToAllLocations:
            createCustomerDto.isAllocatedToAllLocations ?? false,
          billingAddressId: billingAddressId,
          shippingAddressId: shippingAddressId,
          customerGroupId: createCustomerDto.customerGroupId,
          notes: createCustomerDto.notes,
          attachments: createCustomerDto.attachments ?? [],
          salesTaxRegistration: createCustomerDto.salesTaxRegistration,
          openingBalance: createCustomerDto.openingBalance,
          openingBalanceAsOf: createCustomerDto.openingBalanceAsOf,
          status: createCustomerDto.status ?? CustomerStatus.ACTIVE,
          createdBy: userId,
        })
        .returning();

      // Handle location associations
      await this.manageCustomerLocations(
        customer.id,
        businessId,
        createCustomerDto.isAllocatedToAllLocations ?? false,
        createCustomerDto.locationIds,
        userId,
      );

      return { id: customer.id };
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to create customer');
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createCustomerDto: CreateCustomerDto,
  ): Promise<{ id: string }> {
    return this.create(userId, businessId, createCustomerDto);
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createCustomerDtos: CreateCustomerDto[],
  ): Promise<{ ids: string[]; count: number }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const createdIds: string[] = [];

    for (const createCustomerDto of createCustomerDtos) {
      try {
        const result = await this.create(userId, businessId, createCustomerDto);
        createdIds.push(result.id);
      } catch (error) {
        // Continue with other customers if one fails
        console.error(`Failed to create customer: ${error.message}`);
      }
    }

    return { ids: createdIds, count: createdIds.length };
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createCustomerDtos: CreateCustomerDto[],
  ): Promise<{ ids: string[]; count: number }> {
    return this.bulkCreate(userId, businessId, createCustomerDtos);
  }

  async findAllOptimized(
    _userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    customerDisplayName?: string,
    email?: string,
    companyName?: string,
    phoneNumber?: string,
    mobileNumber?: string,
    status?: string,
    customerGroupId?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: CustomerListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions (exclude blacklisted customers by default)
    const whereConditions = [
      isNull(customers.deletedAt),
      eq(customers.businessId, businessId),
      eq(customers.isBlacklisted, false),
    ];

    // Add date range filters
    if (from) {
      whereConditions.push(gte(customers.createdAt, new Date(from)));
    }
    if (to) {
      whereConditions.push(lte(customers.createdAt, new Date(to)));
    }

    // Add search filters
    if (customerDisplayName) {
      whereConditions.push(
        ilike(customers.customerDisplayName, `%${customerDisplayName}%`),
      );
    }
    if (email) {
      whereConditions.push(ilike(customers.email, `%${email}%`));
    }
    if (companyName) {
      whereConditions.push(ilike(customers.companyName, `%${companyName}%`));
    }
    if (phoneNumber) {
      whereConditions.push(ilike(customers.phoneNumber, `%${phoneNumber}%`));
    }
    if (mobileNumber) {
      whereConditions.push(ilike(customers.mobileNumber, `%${mobileNumber}%`));
    }
    if (status) {
      whereConditions.push(eq(customers.status, status as CustomerStatus));
    }
    if (customerGroupId) {
      whereConditions.push(eq(customers.customerGroupId, customerGroupId));
    }

    // Handle advanced filters (matching categories service structure)
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        const filterConditions = [];

        for (const filter of parsedFilters) {
          if (filter.id && filter.value !== undefined && filter.value !== '') {
            const operator = filter.operator || 'iLike';
            const value = filter.value;

            switch (filter.id) {
              case 'customerDisplayName':
                if (operator === 'iLike') {
                  filterConditions.push(
                    ilike(customers.customerDisplayName, `%${value}%`),
                  );
                } else if (operator === 'notILike') {
                  filterConditions.push(
                    sql`NOT ${ilike(customers.customerDisplayName, `%${value}%`)}`,
                  );
                } else if (operator === 'eq') {
                  filterConditions.push(
                    eq(customers.customerDisplayName, value),
                  );
                } else if (operator === 'ne') {
                  filterConditions.push(
                    sql`${customers.customerDisplayName} != ${value}`,
                  );
                } else if (operator === 'isEmpty') {
                  filterConditions.push(
                    sql`${customers.customerDisplayName} IS NULL OR ${customers.customerDisplayName} = ''`,
                  );
                } else if (operator === 'isNotEmpty') {
                  filterConditions.push(
                    sql`${customers.customerDisplayName} IS NOT NULL AND ${customers.customerDisplayName} != ''`,
                  );
                }
                break;
              case 'email':
                if (operator === 'iLike') {
                  filterConditions.push(ilike(customers.email, `%${value}%`));
                } else if (operator === 'notILike') {
                  filterConditions.push(
                    sql`NOT ${ilike(customers.email, `%${value}%`)}`,
                  );
                } else if (operator === 'eq') {
                  filterConditions.push(eq(customers.email, value));
                } else if (operator === 'ne') {
                  filterConditions.push(sql`${customers.email} != ${value}`);
                } else if (operator === 'isEmpty') {
                  filterConditions.push(
                    sql`${customers.email} IS NULL OR ${customers.email} = ''`,
                  );
                } else if (operator === 'isNotEmpty') {
                  filterConditions.push(
                    sql`${customers.email} IS NOT NULL AND ${customers.email} != ''`,
                  );
                }
                break;
              case 'companyName':
                if (operator === 'iLike') {
                  filterConditions.push(
                    ilike(customers.companyName, `%${value}%`),
                  );
                } else if (operator === 'notILike') {
                  filterConditions.push(
                    sql`NOT ${ilike(customers.companyName, `%${value}%`)}`,
                  );
                } else if (operator === 'eq') {
                  filterConditions.push(eq(customers.companyName, value));
                } else if (operator === 'ne') {
                  filterConditions.push(
                    sql`${customers.companyName} != ${value}`,
                  );
                } else if (operator === 'isEmpty') {
                  filterConditions.push(
                    sql`${customers.companyName} IS NULL OR ${customers.companyName} = ''`,
                  );
                } else if (operator === 'isNotEmpty') {
                  filterConditions.push(
                    sql`${customers.companyName} IS NOT NULL AND ${customers.companyName} != ''`,
                  );
                }
                break;
              case 'phoneNumber':
                if (operator === 'iLike') {
                  filterConditions.push(
                    ilike(customers.phoneNumber, `%${value}%`),
                  );
                } else if (operator === 'notILike') {
                  filterConditions.push(
                    sql`NOT ${ilike(customers.phoneNumber, `%${value}%`)}`,
                  );
                } else if (operator === 'eq') {
                  filterConditions.push(eq(customers.phoneNumber, value));
                } else if (operator === 'ne') {
                  filterConditions.push(
                    sql`${customers.phoneNumber} != ${value}`,
                  );
                } else if (operator === 'isEmpty') {
                  filterConditions.push(
                    sql`${customers.phoneNumber} IS NULL OR ${customers.phoneNumber} = ''`,
                  );
                } else if (operator === 'isNotEmpty') {
                  filterConditions.push(
                    sql`${customers.phoneNumber} IS NOT NULL AND ${customers.phoneNumber} != ''`,
                  );
                }
                break;
              case 'mobileNumber':
                if (operator === 'iLike') {
                  filterConditions.push(
                    ilike(customers.mobileNumber, `%${value}%`),
                  );
                } else if (operator === 'notILike') {
                  filterConditions.push(
                    sql`NOT ${ilike(customers.mobileNumber, `%${value}%`)}`,
                  );
                } else if (operator === 'eq') {
                  filterConditions.push(eq(customers.mobileNumber, value));
                } else if (operator === 'ne') {
                  filterConditions.push(
                    sql`${customers.mobileNumber} != ${value}`,
                  );
                } else if (operator === 'isEmpty') {
                  filterConditions.push(
                    sql`${customers.mobileNumber} IS NULL OR ${customers.mobileNumber} = ''`,
                  );
                } else if (operator === 'isNotEmpty') {
                  filterConditions.push(
                    sql`${customers.mobileNumber} IS NOT NULL AND ${customers.mobileNumber} != ''`,
                  );
                }
                break;
              case 'status':
                if (operator === 'eq') {
                  filterConditions.push(eq(customers.status, value));
                } else if (operator === 'ne') {
                  filterConditions.push(sql`${customers.status} != ${value}`);
                }
                break;
              case 'isAllocatedToAllLocations': {
                const boolValue = value === 'true' || value === true;
                if (operator === 'eq') {
                  filterConditions.push(
                    eq(customers.isAllocatedToAllLocations, boolValue),
                  );
                } else if (operator === 'ne') {
                  filterConditions.push(
                    sql`${customers.isAllocatedToAllLocations} != ${boolValue}`,
                  );
                }
                break;
              }
            }
          }
        }

        if (filterConditions.length > 0) {
          if (joinOperator === 'or') {
            whereConditions.push(or(...filterConditions));
          } else {
            whereConditions.push(and(...filterConditions));
          }
        }
      } catch {
        // Invalid JSON filters, ignore
      }
    }

    // Build sort conditions (matching categories service structure)
    let orderBy = [desc(customers.createdAt), asc(customers.id)];

    if (sort) {
      try {
        const parsedSort = JSON.parse(sort);
        if (parsedSort.length > 0) {
          const sortField = parsedSort[0];
          const isDesc = sortField.desc === true;

          switch (sortField.id) {
            case 'customerDisplayName':
              orderBy = [
                isDesc
                  ? desc(customers.customerDisplayName)
                  : asc(customers.customerDisplayName),
                asc(customers.id), // Secondary sort for consistency
              ];
              break;
            case 'companyName':
              orderBy = [
                isDesc
                  ? desc(customers.companyName)
                  : asc(customers.companyName),
                asc(customers.id),
              ];
              break;
            case 'email':
              orderBy = [
                isDesc ? desc(customers.email) : asc(customers.email),
                asc(customers.id),
              ];
              break;
            case 'status':
              orderBy = [
                isDesc ? desc(customers.status) : asc(customers.status),
                asc(customers.id),
              ];
              break;
            case 'createdAt':
              orderBy = [
                isDesc ? desc(customers.createdAt) : asc(customers.createdAt),
                asc(customers.id),
              ];
              break;
            case 'updatedAt':
              orderBy = [
                isDesc ? desc(customers.updatedAt) : asc(customers.updatedAt),
                asc(customers.id),
              ];
              break;
            case 'openingBalance':
              orderBy = [
                isDesc
                  ? desc(customers.openingBalance)
                  : asc(customers.openingBalance),
                asc(customers.id),
              ];
              break;
          }
        }
      } catch {
        // Invalid JSON, use default sort
      }
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: count() })
      .from(customers)
      .where(and(...whereConditions));

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Get paginated data with joins
    const customersData = await this.db
      .select({
        id: customers.id,
        customerDisplayName: customers.customerDisplayName,
        companyName: customers.companyName,
        email: customers.email,
        phoneNumber: customers.phoneNumber,
        mobileNumber: customers.mobileNumber,
        status: customers.status,
        isSubCustomer: customers.isSubCustomer,
        isAllocatedToAllLocations: customers.isAllocatedToAllLocations,
        openingBalance: customers.openingBalance,
        salesTaxRegistration: customers.salesTaxRegistration,
        customerGroupName: customerGroups.name,
        billingAddressCity: sql<string>`${addresses.city}`,
        billingAddressState: sql<string>`${addresses.state}`,
        totalPoints: customerRewards.totalPoints,
        pointsEarnedYtd: customerRewards.pointsEarnedYtd,
        isBlacklisted: customers.isBlacklisted,
      })
      .from(customers)
      .leftJoin(
        customerGroups,
        eq(customers.customerGroupId, customerGroups.id),
      )
      .leftJoin(addresses, eq(customers.billingAddressId, addresses.id))
      .leftJoin(
        customerRewards,
        and(
          eq(customers.id, customerRewards.customerId),
          isNull(customerRewards.deletedAt),
        ),
      )
      .where(and(...whereConditions))
      .orderBy(...orderBy)
      .limit(limit)
      .offset(offset);

    const data: CustomerListDto[] = customersData.map((customer) => ({
      id: customer.id,
      customerDisplayName: customer.customerDisplayName,
      companyName: customer.companyName,
      email: customer.email,
      phoneNumber: customer.phoneNumber,
      mobileNumber: customer.mobileNumber,
      status: customer.status,
      isSubCustomer: customer.isSubCustomer,
      isAllocatedToAllLocations: customer.isAllocatedToAllLocations,
      customerGroupName: customer.customerGroupName,
      openingBalance: customer.openingBalance,
      salesTaxRegistration: customer.salesTaxRegistration,
      primaryAddress:
        customer.billingAddressCity && customer.billingAddressState
          ? `${customer.billingAddressCity}, ${customer.billingAddressState}`
          : undefined,
      totalPoints: customer.totalPoints ?? 0,
      pointsEarnedYtd: customer.pointsEarnedYtd ?? 0,
      locationNames: undefined, // Will be populated later if needed
      isBlacklisted: customer.isBlacklisted,
    }));

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllSlim(
    _userId: string,
    businessId: string | null,
  ): Promise<CustomerSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const customersData = await this.db
      .select({
        id: customers.id,
        customerDisplayName: customers.customerDisplayName,
        companyName: customers.companyName,
        status: customers.status,
      })
      .from(customers)
      .where(
        and(
          eq(customers.businessId, businessId),
          isNull(customers.deletedAt),
          eq(customers.status, CustomerStatus.ACTIVE),
          eq(customers.isBlacklisted, false),
        ),
      )
      .orderBy(asc(customers.customerDisplayName));

    return customersData.map((customer) => ({
      id: customer.id,
      customerDisplayName: customer.customerDisplayName,
      companyName: customer.companyName,
      status: customer.status,
    }));
  }

  async findAutocomplete(
    businessId: string,
    search?: string,
    limit: number = 10,
  ): Promise<CustomerAutocompleteDto[]> {
    const conditions = [
      eq(customers.businessId, businessId),
      eq(customers.status, CustomerStatus.ACTIVE),
      isNull(customers.deletedAt),
      eq(customers.isBlacklisted, false),
    ];

    if (search && search.trim()) {
      conditions.push(
        ilike(customers.customerDisplayName, `%${search.trim()}%`),
      );
    }

    const customersData = await this.db
      .select({
        id: customers.id,
        customerDisplayName: customers.customerDisplayName,
      })
      .from(customers)
      .where(and(...conditions))
      .orderBy(asc(customers.customerDisplayName))
      .limit(limit);

    return customersData.map((customer) => ({
      id: customer.id,
      name: customer.customerDisplayName,
    }));
  }

  async findOne(
    _userId: string,
    businessId: string | null,
    id: string,
  ): Promise<CustomerDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const customerData = await this.db
      .select({
        customer: customers,
        billingAddress: addresses,
        shippingAddress: sql<any>`NULL`,
        customerGroup: customerGroups,
        createdByUser: users,
        updatedByUser: sql<any>`NULL`,
        customerReward: customerRewards,
      })
      .from(customers)
      .leftJoin(addresses, eq(customers.billingAddressId, addresses.id))
      .leftJoin(
        customerGroups,
        eq(customers.customerGroupId, customerGroups.id),
      )
      .leftJoin(users, eq(customers.createdBy, users.id))
      .leftJoin(
        customerRewards,
        and(
          eq(customers.id, customerRewards.customerId),
          isNull(customerRewards.deletedAt),
        ),
      )
      .where(
        and(
          eq(customers.id, id),
          eq(customers.businessId, businessId),
          isNull(customers.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!customerData) {
      throw new NotFoundException('Customer not found');
    }

    // Get shipping address separately if different from billing
    let shippingAddress = null;
    if (
      customerData.customer.shippingAddressId &&
      customerData.customer.shippingAddressId !==
        customerData.customer.billingAddressId
    ) {
      shippingAddress = await this.db
        .select()
        .from(addresses)
        .where(eq(addresses.id, customerData.customer.shippingAddressId))
        .then((results) => results[0]);
    }

    // Get updated by user if exists
    let updatedByUser = null;
    if (customerData.customer.updatedBy) {
      updatedByUser = await this.db
        .select()
        .from(users)
        .where(eq(users.id, customerData.customer.updatedBy))
        .then((results) => results[0]);
    }

    const customer = customerData.customer;

    // Get customer locations if not allocated to all locations
    let customerLocations: { id: string; name: string }[] = [];
    if (!customer.isAllocatedToAllLocations) {
      customerLocations = await this.getCustomerLocations(customer.id);
    }

    const result: CustomerDto = {
      id: customer.id,
      businessId: customer.businessId,
      title: customer.title,
      firstName: customer.firstName,
      middleName: customer.middleName,
      lastName: customer.lastName,
      suffix: customer.suffix,
      customerDisplayName: customer.customerDisplayName,
      companyName: customer.companyName,
      email: customer.email,
      phoneNumber: customer.phoneNumber,
      mobileNumber: customer.mobileNumber,
      fax: customer.fax,
      other: customer.other,
      website: customer.website,
      isSubCustomer: customer.isSubCustomer,
      isAllocatedToAllLocations: customer.isAllocatedToAllLocations,
      billingAddress: customerData.billingAddress
        ? {
            id: customerData.billingAddress.id,
            street: customerData.billingAddress.street,
            city: customerData.billingAddress.city,
            state: customerData.billingAddress.state,
            zipCode: customerData.billingAddress.zipCode,
            country: customerData.billingAddress.country,
          }
        : undefined,
      shippingAddress: shippingAddress
        ? {
            id: shippingAddress.id,
            street: shippingAddress.street,
            city: shippingAddress.city,
            state: shippingAddress.state,
            zipCode: shippingAddress.zipCode,
            country: shippingAddress.country,
          }
        : undefined,
      customerGroup: customerData.customerGroup
        ? {
            id: customerData.customerGroup.id,
            name: customerData.customerGroup.name,
          }
        : undefined,
      notes: customer.notes,
      attachments: customer.attachments,
      salesTaxRegistration: customer.salesTaxRegistration,
      openingBalance: customer.openingBalance,
      openingBalanceAsOf: customer.openingBalanceAsOf,
      status: customer.status,
      locations: customerLocations.length > 0 ? customerLocations : undefined,
      createdBy:
        customerData.createdByUser?.firstName &&
        customerData.createdByUser?.lastName
          ? `${customerData.createdByUser.firstName} ${customerData.createdByUser.lastName}`
          : customerData.createdByUser?.email || 'Unknown',
      updatedBy:
        updatedByUser?.firstName && updatedByUser?.lastName
          ? `${updatedByUser.firstName} ${updatedByUser.lastName}`
          : updatedByUser?.email,
      createdAt: customer.createdAt,
      updatedAt: customer.updatedAt,
      totalPoints: customerData.customerReward?.totalPoints ?? 0,
      pointsEarnedYtd: customerData.customerReward?.pointsEarnedYtd ?? 0,
      isBlacklisted: customer.isBlacklisted,
      blacklistReason: customer.blacklistReason,
      blacklistDate: customer.blacklistDate,
    };

    return result;
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateCustomerDto: UpdateCustomerDto,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if customer exists
      const existingCustomer = await this.db
        .select()
        .from(customers)
        .where(
          and(
            eq(customers.id, id),
            eq(customers.businessId, businessId),
            isNull(customers.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!existingCustomer) {
        throw new NotFoundException('Customer not found');
      }

      // Check if display name is being updated and already exists
      if (
        updateCustomerDto.customerDisplayName &&
        updateCustomerDto.customerDisplayName !==
          existingCustomer.customerDisplayName
      ) {
        const duplicateCustomer = await this.db
          .select()
          .from(customers)
          .where(
            and(
              eq(customers.businessId, businessId),
              ilike(
                customers.customerDisplayName,
                updateCustomerDto.customerDisplayName,
              ),
              isNull(customers.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (duplicateCustomer) {
          throw new ConflictException(
            `Customer with display name "${updateCustomerDto.customerDisplayName}" already exists`,
          );
        }
      }

      // Check if email is being updated and already exists
      if (
        updateCustomerDto.email &&
        updateCustomerDto.email !== existingCustomer.email
      ) {
        const duplicateEmailCustomer = await this.db
          .select()
          .from(customers)
          .where(
            and(
              eq(customers.businessId, businessId),
              ilike(customers.email, updateCustomerDto.email),
              isNull(customers.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (duplicateEmailCustomer) {
          throw new ConflictException(
            `Customer with email "${updateCustomerDto.email}" already exists`,
          );
        }
      }

      // Validate customer group if provided
      if (updateCustomerDto.customerGroupId) {
        const customerGroup = await this.db
          .select()
          .from(customerGroups)
          .where(
            and(
              eq(customerGroups.id, updateCustomerDto.customerGroupId),
              eq(customerGroups.businessId, businessId),
              isNull(customerGroups.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (!customerGroup) {
          throw new BadRequestException('Invalid customer group ID');
        }
      }

      // Handle address updates if provided
      let billingAddressId: string | undefined;
      let shippingAddressId: string | undefined;

      if (updateCustomerDto.billingAddress) {
        const billingAddressResult = await this.addressService.create(userId, {
          ...updateCustomerDto.billingAddress,
          addressType: AddressType.BILLING,
          businessId,
        });
        billingAddressId = billingAddressResult.id;
      }

      if (updateCustomerDto.shippingAddress) {
        const shippingAddressResult = await this.addressService.create(userId, {
          ...updateCustomerDto.shippingAddress,
          addressType: AddressType.SHIPPING,
          businessId,
        });
        shippingAddressId = shippingAddressResult.id;
      }

      // Update customer
      const updateData: any = {
        ...updateCustomerDto,
        updatedBy: userId,
        updatedAt: new Date(),
      };

      // Remove address objects and location arrays from update data
      delete updateData.billingAddress;
      delete updateData.shippingAddress;
      delete updateData.locationIds;

      if (billingAddressId) {
        updateData.billingAddressId = billingAddressId;
      }

      if (shippingAddressId) {
        updateData.shippingAddressId = shippingAddressId;
      }

      const [updatedCustomer] = await this.db
        .update(customers)
        .set(updateData)
        .where(eq(customers.id, id))
        .returning();

      // Handle location associations if provided
      if (
        updateCustomerDto.isAllocatedToAllLocations !== undefined ||
        updateCustomerDto.locationIds !== undefined
      ) {
        await this.manageCustomerLocations(
          updatedCustomer.id,
          businessId,
          updateCustomerDto.isAllocatedToAllLocations ??
            existingCustomer.isAllocatedToAllLocations,
          updateCustomerDto.locationIds,
          userId,
        );
      }

      return { id: updatedCustomer.id };
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to update customer');
    }
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateCustomerDto: UpdateCustomerDto,
  ): Promise<{ id: string }> {
    return this.update(userId, businessId, id, updateCustomerDto);
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<{ id: string; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if customer exists
    const existingCustomer = await this.db
      .select()
      .from(customers)
      .where(
        and(
          eq(customers.id, id),
          eq(customers.businessId, businessId),
          isNull(customers.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!existingCustomer) {
      throw new NotFoundException('Customer not found');
    }

    // Soft delete the customer and clean up location associations
    await this.db
      .update(customers)
      .set({
        deletedBy: userId,
        deletedAt: new Date(),
      })
      .where(eq(customers.id, id));

    // Clean up location associations
    await this.db
      .delete(customerLocations)
      .where(eq(customerLocations.customerId, id));

    return { id, message: 'Customer deleted successfully' };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    customerIds: string[],
  ): Promise<{ deletedIds: string[]; count: number; failedIds: string[] }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const deletedIds: string[] = [];
    const failedIds: string[] = [];

    for (const customerId of customerIds) {
      try {
        await this.remove(userId, businessId, customerId);
        deletedIds.push(customerId);
      } catch {
        failedIds.push(customerId);
      }
    }

    return { deletedIds, count: deletedIds.length, failedIds };
  }

  async checkCustomerDisplayNameAvailability(
    businessId: string | null,
    customerDisplayName: string,
    excludeId?: string,
  ): Promise<{ available: boolean; customerDisplayName: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    const whereConditions = [
      eq(customers.businessId, businessId),
      ilike(customers.customerDisplayName, customerDisplayName),
      isNull(customers.deletedAt),
    ];

    if (excludeId) {
      whereConditions.push(sql`${customers.id} != ${excludeId}`);
    }

    const existingCustomer = await this.db
      .select()
      .from(customers)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    return {
      available: !existingCustomer,
      customerDisplayName,
    };
  }

  async checkCustomerEmailAvailability(
    businessId: string | null,
    email: string,
    excludeId?: string,
  ): Promise<{ available: boolean; email: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    const whereConditions = [
      eq(customers.businessId, businessId),
      ilike(customers.email, email),
      isNull(customers.deletedAt),
    ];

    if (excludeId) {
      whereConditions.push(sql`${customers.id} != ${excludeId}`);
    }

    const existingCustomer = await this.db
      .select()
      .from(customers)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    return {
      available: !existingCustomer,
      email,
    };
  }

  /**
   * Validate location IDs belong to the business
   */
  private async validateLocationIds(
    businessId: string,
    locationIds: string[],
  ): Promise<void> {
    if (!locationIds || locationIds.length === 0) {
      return;
    }

    const validLocations = await this.db
      .select({ id: locations.id })
      .from(locations)
      .where(
        and(
          eq(locations.businessId, businessId),
          inArray(locations.id, locationIds),
          isNull(locations.deletedAt),
        ),
      );

    if (validLocations.length !== locationIds.length) {
      throw new BadRequestException(
        'Some location IDs do not exist or belong to this business',
      );
    }
  }

  /**
   * Manage customer location associations
   */
  private async manageCustomerLocations(
    customerId: string,
    businessId: string,
    isAllocatedToAllLocations: boolean,
    locationIds: string[] | undefined,
    userId: string,
  ): Promise<void> {
    // Delete existing location associations
    await this.db
      .delete(customerLocations)
      .where(eq(customerLocations.customerId, customerId));

    // If allocated to all locations, no need to create specific associations
    if (isAllocatedToAllLocations) {
      return;
    }

    // If specific locations are provided, create associations
    if (locationIds && locationIds.length > 0) {
      await this.validateLocationIds(businessId, locationIds);

      const locationAssociations = locationIds.map((locationId) => ({
        customerId,
        locationId,
        createdBy: userId,
      }));

      await this.db.insert(customerLocations).values(locationAssociations);
    }
  }

  /**
   * Get customer location associations
   */
  private async getCustomerLocations(customerId: string): Promise<
    {
      id: string;
      name: string;
    }[]
  > {
    const customerLocationData = await this.db
      .select({
        id: locations.id,
        name: locations.name,
      })
      .from(customerLocations)
      .innerJoin(locations, eq(customerLocations.locationId, locations.id))
      .where(
        and(
          eq(customerLocations.customerId, customerId),
          isNull(locations.deletedAt),
        ),
      )
      .orderBy(locations.name);

    return customerLocationData;
  }

  /**
   * Count customers by customer group IDs
   */
  async countCustomersByGroupIds(
    businessId: string,
    customerGroupIds: string[],
  ): Promise<{ [customerGroupId: string]: number }> {
    if (!customerGroupIds || customerGroupIds.length === 0) {
      return {};
    }

    const customerCounts = await this.db
      .select({
        customerGroupId: customers.customerGroupId,
        count: count(),
      })
      .from(customers)
      .where(
        and(
          eq(customers.businessId, businessId),
          inArray(customers.customerGroupId, customerGroupIds),
          eq(customers.status, CustomerStatus.ACTIVE),
          isNull(customers.deletedAt),
        ),
      )
      .groupBy(customers.customerGroupId);

    const result: { [customerGroupId: string]: number } = {};

    // Initialize all group IDs with 0
    customerGroupIds.forEach((id) => {
      result[id] = 0;
    });

    // Fill in actual counts
    customerCounts.forEach(({ customerGroupId, count }) => {
      if (customerGroupId) {
        result[customerGroupId] = count;
      }
    });

    return result;
  }

  /**
   * Blacklist a customer
   */
  async blacklistCustomer(
    userId: string,
    businessId: string | null,
    customerId: string,
    reason: string,
  ): Promise<{ id: string; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if customer exists and is not already blacklisted
    const existingCustomer = await this.db
      .select()
      .from(customers)
      .where(
        and(
          eq(customers.id, customerId),
          eq(customers.businessId, businessId),
          isNull(customers.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!existingCustomer) {
      throw new NotFoundException('Customer not found');
    }

    if (existingCustomer.isBlacklisted) {
      throw new ConflictException('Customer is already blacklisted');
    }

    // Blacklist the customer
    await this.db
      .update(customers)
      .set({
        isBlacklisted: true,
        blacklistReason: reason,
        blacklistDate: new Date(),
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(customers.id, customerId));

    return { id: customerId, message: 'Customer blacklisted successfully' };
  }

  /**
   * Unblacklist a customer
   */
  async unblacklistCustomer(
    userId: string,
    businessId: string | null,
    customerId: string,
  ): Promise<{ id: string; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if customer exists and is blacklisted
    const existingCustomer = await this.db
      .select()
      .from(customers)
      .where(
        and(
          eq(customers.id, customerId),
          eq(customers.businessId, businessId),
          isNull(customers.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!existingCustomer) {
      throw new NotFoundException('Customer not found');
    }

    if (!existingCustomer.isBlacklisted) {
      throw new ConflictException('Customer is not blacklisted');
    }

    // Unblacklist the customer
    await this.db
      .update(customers)
      .set({
        isBlacklisted: false,
        blacklistReason: null,
        blacklistDate: null,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(customers.id, customerId));

    return { id: customerId, message: 'Customer unblacklisted successfully' };
  }

  /**
   * Get all blacklisted customers
   */
  async getBlacklistedCustomers(
    _userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    customerDisplayName?: string,
    email?: string,
    companyName?: string,
    from?: string,
    to?: string,
    sort?: string,
  ): Promise<{
    data: CustomerListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions for blacklisted customers
    const whereConditions = [
      isNull(customers.deletedAt),
      eq(customers.businessId, businessId),
      eq(customers.isBlacklisted, true),
    ];

    // Add date range filters for blacklist date
    if (from) {
      whereConditions.push(gte(customers.blacklistDate, new Date(from)));
    }
    if (to) {
      whereConditions.push(lte(customers.blacklistDate, new Date(to)));
    }

    // Add search filters
    if (customerDisplayName) {
      whereConditions.push(
        ilike(customers.customerDisplayName, `%${customerDisplayName}%`),
      );
    }
    if (email) {
      whereConditions.push(ilike(customers.email, `%${email}%`));
    }
    if (companyName) {
      whereConditions.push(ilike(customers.companyName, `%${companyName}%`));
    }

    // Build sort conditions
    let orderBy = [desc(customers.blacklistDate), asc(customers.id)];

    if (sort) {
      try {
        const parsedSort = JSON.parse(sort);
        if (parsedSort.length > 0) {
          const sortField = parsedSort[0];
          const isDesc = sortField.desc === true;

          switch (sortField.id) {
            case 'customerDisplayName':
              orderBy = [
                isDesc
                  ? desc(customers.customerDisplayName)
                  : asc(customers.customerDisplayName),
                asc(customers.id),
              ];
              break;
            case 'companyName':
              orderBy = [
                isDesc
                  ? desc(customers.companyName)
                  : asc(customers.companyName),
                asc(customers.id),
              ];
              break;
            case 'email':
              orderBy = [
                isDesc ? desc(customers.email) : asc(customers.email),
                asc(customers.id),
              ];
              break;
            case 'blacklistDate':
              orderBy = [
                isDesc
                  ? desc(customers.blacklistDate)
                  : asc(customers.blacklistDate),
                asc(customers.id),
              ];
              break;
          }
        }
      } catch {
        // Invalid JSON, use default sort
      }
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: count() })
      .from(customers)
      .where(and(...whereConditions));

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Get paginated data with joins
    const customersData = await this.db
      .select({
        id: customers.id,
        customerDisplayName: customers.customerDisplayName,
        companyName: customers.companyName,
        email: customers.email,
        phoneNumber: customers.phoneNumber,
        mobileNumber: customers.mobileNumber,
        status: customers.status,
        isSubCustomer: customers.isSubCustomer,
        isAllocatedToAllLocations: customers.isAllocatedToAllLocations,
        openingBalance: customers.openingBalance,
        salesTaxRegistration: customers.salesTaxRegistration,
        customerGroupName: customerGroups.name,
        billingAddressCity: sql<string>`${addresses.city}`,
        billingAddressState: sql<string>`${addresses.state}`,
        totalPoints: customerRewards.totalPoints,
        pointsEarnedYtd: customerRewards.pointsEarnedYtd,
        isBlacklisted: customers.isBlacklisted,
        blacklistReason: customers.blacklistReason,
        blacklistDate: customers.blacklistDate,
      })
      .from(customers)
      .leftJoin(
        customerGroups,
        eq(customers.customerGroupId, customerGroups.id),
      )
      .leftJoin(addresses, eq(customers.billingAddressId, addresses.id))
      .leftJoin(
        customerRewards,
        and(
          eq(customers.id, customerRewards.customerId),
          isNull(customerRewards.deletedAt),
        ),
      )
      .where(and(...whereConditions))
      .orderBy(...orderBy)
      .limit(limit)
      .offset(offset);

    const data: CustomerListDto[] = customersData.map((customer) => ({
      id: customer.id,
      customerDisplayName: customer.customerDisplayName,
      companyName: customer.companyName,
      email: customer.email,
      phoneNumber: customer.phoneNumber,
      mobileNumber: customer.mobileNumber,
      status: customer.status,
      isSubCustomer: customer.isSubCustomer,
      isAllocatedToAllLocations: customer.isAllocatedToAllLocations,
      customerGroupName: customer.customerGroupName,
      openingBalance: customer.openingBalance,
      salesTaxRegistration: customer.salesTaxRegistration,
      primaryAddress:
        customer.billingAddressCity && customer.billingAddressState
          ? `${customer.billingAddressCity}, ${customer.billingAddressState}`
          : undefined,
      totalPoints: customer.totalPoints ?? 0,
      pointsEarnedYtd: customer.pointsEarnedYtd ?? 0,
      locationNames: undefined,
      isBlacklisted: customer.isBlacklisted,
    }));

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }
}
