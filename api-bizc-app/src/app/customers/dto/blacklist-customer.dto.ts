import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, MaxLength } from 'class-validator';

export class BlacklistCustomerDto {
  @ApiProperty({
    description: 'Reason for blacklisting the customer',
    example: 'Customer has been blacklisted due to fraudulent activities',
    maxLength: 500,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(500)
  reason: string;
}

export class BlacklistResponseDto {
  @ApiProperty({
    description: 'Customer ID that was blacklisted',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'Success message',
    example: 'Customer blacklisted successfully',
  })
  message: string;
}

export class UnblacklistResponseDto {
  @ApiProperty({
    description: 'Customer ID that was unblacklisted',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'Success message',
    example: 'Customer unblacklisted successfully',
  })
  message: string;
}
