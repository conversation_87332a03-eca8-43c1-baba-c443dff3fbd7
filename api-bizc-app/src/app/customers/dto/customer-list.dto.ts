import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { CustomerStatus } from '../../shared/types/customer.enum';

export class CustomerListDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Customer ID',
  })
  id: string;

  @ApiProperty({ example: 'John Doe', description: 'Customer display name' })
  customerDisplayName: string;

  @ApiPropertyOptional({
    example: 'Acme Corporation',
    description: 'Company name',
    nullable: true,
  })
  companyName?: string;

  @ApiPropertyOptional({
    example: '<EMAIL>',
    description: 'Customer email address',
    nullable: true,
  })
  email?: string;

  @ApiPropertyOptional({
    example: '******-123-4567',
    description: 'Customer phone number',
    nullable: true,
  })
  phoneNumber?: string;

  @ApiPropertyOptional({
    example: '******-987-6543',
    description: 'Customer mobile number',
    nullable: true,
  })
  mobileNumber?: string;

  @ApiProperty({
    example: CustomerStatus.ACTIVE,
    enum: CustomerStatus,
    enumName: 'CustomerStatus',
    description: 'Customer status',
  })
  status: CustomerStatus;

  @ApiProperty({
    example: false,
    description: 'Whether this is a sub-customer',
  })
  isSubCustomer: boolean;

  @ApiProperty({
    example: false,
    description: 'Whether customer is allocated to all business locations',
  })
  isAllocatedToAllLocations: boolean;

  @ApiPropertyOptional({
    example: 'Premium Customers',
    description: 'Customer group name',
    nullable: true,
  })
  customerGroupName?: string;

  @ApiPropertyOptional({
    example: '1000.00',
    description: 'Opening balance amount',
    nullable: true,
  })
  openingBalance?: string;

  @ApiPropertyOptional({
    example: 'TAX123456789',
    description: 'Sales tax registration number',
    nullable: true,
  })
  salesTaxRegistration?: string;

  @ApiPropertyOptional({
    example: 'New York, NY',
    description: 'Primary address (city, state)',
    nullable: true,
  })
  primaryAddress?: string;

  @ApiPropertyOptional({
    example: 'Main Office, Warehouse',
    description: 'Assigned location names (comma-separated)',
    nullable: true,
  })
  locationNames?: string;

  @ApiPropertyOptional({
    example: 1250,
    description: 'Total reward points balance',
    nullable: true,
  })
  totalPoints?: number;

  @ApiPropertyOptional({
    example: 2500,
    description: 'Total points earned year-to-date',
    nullable: true,
  })
  pointsEarnedYtd?: number;

  @ApiPropertyOptional({
    example: false,
    description: 'Whether the customer is blacklisted',
    default: false,
  })
  isBlacklisted?: boolean;
}
