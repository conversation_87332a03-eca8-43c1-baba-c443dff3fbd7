import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { CustomerStatus } from '../../shared/types/customer.enum';

export class CustomerDto {
  @ApiProperty({ description: 'Unique identifier for the customer' })
  id: string;

  @ApiProperty({ description: 'Business ID the customer belongs to' })
  businessId: string;

  @ApiPropertyOptional({
    description: 'Customer title (Mr., Mrs., Dr., etc.)',
    nullable: true,
  })
  title?: string;

  @ApiPropertyOptional({
    description: 'Customer first name',
    nullable: true,
  })
  firstName?: string;

  @ApiPropertyOptional({
    description: 'Customer middle name',
    nullable: true,
  })
  middleName?: string;

  @ApiPropertyOptional({
    description: 'Customer last name',
    nullable: true,
  })
  lastName?: string;

  @ApiPropertyOptional({
    description: 'Customer suffix (Jr., Sr., III, etc.)',
    nullable: true,
  })
  suffix?: string;

  @ApiProperty({ description: 'Customer display name' })
  customerDisplayName: string;

  @ApiPropertyOptional({
    description: 'Company name',
    nullable: true,
  })
  companyName?: string;

  @ApiPropertyOptional({
    description: 'Customer email address',
    nullable: true,
  })
  email?: string;

  @ApiPropertyOptional({
    description: 'Customer phone number',
    nullable: true,
  })
  phoneNumber?: string;

  @ApiPropertyOptional({
    description: 'Customer mobile number',
    nullable: true,
  })
  mobileNumber?: string;

  @ApiPropertyOptional({
    description: 'Customer fax number',
    nullable: true,
  })
  fax?: string;

  @ApiPropertyOptional({
    description: 'Other contact information',
    nullable: true,
  })
  other?: string;

  @ApiPropertyOptional({
    description: 'Customer website',
    nullable: true,
  })
  website?: string;

  @ApiProperty({
    description: 'Whether this is a sub-customer',
    example: false,
  })
  isSubCustomer: boolean;

  @ApiProperty({
    description: 'Whether customer is allocated to all business locations',
    example: false,
  })
  isAllocatedToAllLocations: boolean;

  @ApiPropertyOptional({
    description: 'Billing address information',
    type: Object,
    nullable: true,
  })
  billingAddress?: {
    id: string;
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };

  @ApiPropertyOptional({
    description: 'Shipping address information',
    type: Object,
    nullable: true,
  })
  shippingAddress?: {
    id: string;
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };

  @ApiPropertyOptional({
    description: 'Customer group information',
    type: Object,
    nullable: true,
  })
  customerGroup?: {
    id: string;
    name: string;
  };

  @ApiPropertyOptional({
    description: 'Customer notes',
    nullable: true,
  })
  notes?: string;

  @ApiPropertyOptional({
    description: 'Array of attachment IDs',
    type: [String],
    nullable: true,
  })
  attachments?: string[];

  @ApiPropertyOptional({
    description: 'Sales tax registration number',
    nullable: true,
  })
  salesTaxRegistration?: string;

  @ApiPropertyOptional({
    description: 'Opening balance amount',
    nullable: true,
    example: '1000.00',
  })
  openingBalance?: string;

  @ApiPropertyOptional({
    description: 'Opening balance as of date',
    nullable: true,
    example: '2023-01-01',
  })
  openingBalanceAsOf?: string;

  @ApiProperty({
    description: 'Customer status',
    enum: CustomerStatus,
    enumName: 'CustomerStatus',
    example: CustomerStatus.ACTIVE,
  })
  status: CustomerStatus;

  @ApiPropertyOptional({
    description: 'Locations assigned to this customer',
    type: [Object],
    nullable: true,
  })
  locations?: {
    id: string;
    name: string;
  }[];

  @ApiProperty({
    example: 'John Doe',
    description: 'Name of the user who created the customer',
  })
  createdBy: string;

  @ApiPropertyOptional({
    example: 'Jane Smith',
    description: 'Name of the user who last updated the customer',
    nullable: true,
  })
  updatedBy?: string;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Creation timestamp',
  })
  createdAt: Date;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Last update timestamp',
  })
  updatedAt: Date;

  @ApiPropertyOptional({
    example: 1250,
    description: 'Total reward points balance',
    nullable: true,
  })
  totalPoints?: number;

  @ApiPropertyOptional({
    example: 2500,
    description: 'Total points earned year-to-date',
    nullable: true,
  })
  pointsEarnedYtd?: number;

  @ApiPropertyOptional({
    example: false,
    description: 'Whether the customer is blacklisted',
    default: false,
  })
  isBlacklisted?: boolean;

  @ApiPropertyOptional({
    example: 'Customer has been blacklisted due to fraudulent activities',
    description: 'Reason for blacklisting the customer',
    nullable: true,
  })
  blacklistReason?: string;

  @ApiPropertyOptional({
    example: '2023-06-15T10:30:00Z',
    description: 'Date when the customer was blacklisted',
    nullable: true,
  })
  blacklistDate?: Date;
}
