import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Request,
  UseGuards,
  ParseIntPipe,
  DefaultValuePipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { CustomersService } from './customers.service';
import { CreateCustomerDto } from './dto/create-customer.dto';
import { UpdateCustomerDto } from './dto/update-customer.dto';
import { CustomerDto } from './dto/customer.dto';
import { CustomerSlimDto } from './dto/customer-slim.dto';
import { CustomerAutocompleteDto } from './dto/customer-autocomplete.dto';
import { CustomerIdResponseDto } from './dto/customer-id-response.dto';
import { PaginatedCustomersResponseDto } from './dto/paginated-customers-response.dto';
import { BulkCreateCustomerDto } from './dto/bulk-create-customer.dto';
import { BulkCustomerIdsResponseDto } from './dto/bulk-customer-ids-response.dto';
import { BulkDeleteCustomerDto } from './dto/bulk-delete-customer.dto';
import { BulkDeleteCustomerResponseDto } from './dto/bulk-delete-customer-response.dto';
import { DeleteCustomerResponseDto } from './dto/delete-customer-response.dto';
import { CustomerNameAvailabilityResponseDto } from './dto/check-customer-name.dto';
import { CustomerEmailAvailabilityResponseDto } from './dto/check-customer-email.dto';
import {
  BlacklistCustomerDto,
  BlacklistResponseDto,
  UnblacklistResponseDto,
} from './dto/blacklist-customer.dto';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { Permission } from '../shared/types/permission.enum';

@ApiTags('customers')
@Controller('customers')
@UseGuards(PermissionsGuard)
export class CustomersController {
  constructor(private readonly customersService: CustomersService) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.CUSTOMER_CREATE)
  @ApiOperation({
    summary: 'Create a new customer',
    description: 'Creates a new customer for the active business',
  })
  @ApiBody({
    description: 'Customer data',
    type: CreateCustomerDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Customer created successfully',
    type: CustomerIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Customer display name or email already exists',
  })
  create(
    @Request() req,
    @Body() createCustomerDto: CreateCustomerDto,
  ): Promise<CustomerIdResponseDto> {
    return this.customersService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createCustomerDto,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CUSTOMER_CREATE)
  @ApiOperation({
    summary: 'Bulk create customers',
    description: 'Creates multiple customers at once for the active business',
  })
  @ApiBody({
    description: 'Array of customer data',
    type: BulkCreateCustomerDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Customers created successfully',
    type: BulkCustomerIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Customer names already exist',
  })
  bulkCreate(
    @Request() req,
    @Body() bulkCreateCustomerDto: BulkCreateCustomerDto,
  ): Promise<BulkCustomerIdsResponseDto> {
    return this.customersService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateCustomerDto.customers,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.CUSTOMER_READ)
  @ApiOperation({
    summary: 'Get all customers for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Start date for filtering (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'to',
    description: 'End date for filtering (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'customerDisplayName',
    description: 'Filter by customer display name',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'email',
    description: 'Filter by email',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'companyName',
    description: 'Filter by company name',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'phoneNumber',
    description: 'Filter by phone number',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'mobileNumber',
    description: 'Filter by mobile number',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'status',
    description: 'Filter by status',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'customerGroupId',
    description: 'Filter by customer group ID',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'filters',
    description:
      'Advanced filters as JSON string with operator support. Supported operators: iLike (contains), notILike (does not contain), eq (is), ne (is not), isEmpty (is empty), isNotEmpty (is not empty)',
    required: false,
    type: String,
    example:
      '[{"id":"customerDisplayName","value":"John","operator":"iLike","type":"text","rowId":"1"},{"id":"status","value":"active","operator":"eq","type":"select","rowId":"2"}]',
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for advanced filters',
    required: false,
    enum: ['and', 'or'],
    example: 'and',
  })
  @ApiQuery({
    name: 'sort',
    description:
      'Sort configuration as JSON string. Supported fields: customerDisplayName, companyName, email, createdAt, updatedAt, openingBalance',
    required: false,
    type: String,
    example: '[{"id":"customerDisplayName","desc":false}]',
  })
  @ApiResponse({
    status: 200,
    description: 'Customers retrieved successfully',
    type: PaginatedCustomersResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('customerDisplayName') customerDisplayName?: string,
    @Query('email') email?: string,
    @Query('companyName') companyName?: string,
    @Query('phoneNumber') phoneNumber?: string,
    @Query('mobileNumber') mobileNumber?: string,
    @Query('status') status?: string,
    @Query('customerGroupId') customerGroupId?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedCustomersResponseDto> {
    return this.customersService.findAllOptimized(
      req.user.id,
      req.user.activeBusinessId,
      page,
      limit,
      from,
      to,
      customerDisplayName,
      email,
      companyName,
      phoneNumber,
      mobileNumber,
      status,
      customerGroupId,
      filters,
      joinOperator,
      sort,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CUSTOMER_READ)
  @ApiOperation({
    summary: 'Get all customers in slim format',
    description:
      'Returns a simplified list of customers for dropdowns and selections',
  })
  @ApiResponse({
    status: 200,
    description: 'Customers retrieved successfully',
    type: [CustomerSlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req): Promise<CustomerSlimDto[]> {
    return this.customersService.findAllSlim(
      req.user.id,
      req.user.activeBusinessId,
    );
  }

  @Get('autocomplete')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CUSTOMER_READ)
  @ApiOperation({
    summary: 'Search customers for autocomplete',
    description:
      'Returns customers matching the search query for autocomplete functionality',
  })
  @ApiQuery({
    name: 'search',
    description: 'Search term to filter customers by name',
    required: false,
    type: String,
    example: 'john',
  })
  @ApiQuery({
    name: 'limit',
    description: 'Maximum number of results to return (default: 10, max: 50)',
    required: false,
    type: Number,
    example: 10,
  })
  @ApiResponse({
    status: 200,
    description: 'Customers returned successfully',
    type: [CustomerAutocompleteDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async findAutocomplete(
    @Request() req,
    @Query('search') search?: string,
    @Query('limit') limit?: number,
  ): Promise<CustomerAutocompleteDto[]> {
    return this.customersService.findAutocomplete(
      req.user.activeBusinessId,
      search,
      limit ? Math.min(parseInt(limit.toString()), 50) : 10,
    );
  }

  @Get('check-name-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CUSTOMER_READ)
  @ApiOperation({
    summary: 'Check customer display name availability',
    description: 'Checks if a customer display name is available for use',
  })
  @ApiQuery({
    name: 'customerDisplayName',
    description: 'Customer display name to check',
    required: true,
    type: String,
  })
  @ApiQuery({
    name: 'excludeId',
    description: 'Customer ID to exclude from check (for updates)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Name availability checked successfully',
    type: CustomerNameAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkNameAvailability(
    @Request() req,
    @Query('customerDisplayName') customerDisplayName: string,
    @Query('excludeId') excludeId?: string,
  ): Promise<CustomerNameAvailabilityResponseDto> {
    return this.customersService.checkCustomerDisplayNameAvailability(
      req.user.activeBusinessId,
      customerDisplayName,
      excludeId,
    );
  }

  @Get('check-email-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CUSTOMER_READ)
  @ApiOperation({
    summary: 'Check customer email availability',
    description: 'Checks if an email address is available for use',
  })
  @ApiQuery({
    name: 'email',
    description: 'Email address to check',
    required: true,
    type: String,
  })
  @ApiQuery({
    name: 'excludeId',
    description: 'Customer ID to exclude from check (for updates)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Email availability checked successfully',
    type: CustomerEmailAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkEmailAvailability(
    @Request() req,
    @Query('email') email: string,
    @Query('excludeId') excludeId?: string,
  ): Promise<CustomerEmailAvailabilityResponseDto> {
    return this.customersService.checkCustomerEmailAvailability(
      req.user.activeBusinessId,
      email,
      excludeId,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CUSTOMER_READ)
  @ApiOperation({
    summary: 'Get a customer by ID',
    description: 'Retrieves a specific customer by their ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Customer ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Customer retrieved successfully',
    type: CustomerDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Customer not found',
  })
  findOne(@Request() req, @Param('id') id: string): Promise<CustomerDto> {
    return this.customersService.findOne(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CUSTOMER_UPDATE)
  @ApiOperation({
    summary: 'Update a customer',
    description: 'Updates an existing customer',
  })
  @ApiParam({
    name: 'id',
    description: 'Customer ID',
    type: String,
  })
  @ApiBody({
    description: 'Customer update data',
    type: UpdateCustomerDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Customer updated successfully',
    type: CustomerIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Customer not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Customer display name or email already exists',
  })
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateCustomerDto: UpdateCustomerDto,
  ): Promise<CustomerIdResponseDto> {
    return this.customersService.updateAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateCustomerDto,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CUSTOMER_DELETE)
  @ApiOperation({ summary: 'Bulk delete customers' })
  @ApiBody({
    description: 'Array of customer IDs to delete',
    type: BulkDeleteCustomerDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Customers deleted successfully',
    type: BulkDeleteCustomerResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  bulkDelete(
    @Request() req,
    @Body() bulkDeleteCustomerDto: BulkDeleteCustomerDto,
  ): Promise<BulkDeleteCustomerResponseDto> {
    return this.customersService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteCustomerDto.customerIds,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CUSTOMER_DELETE)
  @ApiOperation({
    summary: 'Delete a customer',
    description: 'Soft deletes a customer by ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Customer ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Customer deleted successfully',
    type: DeleteCustomerResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Customer not found',
  })
  remove(
    @Request() req,
    @Param('id') id: string,
  ): Promise<DeleteCustomerResponseDto> {
    return this.customersService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }

  @Post(':id/blacklist')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CUSTOMER_UPDATE)
  @ApiOperation({
    summary: 'Blacklist a customer',
    description: 'Blacklists a customer with a reason',
  })
  @ApiParam({
    name: 'id',
    description: 'Customer ID',
    type: String,
  })
  @ApiBody({
    description: 'Blacklist reason',
    type: BlacklistCustomerDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Customer blacklisted successfully',
    type: BlacklistResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Customer not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Customer is already blacklisted',
  })
  blacklistCustomer(
    @Request() req,
    @Param('id') id: string,
    @Body() blacklistCustomerDto: BlacklistCustomerDto,
  ): Promise<BlacklistResponseDto> {
    return this.customersService.blacklistCustomer(
      req.user.id,
      req.user.activeBusinessId,
      id,
      blacklistCustomerDto.reason,
    );
  }

  @Delete(':id/blacklist')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CUSTOMER_UPDATE)
  @ApiOperation({
    summary: 'Unblacklist a customer',
    description: 'Removes a customer from the blacklist',
  })
  @ApiParam({
    name: 'id',
    description: 'Customer ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Customer unblacklisted successfully',
    type: UnblacklistResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Customer not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Customer is not blacklisted',
  })
  unblacklistCustomer(
    @Request() req,
    @Param('id') id: string,
  ): Promise<UnblacklistResponseDto> {
    return this.customersService.unblacklistCustomer(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }

  @Get('blacklisted')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CUSTOMER_READ)
  @ApiOperation({
    summary: 'Get all blacklisted customers',
    description: 'Retrieves a paginated list of blacklisted customers',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'customerDisplayName',
    description: 'Filter by customer display name',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'email',
    description: 'Filter by email',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'companyName',
    description: 'Filter by company name',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'from',
    description: 'Start date for blacklist date filtering (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'to',
    description: 'End date for blacklist date filtering (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'sort',
    description:
      'Sort configuration as JSON string. Supported fields: customerDisplayName, companyName, email, blacklistDate',
    required: false,
    type: String,
    example: '[{"id":"blacklistDate","desc":true}]',
  })
  @ApiResponse({
    status: 200,
    description: 'Blacklisted customers retrieved successfully',
    type: PaginatedCustomersResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  getBlacklistedCustomers(
    @Request() req,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('customerDisplayName') customerDisplayName?: string,
    @Query('email') email?: string,
    @Query('companyName') companyName?: string,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('sort') sort?: string,
  ): Promise<PaginatedCustomersResponseDto> {
    return this.customersService.getBlacklistedCustomers(
      req.user.id,
      req.user.activeBusinessId,
      page,
      limit,
      customerDisplayName,
      email,
      companyName,
      from,
      to,
      sort,
    );
  }
}
