import { type Metadata } from "next";
import { DataPage } from "@/components/shared/data-page";
import WorkOrderStatusesTableWrapper from "@/components/work-order-statuses/work-order-statuses-table-wrapper";

export const metadata: Metadata = {
  title: "Work Order Statuses",
  description: "Manage work order statuses",
};

export default async function IndexPage() {
  return (
    <DataPage
      TableComponent={WorkOrderStatusesTableWrapper}
      columnCount={6}
      searchableColumnCount={2}
      filterableColumnCount={2}
      cellWidths={["15rem", "20rem", "12rem", "10rem", "10rem", "8rem"]}
      isDemo={false}
    />
  );
} 