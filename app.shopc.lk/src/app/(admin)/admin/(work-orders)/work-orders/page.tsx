import { type Metadata } from "next";
import { DataPage } from "@/components/shared/data-page";
import WorkOrdersTableWrapper from "@/components/work-orders/work-orders-table-wrapper";

export const metadata: Metadata = {
  title: "Work Orders",
  description: "Manage work orders",
};

export default async function IndexPage() {
  return (
    <DataPage
      TableComponent={WorkOrdersTableWrapper}
      columnCount={8}
      searchableColumnCount={2}
      filterableColumnCount={3}
      cellWidths={[
        "15rem",
        "20rem",
        "12rem",
        "12rem",
        "12rem",
        "12rem",
        "10rem",
        "8rem",
      ]}
      isDemo={false}
    />
  );
}
