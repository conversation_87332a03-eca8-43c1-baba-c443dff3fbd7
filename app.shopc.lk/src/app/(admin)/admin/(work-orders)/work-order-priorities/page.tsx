import { type Metadata } from "next";
import { DataPage } from "@/components/shared/data-page";
import WorkOrderPrioritiesTableWrapper from "@/components/work-order-priorities/work-order-priorities-table-wrapper";

export const metadata: Metadata = {
  title: "Work Order Priorities",
  description: "Manage work order priorities",
};

export default async function IndexPage() {
  return (
    <DataPage
      TableComponent={WorkOrderPrioritiesTableWrapper}
      columnCount={6}
      searchableColumnCount={2}
      filterableColumnCount={2}
      cellWidths={["15rem", "20rem", "12rem", "10rem", "10rem", "8rem"]}
      isDemo={false}
    />
  );
} 