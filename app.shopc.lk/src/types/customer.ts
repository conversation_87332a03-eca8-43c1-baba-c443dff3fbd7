import { ApiResponse } from "./common";

// Autocomplete types
export interface CustomerAutocomplete {
  id: string;
  name: string;
}

export type CustomerAutocompleteResponse = ApiResponse<CustomerAutocomplete[]>;

export enum CustomerStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
}

// Backend DTO: CustomerDto
export interface CustomerDto {
  id: string;
  businessId: string;
  title?: string;
  firstName?: string;
  middleName?: string;
  lastName?: string;
  suffix?: string;
  customerDisplayName: string;
  companyName?: string;
  email?: string;
  phoneNumber?: string;
  mobileNumber?: string;
  fax?: string;
  website?: string;
  other?: string;
  isSubCustomer: boolean;
  isAllocatedToAllLocations: boolean;
  billingAddress?: {
    id: string;
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  shippingAddress?: {
    id: string;
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  customerGroup?: {
    id: string;
    name: string;
  };
  notes?: string;
  attachments?: string[];
  salesTaxRegistration?: string;
  openingBalance?: string;
  openingBalanceAsOf?: string;
  status: CustomerStatus;
  locations?: {
    id: string;
    name: string;
  }[];
  createdBy: string;
  updatedBy?: string;
  deletedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  totalPoints?: number;
  pointsEarnedYtd?: number;
  isBlacklisted?: boolean;
  blacklistReason?: string;
  blacklistDate?: Date;
}

// Backend DTO: CustomerSlimDto
export interface CustomerSlimDto {
  id: string;
  customerDisplayName: string;
  companyName?: string;
  status: CustomerStatus;
}

// Backend DTO: CustomerListDto
export interface CustomerListDto {
  id: string;
  customerDisplayName: string;
  companyName?: string;
  email?: string;
  phoneNumber?: string;
  mobileNumber?: string;
  status: CustomerStatus;
  isSubCustomer: boolean;
  isAllocatedToAllLocations: boolean;
  customerGroupName?: string;
  openingBalance?: string;
  salesTaxRegistration?: string;
  primaryAddress?: string;
  locationNames?: string;
  totalPoints?: number;
  pointsEarnedYtd?: number;
  isBlacklisted?: boolean;
}

// Customer Address DTO
export interface CustomerAddressDto {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  isDefault?: boolean;
}

// Backend DTO: CreateCustomerDto
export interface CreateCustomerDto {
  title?: string;
  firstName?: string;
  middleName?: string;
  lastName?: string;
  suffix?: string;
  customerDisplayName: string;
  companyName?: string;
  email?: string;
  phoneNumber?: string;
  mobileNumber?: string;
  fax?: string;
  website?: string;
  other?: string;
  isSubCustomer?: boolean;
  billingAddress?: CustomerAddressDto;
  shippingAddress?: CustomerAddressDto;
  customerGroupId?: string;
  notes?: string;
  attachments?: string[];
  salesTaxRegistration?: string;
  openingBalance?: string;
  openingBalanceAsOf?: string;
  status?: CustomerStatus;
  isAllocatedToAllLocations?: boolean;
  locationIds?: string[];
}

// Backend DTO: UpdateCustomerDto
export interface UpdateCustomerDto {
  title?: string;
  firstName?: string;
  middleName?: string;
  lastName?: string;
  suffix?: string;
  customerDisplayName?: string;
  companyName?: string;
  email?: string;
  phoneNumber?: string;
  mobileNumber?: string;
  fax?: string;
  website?: string;
  other?: string;
  isSubCustomer?: boolean;
  billingAddress?: CustomerAddressDto;
  shippingAddress?: CustomerAddressDto;
  customerGroupId?: string;
  notes?: string;
  attachments?: string[];
  salesTaxRegistration?: string;
  openingBalance?: string;
  openingBalanceAsOf?: string;
  status?: CustomerStatus;
  isAllocatedToAllLocations?: boolean;
  locationIds?: string[];
}

// Response DTOs
export interface CustomerIdResponseDto {
  id: string;
}

export interface PaginatedCustomersResponseDto {
  data: CustomerListDto[];
  meta: {
    total: number;
    page: number;
    totalPages: number;
  };
}

export interface BulkCreateCustomerDto {
  customers: CreateCustomerDto[];
}

export interface BulkCustomerIdsResponseDto {
  ids: string[];
  message: string;
}

export interface BulkDeleteCustomerDto {
  customerIds: string[];
}

export interface BulkDeleteCustomerResponseDto {
  deleted: number;
  message: string;
}

export interface DeleteCustomerResponseDto {
  id: string;
  message: string;
}

// Blacklist DTOs
export interface BlacklistCustomerDto {
  reason: string;
}

export interface BlacklistResponseDto {
  id: string;
  message: string;
}

export interface UnblacklistResponseDto {
  id: string;
  message: string;
}

export interface CustomerNameAvailabilityResponseDto {
  available: boolean;
}

export interface CustomerEmailAvailabilityResponseDto {
  available: boolean;
}

// API Response Types
export type CustomerResponse = ApiResponse<CustomerDto>;
export type CustomerPaginatedResponse =
  ApiResponse<PaginatedCustomersResponseDto>;
export type CustomerIdResponse = ApiResponse<CustomerIdResponseDto>;
export type BulkCustomerIdsResponse = ApiResponse<BulkCustomerIdsResponseDto>;
export type BulkDeleteCustomerResponse =
  ApiResponse<BulkDeleteCustomerResponseDto>;
export type CustomerNameAvailabilityResponse =
  ApiResponse<CustomerNameAvailabilityResponseDto>;
export type CustomerEmailAvailabilityResponse =
  ApiResponse<CustomerEmailAvailabilityResponseDto>;
export type SimpleCustomerResponse = ApiResponse<CustomerSlimDto[]>;
export type DeleteCustomerResponse = ApiResponse<DeleteCustomerResponseDto>;
export type BlacklistResponse = ApiResponse<BlacklistResponseDto>;
export type UnblacklistResponse = ApiResponse<UnblacklistResponseDto>;

// Table data interface for frontend tables
export interface CustomerTableData extends CustomerListDto {
  // Additional computed fields for table display
  createdAt?: Date;
  updatedAt?: Date;
  blacklistReason?: string;
  blacklistDate?: Date;
}

// Form values interface for UI components
export interface CustomerFormValues {
  title?: string;
  firstName?: string;
  middleName?: string;
  lastName?: string;
  suffix?: string;
  customerDisplayName: string;
  companyName?: string;
  email?: string;
  phoneNumber?: string;
  mobileNumber?: string;
  fax?: string;
  website?: string;
  other?: string;
  isSubCustomer?: boolean;
  billingAddress?: CustomerAddressDto;
  shippingAddress?: CustomerAddressDto;
  customerGroupId?: string;
  notes?: string;
  attachments?: string[];
  salesTaxRegistration?: string;
  openingBalance?: string;
  openingBalanceAsOf?: string;
  status?: CustomerStatus;
  isAllocatedToAllLocations?: boolean;
  locationIds?: string[];
}

// Legacy aliases for backward compatibility (to be removed gradually)
export type Customer = CustomerDto;
export type SimpleCustomerData = CustomerSlimDto;
export type CustomerPaginatedData = PaginatedCustomersResponseDto;
