"use client";

import * as React from "react";
import { toast } from "sonner";
import { useMutation, useQueryClient } from "@tanstack/react-query";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { unblacklistCustomer } from "@/lib/customers";
import { customerKeys } from "@/lib/customers/hooks";
import { CustomerTableData } from "@/types/customer";
import { ApiStatus } from "@/types/common";

interface UnblacklistCustomerDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  customer: CustomerTableData | null;
  isDemo?: boolean;
  onSuccess?: () => void;
}

export function UnblacklistCustomerDialog({
  open,
  onOpenChange,
  customer,
  isDemo = false,
  onSuccess,
}: UnblacklistCustomerDialogProps) {
  const queryClient = useQueryClient();

  const unblacklistMutation = useMutation({
    mutationFn: async () => {
      if (!customer) throw new Error("No customer selected");
      return unblacklistCustomer(customer.id, isDemo);
    },
    onSuccess: (response) => {
      if (response.status === ApiStatus.SUCCESS) {
        toast.success("Customer removed from blacklist successfully");
        queryClient.invalidateQueries({ queryKey: customerKeys.all });
        onOpenChange(false);
        onSuccess?.();
      } else {
        toast.error(response.message || "Failed to remove customer from blacklist");
      }
    },
    onError: (error: any) => {
      toast.error(error?.message || "Failed to remove customer from blacklist");
    },
  });

  const handleUnblacklist = () => {
    unblacklistMutation.mutate();
  };

  const handleClose = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Remove from Blacklist</DialogTitle>
          <DialogDescription>
            Are you sure you want to remove{" "}
            <span className="font-semibold">{customer?.customerDisplayName}</span>{" "}
            from the blacklist? This will make them visible in regular customer
            listings and searches again.
          </DialogDescription>
        </DialogHeader>

        <DialogFooter className="gap-2 sm:space-x-0">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={unblacklistMutation.isPending}
          >
            Cancel
          </Button>
          <Button
            type="button"
            onClick={handleUnblacklist}
            disabled={unblacklistMutation.isPending}
          >
            {unblacklistMutation.isPending ? "Removing..." : "Remove from Blacklist"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}