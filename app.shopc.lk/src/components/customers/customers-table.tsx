"use client";

import * as React from "react";
import type {
  DataTableAdvancedFilterField,
  DataTableFilterField,
  DataTableRowAction,
} from "@/types";
import { BaseTable } from "@/components/shared/base-table";
import { getCustomersTableData } from "@/lib/customers/queries";
import { DeleteCustomersDialog } from "./delete-customers-dialog";
import { BlacklistCustomerDialog } from "./blacklist-customer-dialog";
import { UnblacklistCustomerDialog } from "./unblacklist-customer-dialog";
import { CustomersTableToolbarActions } from "./customers-table-toolbar-actions";
import { CustomersTableFloatingBar } from "./customers-table-floating-bar";
import { CustomerSheet } from "./customer-sheet";
import { CustomerDetails } from "./customer-details";
import { CustomerDetailsContent } from "./customer-details-content";
import { useSearchParams } from "next/navigation";
import { getColumns } from "./customers-table-columns";
import { CustomerTableData, CustomerStatus } from "@/types/customer";
import { useCustomersData } from "@/lib/customers/hooks";
import { useQueryClient } from "@tanstack/react-query";
import { customerKeys } from "@/lib/customers/hooks";

interface CustomersTableProps {
  isDemo?: boolean;
}

export function CustomersTable({ isDemo = false }: CustomersTableProps) {
  const [rowAction, setRowAction] =
    React.useState<DataTableRowAction<CustomerTableData> | null>(null);
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();

  const filterFields: DataTableFilterField<CustomerTableData>[] = React.useMemo(
    () => [
      {
        id: "customerDisplayName",
        label: "Name",
        placeholder: "Filter by customer name...",
      },
      {
        id: "email",
        label: "Email",
        placeholder: "Filter by email...",
      },
      {
        id: "companyName",
        label: "Company",
        placeholder: "Filter by company...",
      },
      {
        id: "status",
        label: "Status",
        placeholder: "Filter by status...",
        type: "select",
        options: [
          { label: "Active", value: CustomerStatus.ACTIVE },
          { label: "Inactive", value: CustomerStatus.INACTIVE },
        ],
      },
    ],
    []
  );

  const advancedFilterFields: DataTableAdvancedFilterField<CustomerTableData>[] =
    React.useMemo(
      () => [
        {
          id: "customerDisplayName",
          label: "Name",
          type: "text",
        },
        {
          id: "email",
          label: "Email",
          type: "text",
        },
        {
          id: "companyName",
          label: "Company",
          type: "text",
        },
        {
          id: "phoneNumber",
          label: "Phone Number",
          type: "text",
        },
        {
          id: "mobileNumber",
          label: "Mobile Number",
          type: "text",
        },
        {
          id: "status",
          label: "Status",
          type: "select",
          options: [
            { label: "Active", value: CustomerStatus.ACTIVE },
            { label: "Inactive", value: CustomerStatus.INACTIVE },
          ],
        },
        {
          id: "isAllocatedToAllLocations",
          label: "All Locations",
          type: "select",
          options: [
            { label: "Yes", value: "true" },
            { label: "No", value: "false" },
          ],
        },
      ],
      []
    );

  const sortFields: DataTableAdvancedFilterField<CustomerTableData>[] =
    React.useMemo(
      () => [
        {
          id: "customerDisplayName",
          label: "Name",
          type: "text",
        },
        {
          id: "companyName",
          label: "Company",
          type: "text",
        },
        {
          id: "email",
          label: "Email",
          type: "text",
        },
        {
          id: "createdAt",
          label: "Created Date",
          type: "text",
        },
        {
          id: "updatedAt",
          label: "Updated Date",
          type: "text",
        },
        {
          id: "openingBalance",
          label: "Opening Balance",
          type: "number",
        },
      ],
      []
    );

  const handleRowAction = React.useCallback(
    (action: DataTableRowAction<CustomerTableData>) => {
      setRowAction(action);
    },
    []
  );

  const searchParamsValues = React.useMemo(() => {
    return {
      page: searchParams.get("page")
        ? parseInt(searchParams.get("page") as string)
        : 1,
      perPage: searchParams.get("perPage")
        ? parseInt(searchParams.get("perPage") as string)
        : 10,
      customerDisplayName: searchParams.get("customerDisplayName") || "",
      email: searchParams.get("email") || "",
      companyName: searchParams.get("companyName") || "",
      phoneNumber: searchParams.get("phoneNumber") || "",
      mobileNumber: searchParams.get("mobileNumber") || "",
      status: searchParams.get("status") || "",
      customerGroupId: searchParams.get("customerGroupId") || "",
      from: searchParams.get("from") || "",
      to: searchParams.get("to") || "",
      filters: (() => {
        const filtersParam = searchParams.get("filters");
        return filtersParam ? JSON.parse(filtersParam) : [];
      })(),
      joinOperator: (searchParams.get("joinOperator") as "and" | "or") || "and",
      sort: (() => {
        const sortParam = searchParams.get("sort");
        return sortParam ? JSON.parse(sortParam) : [];
      })(),
    };
  }, [searchParams]);

  // Use TanStack Query hook
  const {
    data: customersData,
    isLoading,
    isFetching,
    isRefetching,
  } = useCustomersData(searchParamsValues, isDemo);

  // Only show full loading indicator for initial loading
  // Show header loading for refetch operations
  const isHeaderLoading = isFetching && !isLoading;

  // Determine if actions should be disabled (during any loading/fetching state)
  const isActionsDisabled = isLoading || isFetching;

  // Check if there are active filters or search parameters
  const hasActiveFilters = React.useMemo(() => {
    return !!(
      searchParamsValues.customerDisplayName ||
      searchParamsValues.email ||
      searchParamsValues.companyName ||
      searchParamsValues.phoneNumber ||
      searchParamsValues.mobileNumber ||
      searchParamsValues.status ||
      searchParamsValues.customerGroupId ||
      searchParamsValues.from ||
      searchParamsValues.to ||
      (searchParamsValues.filters && searchParamsValues.filters.length > 0)
    );
  }, [searchParamsValues]);

  const handleRefresh = React.useCallback(
    async (customerIds?: string[]) => {
      const invalidatePromises = [
        queryClient.invalidateQueries({ queryKey: customerKeys.lists() }),
        queryClient.invalidateQueries({ queryKey: customerKeys.slim(isDemo) }),
      ];

      // If specific customer IDs are provided, invalidate their detail cache as well
      if (customerIds && customerIds.length > 0) {
        customerIds.forEach((customerId) => {
          invalidatePromises.push(
            queryClient.invalidateQueries({
              queryKey: customerKeys.detail(customerId),
            })
          );
        });
      }

      await Promise.all(invalidatePromises);
    },
    [queryClient, isDemo]
  );

  // Custom dialog content renderer for row clicks
  const renderCustomerDetails = React.useCallback(
    (customer: CustomerTableData) => {
      return <CustomerDetailsContent customer={customer} isDemo={isDemo} />;
    },
    [isDemo]
  );

  return (
    <>
      <BaseTable<
        CustomerTableData,
        Awaited<ReturnType<typeof getCustomersTableData>>
      >
        data={customersData || undefined}
        isLoading={isLoading}
        isHeaderLoading={isHeaderLoading}
        isDemo={isDemo}
        hasActiveFilters={hasActiveFilters}
        enableSortableRows={false}
        currentPage={searchParamsValues.page}
        itemsPerPage={searchParamsValues.perPage}
        isDragDropEnabled={() => false}
        columns={getColumns({
          setRowAction,
          isDemo,
          isActionsDisabled,
          onStatusUpdate: (customerId?: string) => {
            queryClient.invalidateQueries({ queryKey: customerKeys.lists() });
            queryClient.invalidateQueries({
              queryKey: customerKeys.slim(isDemo),
            });
            if (customerId) {
              queryClient.invalidateQueries({
                queryKey: customerKeys.detail(customerId),
              });
            }
          },
        })}
        filterFields={filterFields}
        advancedFilterFields={advancedFilterFields}
        sortFields={sortFields}
        getInitialData={(response) => {
          return response?.data?.data ?? [];
        }}
        getPageCount={(response) => {
          return response?.data?.meta?.totalPages ?? 1;
        }}
        getRowId={(row) => row.id}
        enableAdvancedTable={false}
        enableFloatingBar={true}
        FloatingBar={(props) => (
          <CustomersTableFloatingBar
            {...props}
            onRefresh={handleRefresh}
            isDemo={isDemo}
          />
        )}
        ToolbarActions={(props) => (
          <CustomersTableToolbarActions {...props} isDemo={isDemo} />
        )}
        onRowAction={handleRowAction}
        defaultSortingId={undefined}
        defaultSortingDesc={false}
        onRefresh={handleRefresh}
        renderDialogContent={renderCustomerDetails}
      />
      <CustomerDetails
        open={rowAction?.type === "view"}
        onOpenChange={() => setRowAction(null)}
        customer={rowAction?.row.original ?? null}
        isDemo={isDemo}
      />
      <CustomerSheet
        open={rowAction?.type === "update"}
        onOpenChange={() => setRowAction(null)}
        customer={rowAction?.row.original ?? null}
        isDemo={isDemo}
        onSuccess={() => {
          const customerId = rowAction?.row.original?.id;
          queryClient.invalidateQueries({ queryKey: customerKeys.lists() });
          queryClient.invalidateQueries({
            queryKey: customerKeys.slim(isDemo),
          });
          if (customerId) {
            queryClient.invalidateQueries({
              queryKey: customerKeys.detail(customerId),
            });
          }
        }}
        isUpdate={true}
      />
      <DeleteCustomersDialog
        open={rowAction?.type === "delete"}
        onOpenChange={() => setRowAction(null)}
        customers={rowAction?.row.original ? [rowAction.row.original] : []}
        showTrigger={false}
        onSuccess={() => {
          queryClient.invalidateQueries({ queryKey: customerKeys.lists() });
          queryClient.invalidateQueries({
            queryKey: customerKeys.slim(isDemo),
          });
        }}
        isDemo={isDemo}
      />
      <BlacklistCustomerDialog
        open={rowAction?.type === "blacklist"}
        onOpenChange={() => setRowAction(null)}
        customer={rowAction?.row.original ?? null}
        isDemo={isDemo}
        onSuccess={() => {
          const customerId = rowAction?.row.original?.id;
          queryClient.invalidateQueries({ queryKey: customerKeys.lists() });
          queryClient.invalidateQueries({
            queryKey: customerKeys.slim(isDemo),
          });
          if (customerId) {
            queryClient.invalidateQueries({
              queryKey: customerKeys.detail(customerId),
            });
          }
        }}
      />
      <UnblacklistCustomerDialog
        open={rowAction?.type === "unblacklist"}
        onOpenChange={() => setRowAction(null)}
        customer={rowAction?.row.original ?? null}
        isDemo={isDemo}
        onSuccess={() => {
          const customerId = rowAction?.row.original?.id;
          queryClient.invalidateQueries({ queryKey: customerKeys.lists() });
          queryClient.invalidateQueries({
            queryKey: customerKeys.slim(isDemo),
          });
          if (customerId) {
            queryClient.invalidateQueries({
              queryKey: customerKeys.detail(customerId),
            });
          }
        }}
      />
    </>
  );
}
