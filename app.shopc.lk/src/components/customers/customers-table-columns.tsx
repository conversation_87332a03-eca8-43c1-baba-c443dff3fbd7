"use client";

import * as React from "react";
import type { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Eye, User, Building2, Ban, UserX } from "lucide-react";
import { CustomerTableData, CustomerStatus } from "@/types/customer";
import { CustomerStatusBadge } from "./customer-status-badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";

export interface ColumnsProps {
  setRowAction: (rowAction: {
    type: "view" | "update" | "delete" | "blacklist" | "unblacklist";
    row: any;
  }) => void;
  isDemo?: boolean;
  isActionsDisabled?: boolean;
  onRefresh?: () => void;
  onStatusUpdate?: (customerId: string, newStatus: CustomerStatus) => void;
}

export function getColumns({
  setRowAction,
  isDemo = false,
  isActionsDisabled = false,
  onRefresh,
  onStatusUpdate,
}: ColumnsProps) {
  const columns: ColumnDef<CustomerTableData>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
          className="translate-y-[2px]"
          disabled={isActionsDisabled}
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          className="translate-y-[2px]"
          disabled={isActionsDisabled}
        />
      ),
      enableSorting: false,
      enableHiding: false,
      size: 10, // Set a fixed small width for the checkbox column
    },
    {
      accessorKey: "customerDisplayName",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Customer Name" />
      ),
      cell: ({ row }) => {
        const customerName = row.getValue("customerDisplayName") as string;
        const companyName = row.original.companyName;
        const isSubCustomer = row.original.isSubCustomer;

        // Create initials from customer name
        const initials = customerName
          .split(" ")
          .map((name) => name.charAt(0))
          .join("")
          .toUpperCase()
          .slice(0, 2);

        return (
          <div className="flex items-center gap-3 pl-2">
            <div className="flex flex-col">
              <div className="flex items-center gap-2">
                <span className="font-medium">{customerName}</span>
                {isSubCustomer && (
                  <Badge variant="secondary" className="text-xs">
                    Sub
                  </Badge>
                )}
              </div>
              {companyName && (
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Building2 className="h-3 w-3" />
                  {companyName}
                </div>
              )}
            </div>
          </div>
        );
      },
      enableSorting: true,
      enableHiding: false,
    },

    {
      accessorKey: "email",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Email" />
      ),
      cell: ({ row }) => {
        const email = row.getValue("email") as string;
        return (
          <div className="pl-2">
            {email ? (
              <span className="text-sm">{email}</span>
            ) : (
              <span className="text-xs text-muted-foreground">No email</span>
            )}
          </div>
        );
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      accessorKey: "phoneNumber",
      header: () => <div className="font-medium">Phone</div>,
      cell: ({ row }) => {
        const phoneNumber = row.getValue("phoneNumber") as string;
        const mobileNumber = row.original.mobileNumber;

        return (
          <div className="pl-2">
            {phoneNumber || mobileNumber ? (
              <div className="flex flex-col gap-1">
                {phoneNumber && <span className="text-sm">{phoneNumber}</span>}
                {mobileNumber && phoneNumber !== mobileNumber && (
                  <span className="text-xs text-muted-foreground">
                    Mobile: {mobileNumber}
                  </span>
                )}
              </div>
            ) : (
              <span className="text-xs text-muted-foreground">No phone</span>
            )}
          </div>
        );
      },
      enableSorting: false,
      enableHiding: true,
    },
    {
      accessorKey: "customerGroupName",
      header: () => <div className="font-medium">Customer Group</div>,
      cell: ({ row }) => {
        const customerGroupName = row.getValue("customerGroupName") as string;
        return (
          <div className="pl-2">
            {customerGroupName ? (
              <Badge variant="outline" className="text-xs">
                {customerGroupName}
              </Badge>
            ) : (
              <span className="text-xs text-muted-foreground">No group</span>
            )}
          </div>
        );
      },
      enableSorting: false,
      enableHiding: true,
    },
    {
      accessorKey: "openingBalance",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Opening Balance" />
      ),
      cell: ({ row }) => {
        const openingBalance = row.getValue("openingBalance") as string;
        return (
          <div className="pl-2">
            {openingBalance ? (
              <span className="text-sm font-medium">
                ${parseFloat(openingBalance).toFixed(2)}
              </span>
            ) : (
              <span className="text-xs text-muted-foreground">$0.00</span>
            )}
          </div>
        );
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      accessorKey: "status",
      header: () => <div className="font-medium">Status</div>,
      cell: ({ row }) => {
        const status = row.getValue("status") as CustomerStatus;
        return (
          <CustomerStatusBadge
            customerId={row.original.id}
            status={status}
            isDemo={isDemo}
            disabled={isActionsDisabled}
            onRefresh={onRefresh}
            onStatusUpdate={onStatusUpdate}
          />
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "isAllocatedToAllLocations",
      header: () => <div className="font-medium">All Locations</div>,
      cell: ({ row }) => {
        const isAllocatedToAllLocations =
          row.original.isAllocatedToAllLocations;
        return (
          <div className="pl-2">
            <Badge
              variant={isAllocatedToAllLocations ? "default" : "secondary"}
              className="text-xs"
            >
              {isAllocatedToAllLocations ? "Yes" : "No"}
            </Badge>
          </div>
        );
      },
      enableSorting: false,
      enableHiding: true,
    },
    {
      accessorKey: "isBlacklisted",
      header: () => <div className="font-medium">Blacklist Status</div>,
      cell: ({ row }) => {
        const isBlacklisted = row.original.isBlacklisted;
        return (
          <div className="pl-2">
            <Badge
              variant={isBlacklisted ? "destructive" : "secondary"}
              className="text-xs"
            >
              {isBlacklisted ? (
                <>
                  <Ban className="mr-1 h-3 w-3" />
                  Blacklisted
                </>
              ) : (
                "Active"
              )}
            </Badge>
          </div>
        );
      },
      enableSorting: false,
      enableHiding: true,
    },

    {
      id: "actions",
      header: () => <div className="font-medium">Actions</div>,
      cell: ({ row }) => {
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="h-8 w-8 p-0 data-[state=open]:bg-muted"
                disabled={isActionsDisabled}
              >
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[160px]">
              <DropdownMenuItem
                onClick={() =>
                  setRowAction({
                    type: "view",
                    row,
                  })
                }
                disabled={isActionsDisabled}
              >
                <Eye className="mr-2 h-4 w-4" />
                View Details
                <DropdownMenuShortcut>⌘V</DropdownMenuShortcut>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  setRowAction({
                    type: "update",
                    row,
                  })
                }
                disabled={isActionsDisabled}
              >
                Edit
                <DropdownMenuShortcut>⌘E</DropdownMenuShortcut>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  setRowAction({
                    type: "delete",
                    row,
                  })
                }
                disabled={isActionsDisabled}
              >
                Delete
                <DropdownMenuShortcut>⌘⌫</DropdownMenuShortcut>
              </DropdownMenuItem>
              
              {row.original.isBlacklisted ? (
                <DropdownMenuItem
                  onClick={() =>
                    setRowAction({
                      type: "unblacklist",
                      row,
                    })
                  }
                  disabled={isActionsDisabled}
                >
                  <UserX className="mr-2 h-4 w-4" />
                  Remove from Blacklist
                </DropdownMenuItem>
              ) : (
                <DropdownMenuItem
                  onClick={() =>
                    setRowAction({
                      type: "blacklist",
                      row,
                    })
                  }
                  disabled={isActionsDisabled}
                  className="text-destructive focus:text-destructive"
                >
                  <Ban className="mr-2 h-4 w-4" />
                  Blacklist Customer
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return columns;
}
