"use client";

import * as React from "react";
import type {
  DataTableAdvancedFilterField,
  DataTableFilterField,
  DataTableRowAction,
} from "@/types";
import { BaseTable } from "@/components/shared/base-table";
import { UnblacklistCustomerDialog } from "./unblacklist-customer-dialog";
import { CustomerDetails } from "./customer-details";
import { CustomerDetailsContent } from "./customer-details-content";
import { useSearchParams } from "next/navigation";
import { getBlacklistedCustomersColumns } from "./blacklisted-customers-table-columns";
import { CustomerTableData } from "@/types/customer";
import { useBlacklistedCustomersData } from "@/lib/customers/hooks";
import { useQueryClient } from "@tanstack/react-query";
import { customerKeys } from "@/lib/customers/hooks";

interface BlacklistedCustomersTableProps {
  isDemo?: boolean;
}

export function BlacklistedCustomersTable({ isDemo = false }: BlacklistedCustomersTableProps) {
  const [rowAction, setRowAction] =
    React.useState<DataTableRowAction<CustomerTableData> | null>(null);
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();

  const filterFields: DataTableFilterField<CustomerTableData>[] = React.useMemo(
    () => [
      {
        id: "customerDisplayName",
        label: "Name",
        placeholder: "Filter by customer name...",
      },
      {
        id: "email",
        label: "Email",
        placeholder: "Filter by email...",
      },
      {
        id: "companyName",
        label: "Company",
        placeholder: "Filter by company...",
      },
    ],
    []
  );

  const advancedFilterFields: DataTableAdvancedFilterField<CustomerTableData>[] =
    React.useMemo(
      () => [
        {
          id: "customerDisplayName",
          label: "Name",
          type: "text",
        },
        {
          id: "email",
          label: "Email",
          type: "text",
        },
        {
          id: "companyName",
          label: "Company",
          type: "text",
        },
      ],
      []
    );

  const sortFields: DataTableAdvancedFilterField<CustomerTableData>[] =
    React.useMemo(
      () => [
        {
          id: "customerDisplayName",
          label: "Customer Name",
          type: "text",
        },
        {
          id: "companyName",
          label: "Company Name",
          type: "text",
        },
        {
          id: "email",
          label: "Email",
          type: "text",
        },
        {
          id: "blacklistDate",
          label: "Blacklist Date",
          type: "text",
        },
      ],
      []
    );

  const handleRowAction = React.useCallback(
    (action: DataTableRowAction<CustomerTableData>) => {
      setRowAction(action);
    },
    []
  );

  const searchParamsValues = React.useMemo(() => {
    return {
      page: searchParams.get("page")
        ? parseInt(searchParams.get("page") as string)
        : 1,
      perPage: searchParams.get("perPage")
        ? parseInt(searchParams.get("perPage") as string)
        : 10,
      customerDisplayName: searchParams.get("customerDisplayName") || "",
      email: searchParams.get("email") || "",
      companyName: searchParams.get("companyName") || "",
      from: searchParams.get("from") || "",
      to: searchParams.get("to") || "",
      sort: (() => {
        const sortParam = searchParams.get("sort");
        return sortParam ? JSON.parse(sortParam) : [];
      })(),
    };
  }, [searchParams]);

  // Use TanStack Query hook for blacklisted customers
  const {
    data: blacklistedCustomersData,
    isLoading,
    isFetching,
  } = useBlacklistedCustomersData(searchParamsValues, isDemo);

  // Only show full loading indicator for initial loading
  // Show header loading for refetch operations
  const isHeaderLoading = isFetching && !isLoading;

  // Determine if actions should be disabled (during any loading/fetching state)
  const isActionsDisabled = isLoading || isFetching;

  // Check if there are active filters or search parameters
  const hasActiveFilters = React.useMemo(() => {
    return !!(
      searchParamsValues.customerDisplayName ||
      searchParamsValues.email ||
      searchParamsValues.companyName ||
      searchParamsValues.from ||
      searchParamsValues.to
    );
  }, [searchParamsValues]);

  const handleRefresh = React.useCallback(
    async (customerIds?: string[]) => {
      const invalidatePromises = [
        queryClient.invalidateQueries({ queryKey: customerKeys.blacklisted() }),
        queryClient.invalidateQueries({ queryKey: customerKeys.lists() }),
        queryClient.invalidateQueries({ queryKey: customerKeys.slim(isDemo) }),
      ];

      // If specific customer IDs are provided, invalidate their detail cache as well
      if (customerIds && customerIds.length > 0) {
        customerIds.forEach((customerId) => {
          invalidatePromises.push(
            queryClient.invalidateQueries({
              queryKey: customerKeys.detail(customerId),
            })
          );
        });
      }

      await Promise.all(invalidatePromises);
    },
    [queryClient, isDemo]
  );

  // Custom dialog content renderer for row clicks
  const renderCustomerDetails = React.useCallback(
    (customer: CustomerTableData) => {
      return <CustomerDetailsContent customer={customer} isDemo={isDemo} />;
    },
    [isDemo]
  );

  return (
    <>
      <BaseTable<CustomerTableData, any>
        data={blacklistedCustomersData || undefined}
        isLoading={isLoading}
        isHeaderLoading={isHeaderLoading}
        isDemo={isDemo}
        hasActiveFilters={hasActiveFilters}
        enableSortableRows={false}
        currentPage={searchParamsValues.page}
        itemsPerPage={searchParamsValues.perPage}
        isDragDropEnabled={() => false}
        columns={getBlacklistedCustomersColumns({
          setRowAction,
          isDemo,
          isActionsDisabled,
        })}
        filterFields={filterFields}
        advancedFilterFields={advancedFilterFields}
        sortFields={sortFields}
        getInitialData={(response) => {
          return response?.data?.data ?? [];
        }}
        getPageCount={(response) => {
          return response?.data?.meta?.totalPages ?? 1;
        }}
        getRowId={(row) => row.id}
        enableAdvancedTable={false}
        enableFloatingBar={false}
        onRowAction={handleRowAction}
        defaultSortingId={undefined}
        defaultSortingDesc={false}
        onRefresh={handleRefresh}
        renderDialogContent={renderCustomerDetails}
      />
      <CustomerDetails
        open={rowAction?.type === "view"}
        onOpenChange={() => setRowAction(null)}
        customer={rowAction?.row.original ?? null}
        isDemo={isDemo}
      />
      <UnblacklistCustomerDialog
        open={rowAction?.type === "unblacklist"}
        onOpenChange={() => setRowAction(null)}
        customer={rowAction?.row.original ?? null}
        isDemo={isDemo}
        onSuccess={() => {
          const customerId = rowAction?.row.original?.id;
          queryClient.invalidateQueries({ queryKey: customerKeys.blacklisted() });
          queryClient.invalidateQueries({ queryKey: customerKeys.lists() });
          queryClient.invalidateQueries({
            queryKey: customerKeys.slim(isDemo),
          });
          if (customerId) {
            queryClient.invalidateQueries({
              queryKey: customerKeys.detail(customerId),
            });
          }
        }}
      />
    </>
  );
}