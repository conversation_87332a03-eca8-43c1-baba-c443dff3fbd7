"use client";

import * as React from "react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  User,
  Building2,
  Mail,
  Phone,
  Globe,
  MapPin,
  CreditCard,
  FileText,
  Calendar,
  Users,
  Ban,
} from "lucide-react";
import { CustomerTableData, CustomerStatus } from "@/types/customer";

interface CustomerDetailsContentProps {
  customer: CustomerTableData;
  isDemo?: boolean;
}

export function CustomerDetailsContent({
  customer,
  isDemo = false,
}: CustomerDetailsContentProps) {
  // Create initials from customer name
  const initials = customer.customerDisplayName
    .split(" ")
    .map((name) => name.charAt(0))
    .join("")
    .toUpperCase()
    .slice(0, 2);

  const statusConfig = {
    [CustomerStatus.ACTIVE]: {
      label: "Active",
      className: "bg-green-100 text-green-800",
    },
    [CustomerStatus.INACTIVE]: {
      label: "Inactive",
      className: "bg-gray-100 text-gray-800",
    },
  };

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="flex items-start gap-4">
        <Avatar className="h-16 w-16">
          <AvatarFallback className="text-lg bg-blue-100 text-blue-700">
            {initials}
          </AvatarFallback>
        </Avatar>
        <div className="flex-1 space-y-2">
          <div className="flex items-center gap-3">
            <h2 className="text-2xl font-bold">{customer.customerDisplayName}</h2>
            <Badge className={statusConfig[customer.status].className}>
              {statusConfig[customer.status].label}
            </Badge>
            {customer.isSubCustomer && (
              <Badge variant="secondary">Sub Customer</Badge>
            )}
            {customer.isBlacklisted && (
              <Badge variant="destructive" className="flex items-center gap-1">
                <Ban className="h-3 w-3" />
                Blacklisted
              </Badge>
            )}
          </div>
          {customer.companyName && (
            <div className="flex items-center gap-2 text-muted-foreground">
              <Building2 className="h-4 w-4" />
              <span>{customer.companyName}</span>
            </div>
          )}
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              <span>Created {new Date(customer.createdAt).toLocaleDateString()}</span>
            </div>
            {customer.customerGroupName && (
              <div className="flex items-center gap-1">
                <Users className="h-4 w-4" />
                <span>{customer.customerGroupName}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      <Separator />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Personal Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Personal Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {customer.title && (
              <div>
                <span className="text-sm font-medium text-muted-foreground">Title:</span>
                <p className="text-sm">{customer.title}</p>
              </div>
            )}
            {customer.firstName && (
              <div>
                <span className="text-sm font-medium text-muted-foreground">First Name:</span>
                <p className="text-sm">{customer.firstName}</p>
              </div>
            )}
            {customer.middleName && (
              <div>
                <span className="text-sm font-medium text-muted-foreground">Middle Name:</span>
                <p className="text-sm">{customer.middleName}</p>
              </div>
            )}
            {customer.lastName && (
              <div>
                <span className="text-sm font-medium text-muted-foreground">Last Name:</span>
                <p className="text-sm">{customer.lastName}</p>
              </div>
            )}
            {customer.suffix && (
              <div>
                <span className="text-sm font-medium text-muted-foreground">Suffix:</span>
                <p className="text-sm">{customer.suffix}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Phone className="h-5 w-5" />
              Contact Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {customer.email && (
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{customer.email}</span>
              </div>
            )}
            {customer.phoneNumber && (
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{customer.phoneNumber}</span>
              </div>
            )}
            {customer.mobileNumber && (
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{customer.mobileNumber} (Mobile)</span>
              </div>
            )}
            {customer.fax && (
              <div>
                <span className="text-sm font-medium text-muted-foreground">Fax:</span>
                <p className="text-sm">{customer.fax}</p>
              </div>
            )}
            {customer.website && (
              <div className="flex items-center gap-2">
                <Globe className="h-4 w-4 text-muted-foreground" />
                <a
                  href={customer.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm text-blue-600 hover:underline"
                >
                  {customer.website}
                </a>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Billing Address */}
        {customer.billingAddress && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Billing Address
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-sm space-y-1">
                <p>{customer.billingAddress.street}</p>
                <p>
                  {customer.billingAddress.city}, {customer.billingAddress.state}{" "}
                  {customer.billingAddress.zipCode}
                </p>
                <p>{customer.billingAddress.country}</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Shipping Address */}
        {customer.shippingAddress && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Shipping Address
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-sm space-y-1">
                <p>{customer.shippingAddress.street}</p>
                <p>
                  {customer.shippingAddress.city}, {customer.shippingAddress.state}{" "}
                  {customer.shippingAddress.zipCode}
                </p>
                <p>{customer.shippingAddress.country}</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Financial Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Financial Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {customer.openingBalance && (
              <div>
                <span className="text-sm font-medium text-muted-foreground">Opening Balance:</span>
                <p className="text-sm font-medium">
                  ${parseFloat(customer.openingBalance).toFixed(2)}
                </p>
              </div>
            )}
            {customer.openingBalanceAsOf && (
              <div>
                <span className="text-sm font-medium text-muted-foreground">As Of:</span>
                <p className="text-sm">
                  {new Date(customer.openingBalanceAsOf).toLocaleDateString()}
                </p>
              </div>
            )}
            {customer.salesTaxRegistration && (
              <div>
                <span className="text-sm font-medium text-muted-foreground">Tax Registration:</span>
                <p className="text-sm">{customer.salesTaxRegistration}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Additional Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Additional Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <span className="text-sm font-medium text-muted-foreground">All Locations:</span>
              <p className="text-sm">
                {customer.isAllocatedToAllLocations ? "Yes" : "No"}
              </p>
            </div>
            {customer.other && (
              <div>
                <span className="text-sm font-medium text-muted-foreground">Other:</span>
                <p className="text-sm">{customer.other}</p>
              </div>
            )}
            {customer.notes && (
              <div>
                <span className="text-sm font-medium text-muted-foreground">Notes:</span>
                <p className="text-sm">{customer.notes}</p>
              </div>
            )}
            {customer.locations && customer.locations.length > 0 && (
              <div>
                <span className="text-sm font-medium text-muted-foreground">Locations:</span>
                <div className="flex flex-wrap gap-1 mt-1">
                  {customer.locations.map((location) => (
                    <Badge key={location.id} variant="outline" className="text-xs">
                      {location.name}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Blacklist Information */}
        {customer.isBlacklisted && (
          <Card className="md:col-span-2 border-destructive">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-destructive">
                <Ban className="h-5 w-5" />
                Blacklist Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {customer.blacklistReason && (
                <div>
                  <span className="text-sm font-medium text-muted-foreground">Reason:</span>
                  <p className="text-sm text-destructive font-medium">{customer.blacklistReason}</p>
                </div>
              )}
              {customer.blacklistDate && (
                <div>
                  <span className="text-sm font-medium text-muted-foreground">Blacklisted On:</span>
                  <p className="text-sm">{new Date(customer.blacklistDate).toLocaleDateString()}</p>
                </div>
              )}
              <div className="mt-4 p-3 bg-destructive/10 rounded-md">
                <p className="text-sm text-destructive">
                  <strong>Note:</strong> This customer is currently blacklisted and will not appear in regular customer searches or selections.
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
