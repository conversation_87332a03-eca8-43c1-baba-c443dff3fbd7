"use client";

import * as React from "react";
import type { ColumnDef } from "@tanstack/react-table";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Eye, Building2, UserX, Calendar } from "lucide-react";
import { CustomerTableData } from "@/types/customer";
import { Badge } from "@/components/ui/badge";
import { formatDate } from "@/lib/utils";

export interface BlacklistedColumnsProps {
  setRowAction: (rowAction: {
    type: "view" | "unblacklist";
    row: any;
  }) => void;
  isDemo?: boolean;
  isActionsDisabled?: boolean;
}

export function getBlacklistedCustomersColumns({
  setRowAction,
  isDemo = false,
  isActionsDisabled = false,
}: BlacklistedColumnsProps) {
  const columns: ColumnDef<CustomerTableData>[] = [
    {
      accessorKey: "customerDisplayName",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Customer Name" />
      ),
      cell: ({ row }) => {
        const customerName = row.getValue("customerDisplayName") as string;
        const companyName = row.original.companyName;
        const isSubCustomer = row.original.isSubCustomer;

        return (
          <div className="flex items-center gap-3 pl-2">
            <div className="flex flex-col">
              <div className="flex items-center gap-2">
                <span className="font-medium">{customerName}</span>
                {isSubCustomer && (
                  <Badge variant="secondary" className="text-xs">
                    Sub
                  </Badge>
                )}
              </div>
              {companyName && (
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Building2 className="h-3 w-3" />
                  {companyName}
                </div>
              )}
            </div>
          </div>
        );
      },
      enableSorting: true,
      enableHiding: false,
    },
    {
      accessorKey: "email",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Email" />
      ),
      cell: ({ row }) => {
        const email = row.getValue("email") as string;
        return (
          <div className="pl-2">
            {email ? (
              <span className="text-sm">{email}</span>
            ) : (
              <span className="text-xs text-muted-foreground">No email</span>
            )}
          </div>
        );
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      accessorKey: "phoneNumber",
      header: () => <div className="font-medium">Phone</div>,
      cell: ({ row }) => {
        const phoneNumber = row.original.phoneNumber;
        const mobileNumber = row.original.mobileNumber;
        
        return (
          <div className="pl-2">
            {phoneNumber || mobileNumber ? (
              <div className="flex flex-col gap-1">
                {phoneNumber && (
                  <span className="text-sm">{phoneNumber}</span>
                )}
                {mobileNumber && (
                  <span className="text-xs text-muted-foreground">{mobileNumber}</span>
                )}
              </div>
            ) : (
              <span className="text-xs text-muted-foreground">No phone</span>
            )}
          </div>
        );
      },
      enableSorting: false,
      enableHiding: true,
    },
    {
      accessorKey: "blacklistReason",
      header: () => <div className="font-medium">Blacklist Reason</div>,
      cell: ({ row }) => {
        const reason = row.original.blacklistReason;
        return (
          <div className="pl-2 max-w-xs">
            {reason ? (
              <div className="text-sm text-destructive truncate" title={reason}>
                {reason}
              </div>
            ) : (
              <span className="text-xs text-muted-foreground">No reason provided</span>
            )}
          </div>
        );
      },
      enableSorting: false,
      enableHiding: true,
    },
    {
      accessorKey: "blacklistDate",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Blacklisted Date" />
      ),
      cell: ({ row }) => {
        const blacklistDate = row.original.blacklistDate;
        return (
          <div className="pl-2">
            {blacklistDate ? (
              <div className="flex items-center gap-1 text-sm">
                <Calendar className="h-3 w-3" />
                {formatDate(blacklistDate)}
              </div>
            ) : (
              <span className="text-xs text-muted-foreground">Unknown</span>
            )}
          </div>
        );
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      id: "actions",
      header: () => <div className="font-medium">Actions</div>,
      cell: ({ row }) => {
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="h-8 w-8 p-0 data-[state=open]:bg-muted"
                disabled={isActionsDisabled}
              >
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[160px]">
              <DropdownMenuItem
                onClick={() =>
                  setRowAction({
                    type: "view",
                    row,
                  })
                }
                disabled={isActionsDisabled}
              >
                <Eye className="mr-2 h-4 w-4" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  setRowAction({
                    type: "unblacklist",
                    row,
                  })
                }
                disabled={isActionsDisabled}
                className="text-green-600 focus:text-green-600"
              >
                <UserX className="mr-2 h-4 w-4" />
                Remove from Blacklist
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return columns;
}