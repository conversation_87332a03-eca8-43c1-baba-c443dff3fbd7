"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";
import { useMutation, useQueryClient } from "@tanstack/react-query";

import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { blacklistCustomer } from "@/lib/customers";
import { customerKeys } from "@/lib/customers/hooks";
import { CustomerTableData } from "@/types/customer";
import { ApiStatus } from "@/types/common";

const blacklistSchema = z.object({
  reason: z
    .string()
    .min(1, "Reason is required")
    .max(500, "Reason must not exceed 500 characters"),
});

type BlacklistFormValues = z.infer<typeof blacklistSchema>;

interface BlacklistCustomerDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  customer: CustomerTableData | null;
  isDemo?: boolean;
  onSuccess?: () => void;
}

export function BlacklistCustomerDialog({
  open,
  onOpenChange,
  customer,
  isDemo = false,
  onSuccess,
}: BlacklistCustomerDialogProps) {
  const queryClient = useQueryClient();

  const form = useForm<BlacklistFormValues>({
    resolver: zodResolver(blacklistSchema),
    defaultValues: {
      reason: "",
    },
  });

  const blacklistMutation = useMutation({
    mutationFn: async (data: BlacklistFormValues) => {
      if (!customer) throw new Error("No customer selected");
      return blacklistCustomer(customer.id, { reason: data.reason }, isDemo);
    },
    onSuccess: (response) => {
      if (response.status === ApiStatus.SUCCESS) {
        toast.success("Customer blacklisted successfully");
        queryClient.invalidateQueries({ queryKey: customerKeys.all });
        onOpenChange(false);
        form.reset();
        onSuccess?.();
      } else {
        toast.error(response.message || "Failed to blacklist customer");
      }
    },
    onError: (error: any) => {
      toast.error(error?.message || "Failed to blacklist customer");
    },
  });

  const onSubmit = (data: BlacklistFormValues) => {
    blacklistMutation.mutate(data);
  };

  const handleClose = () => {
    onOpenChange(false);
    form.reset();
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Blacklist Customer</DialogTitle>
          <DialogDescription>
            Are you sure you want to blacklist{" "}
            <span className="font-semibold">{customer?.customerDisplayName}</span>?
            This will exclude them from regular customer listings and searches.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="reason"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Reason for blacklisting</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter the reason for blacklisting this customer..."
                      className="min-h-[100px] resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className="gap-2 sm:space-x-0">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={blacklistMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="destructive"
                disabled={blacklistMutation.isPending}
              >
                {blacklistMutation.isPending ? "Blacklisting..." : "Blacklist Customer"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}