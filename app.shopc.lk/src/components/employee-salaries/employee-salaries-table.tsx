"use client";

import * as React from "react";
import type {
  DataTableAdvancedFilterField,
  DataTableFilterField,
  DataTableRowAction,
} from "@/types";
import { BaseTable } from "@/components/shared/base-table";
import { getEmployeeSalariesTableData } from "@/lib/employee-salaries/queries";
import { DeleteEmployeeSalariesDialog } from "./delete-employee-salaries-dialog";
import { EmployeeSalariesTableToolbarActions } from "./employee-salaries-table-toolbar-actions";
import { EmployeeSalariesTableFloatingBar } from "./employee-salaries-table-floating-bar";
import { EmployeeSalarySheet } from "./employee-salary-sheet";
import { EmployeeSalaryDetails } from "./employee-salary-details";
import { EmployeeSalaryDetailsContent } from "./employee-salary-details-content";
import { useSearchParams } from "next/navigation";
import { getColumns } from "./employee-salaries-table-columns";
import {
  EmployeeSalaryTableData,
  EmployeeSalaryStatus,
} from "@/types/employee-salary";
import { useEmployeeSalariesData } from "@/lib/employee-salaries/hooks";
import { useQueryClient } from "@tanstack/react-query";
import { employeeSalaryKeys } from "@/lib/employee-salaries/hooks";

interface EmployeeSalariesTableProps {
  isDemo?: boolean;
}

export function EmployeeSalariesTable({
  isDemo = false,
}: EmployeeSalariesTableProps) {
  const [rowAction, setRowAction] =
    React.useState<DataTableRowAction<EmployeeSalaryTableData> | null>(null);
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();

  const filterFields: DataTableFilterField<EmployeeSalaryTableData>[] =
    React.useMemo(
      () => [
        {
          id: "employeeDisplayName",
          label: "Employee Name",
          placeholder: "Filter by employee name...",
        },
        {
          id: "status",
          label: "Status",
          placeholder: "Filter by status...",
          type: "select",
          options: [
            { label: "Active", value: EmployeeSalaryStatus.ACTIVE },
            { label: "Inactive", value: EmployeeSalaryStatus.INACTIVE },
            { label: "Pending", value: EmployeeSalaryStatus.PENDING },
            { label: "Suspended", value: EmployeeSalaryStatus.SUSPENDED },
          ],
        },
      ],
      []
    );

  const advancedFilterFields: DataTableAdvancedFilterField<EmployeeSalaryTableData>[] =
    React.useMemo(
      () => [
        {
          id: "employeeDisplayName",
          label: "Employee Name",
          type: "text",
        },
        {
          id: "employeeEmail",
          label: "Employee Email",
          type: "text",
        },
        {
          id: "bankAccountHolderName",
          label: "Account Holder",
          type: "text",
        },
        {
          id: "bankName",
          label: "Bank Name",
          type: "text",
        },
        {
          id: "status",
          label: "Status",
          type: "select",
          options: [
            { label: "Active", value: EmployeeSalaryStatus.ACTIVE },
            { label: "Inactive", value: EmployeeSalaryStatus.INACTIVE },
            { label: "Pending", value: EmployeeSalaryStatus.PENDING },
            { label: "Suspended", value: EmployeeSalaryStatus.SUSPENDED },
          ],
        },
      ],
      []
    );

  const sortFields: DataTableAdvancedFilterField<EmployeeSalaryTableData>[] =
    React.useMemo(
      () => [
        {
          id: "employeeDisplayName",
          label: "Employee Name",
          type: "text",
        },
        {
          id: "basicSalary",
          label: "Basic Salary",
          type: "number",
        },
        {
          id: "createdAt",
          label: "Created Date",
          type: "text",
        },
        {
          id: "updatedAt",
          label: "Updated Date",
          type: "text",
        },
      ],
      []
    );

  const handleRowAction = React.useCallback(
    (action: DataTableRowAction<EmployeeSalaryTableData>) => {
      setRowAction(action);
    },
    []
  );

  const searchParamsValues = React.useMemo(() => {
    return {
      page: searchParams.get("page")
        ? parseInt(searchParams.get("page") as string)
        : 1,
      perPage: searchParams.get("perPage")
        ? parseInt(searchParams.get("perPage") as string)
        : 10,
      employeeDisplayName: searchParams.get("employeeDisplayName") || "",
      employeeEmail: searchParams.get("employeeEmail") || "",
      bankAccountHolderName: searchParams.get("bankAccountHolderName") || "",
      bankName: searchParams.get("bankName") || "",
      status: searchParams.get("status") || "",
      from: searchParams.get("from") || "",
      to: searchParams.get("to") || "",
      filters: (() => {
        const filtersParam = searchParams.get("filters");
        return filtersParam ? JSON.parse(filtersParam) : [];
      })(),
      joinOperator: (searchParams.get("joinOperator") as "and" | "or") || "and",
      sort: (() => {
        const sortParam = searchParams.get("sort");
        return sortParam ? JSON.parse(sortParam) : [];
      })(),
    };
  }, [searchParams]);

  // Use TanStack Query hook
  const {
    data: employeeSalariesData,
    isLoading,
    isFetching,
    isRefetching,
  } = useEmployeeSalariesData(searchParamsValues, isDemo);

  // Only show full loading indicator for initial loading
  // Show header loading for refetch operations
  const isHeaderLoading = isFetching && !isLoading;

  // Determine if actions should be disabled (during any loading/fetching state)
  const isActionsDisabled = isLoading || isFetching;

  // Check if there are active filters or search parameters
  const hasActiveFilters = React.useMemo(() => {
    return !!(
      searchParamsValues.employeeDisplayName ||
      searchParamsValues.employeeEmail ||
      searchParamsValues.bankAccountHolderName ||
      searchParamsValues.bankName ||
      searchParamsValues.status ||
      searchParamsValues.from ||
      searchParamsValues.to ||
      (searchParamsValues.filters && searchParamsValues.filters.length > 0)
    );
  }, [searchParamsValues]);

  const handleRefresh = React.useCallback(
    async (employeeSalaryIds?: string[]) => {
      const invalidatePromises = [
        queryClient.invalidateQueries({ queryKey: employeeSalaryKeys.list() }),
        queryClient.invalidateQueries({
          queryKey: employeeSalaryKeys.simple(),
        }),
        queryClient.invalidateQueries({
          queryKey: employeeSalaryKeys.withDetails(),
        }),
      ];

      // If specific employee salary IDs are provided, invalidate their detail cache as well
      if (employeeSalaryIds && employeeSalaryIds.length > 0) {
        employeeSalaryIds.forEach((employeeSalaryId) => {
          invalidatePromises.push(
            queryClient.invalidateQueries({
              queryKey: employeeSalaryKeys.detail(employeeSalaryId),
            })
          );
        });
      }

      await Promise.all(invalidatePromises);
    },
    [queryClient]
  );

  // Custom dialog content renderer for row clicks
  const renderEmployeeSalaryDetails = React.useCallback(
    (employeeSalary: EmployeeSalaryTableData) => {
      return (
        <EmployeeSalaryDetailsContent
          employeeSalary={employeeSalary}
          isDemo={isDemo}
        />
      );
    },
    [isDemo]
  );

  return (
    <>
      <BaseTable<
        EmployeeSalaryTableData,
        Awaited<ReturnType<typeof getEmployeeSalariesTableData>>
      >
        data={employeeSalariesData || undefined}
        isLoading={isLoading}
        isHeaderLoading={isHeaderLoading}
        isDemo={isDemo}
        hasActiveFilters={hasActiveFilters}
        enableSortableRows={false}
        currentPage={searchParamsValues.page}
        itemsPerPage={searchParamsValues.perPage}
        isDragDropEnabled={() => false}
        columns={getColumns({
          setRowAction,
          isDemo,
          isActionsDisabled,
          onStatusUpdate: (employeeSalaryId?: string) => {
            queryClient.invalidateQueries({
              queryKey: employeeSalaryKeys.list(),
            });
            queryClient.invalidateQueries({
              queryKey: employeeSalaryKeys.simple(),
            });
            queryClient.invalidateQueries({
              queryKey: employeeSalaryKeys.withDetails(),
            });
            if (employeeSalaryId) {
              queryClient.invalidateQueries({
                queryKey: employeeSalaryKeys.detail(employeeSalaryId),
              });
            }
          },
        })}
        filterFields={filterFields}
        advancedFilterFields={advancedFilterFields}
        sortFields={sortFields}
        getInitialData={(response) => {
          return response?.data?.data ?? [];
        }}
        getPageCount={(response) => {
          return response?.data?.meta?.totalPages ?? 1;
        }}
        getRowId={(row) => row.id}
        enableAdvancedTable={false}
        enableFloatingBar={true}
        FloatingBar={(props) => (
          <EmployeeSalariesTableFloatingBar
            {...props}
            onRefresh={handleRefresh}
            isDemo={isDemo}
          />
        )}
        ToolbarActions={(props) => (
          <EmployeeSalariesTableToolbarActions {...props} isDemo={isDemo} />
        )}
        onRowAction={handleRowAction}
        defaultSortingId={undefined}
        defaultSortingDesc={false}
        onRefresh={handleRefresh}
        renderDialogContent={renderEmployeeSalaryDetails}
      />
      <EmployeeSalaryDetails
        open={rowAction?.type === "view"}
        onOpenChange={() => setRowAction(null)}
        employeeSalary={rowAction?.row.original ?? null}
        isDemo={isDemo}
      />
      <EmployeeSalarySheet
        open={rowAction?.type === "update"}
        onOpenChange={() => setRowAction(null)}
        employeeSalary={rowAction?.row.original ?? null}
        isDemo={isDemo}
        onSuccess={() => {
          const employeeSalaryId = rowAction?.row.original?.id;
          queryClient.invalidateQueries({
            queryKey: employeeSalaryKeys.list(),
          });
          queryClient.invalidateQueries({
            queryKey: employeeSalaryKeys.simple(),
          });
          queryClient.invalidateQueries({
            queryKey: employeeSalaryKeys.withDetails(),
          });
          if (employeeSalaryId) {
            queryClient.invalidateQueries({
              queryKey: employeeSalaryKeys.detail(employeeSalaryId),
            });
          }
        }}
        isUpdate={true}
      />
      <DeleteEmployeeSalariesDialog
        open={rowAction?.type === "delete"}
        onOpenChange={() => setRowAction(null)}
        employeeSalaries={
          rowAction?.row.original ? [rowAction.row.original] : []
        }
        showTrigger={false}
        onSuccess={() => {
          queryClient.invalidateQueries({
            queryKey: employeeSalaryKeys.list(),
          });
          queryClient.invalidateQueries({
            queryKey: employeeSalaryKeys.simple(),
          });
          queryClient.invalidateQueries({
            queryKey: employeeSalaryKeys.withDetails(),
          });
        }}
        isDemo={isDemo}
      />
    </>
  );
}
