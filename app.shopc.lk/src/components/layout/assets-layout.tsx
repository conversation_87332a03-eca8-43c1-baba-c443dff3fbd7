"use client";

import { usePathname, useRouter } from "next/navigation";
import {
  Box,
  BoxesIcon,
  Package,
  UserCheck,
  ArrowLeftRight,
  Wrench,
} from "lucide-react";
import { useState, useCallback } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useModules } from "@/contexts/auth-contexts";
import { ModuleType } from "@/types/business-modules";

import DashboardLayout, {
  SectionCategory,
  NavigationViewType,
} from "@/components/layout/dashboard-layout";

// Import asset sheet components
import { AssetCategorySheet } from "@/components/asset-categories/asset-category-sheet";
import { AssetAllocationSheet } from "@/components/asset-allocations/asset-allocation-sheet";
import { AssetRevokedSheet } from "@/components/asset-revoked/asset-revoked-sheet";
import { AssetSheet, ImportAssetsSheet } from "../assets";
import { assetTypeKeys } from "@/lib/asset-types";
import { ImportAssetCategoriesSheet } from "../asset-categories/import-asset-categories-sheet";

// Define query keys for assets modules
export const assetKeys = {
  all: ["assets"] as const,
  lists: () => [...assetKeys.all, "list"] as const,
  list: (filters: any) => [...assetKeys.lists(), filters] as const,
  details: () => [...assetKeys.all, "detail"] as const,
  detail: (id: string) => [...assetKeys.details(), id] as const,
};

export const assetCategoryKeys = {
  all: ["asset-categories"] as const,
  lists: () => [...assetCategoryKeys.all, "list"] as const,
  list: (filters: any) => [...assetCategoryKeys.lists(), filters] as const,
  details: () => [...assetCategoryKeys.all, "detail"] as const,
  detail: (id: string) => [...assetCategoryKeys.details(), id] as const,
};

export const assetAllocationKeys = {
  all: ["asset-allocations"] as const,
  lists: () => [...assetAllocationKeys.all, "list"] as const,
  list: (filters: any) => [...assetAllocationKeys.lists(), filters] as const,
  details: () => [...assetAllocationKeys.all, "detail"] as const,
  detail: (id: string) => [...assetAllocationKeys.details(), id] as const,
};

export const assetMaintenanceKeys = {
  all: ["asset-maintenance"] as const,
  lists: () => [...assetMaintenanceKeys.all, "list"] as const,
  list: (filters: any) => [...assetMaintenanceKeys.lists(), filters] as const,
  details: () => [...assetMaintenanceKeys.all, "detail"] as const,
  detail: (id: string) => [...assetMaintenanceKeys.details(), id] as const,
};

export const assetRevokedKeys = {
  all: ["asset-revoked"] as const,
  lists: () => [...assetRevokedKeys.all, "list"] as const,
  list: (filters: any) => [...assetRevokedKeys.lists(), filters] as const,
  details: () => [...assetRevokedKeys.all, "detail"] as const,
  detail: (id: string) => [...assetRevokedKeys.details(), id] as const,
};

// Asset categories for the tabs with their routes
const ALL_ASSET_CATEGORIES: SectionCategory[] = [
  {
    name: "assets",
    path: "assets",
    icon: Box,
    module: ModuleType.ASSETS,
    label: "Assets",
  },
  {
    name: "asset-categories",
    path: "categories",
    icon: BoxesIcon,
    module: ModuleType.ASSETS_CATEGORIES,
    label: "Asset Categories",
  },
  {
    name: "asset-allocations",
    path: "allocations",
    icon: UserCheck,
    module: ModuleType.ASSETS_ALLOCATED,
    label: "Asset Allocations",
  },
  {
    name: "asset-revoked",
    path: "revoked",
    icon: ArrowLeftRight,
    module: ModuleType.ASSETS_REVOKED,
    label: "Asset Revoked",
  },
  {
    name: "asset-maintenance",
    path: "maintenance",
    icon: Wrench,
    module: ModuleType.ASSETS_MAINTENANCE,
    label: "Asset Maintenance",
  },
];

export default function AssetsLayout({
  children,
  isDemo = false,
}: {
  children: React.ReactNode;
  isDemo?: boolean;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const queryClient = useQueryClient();
  const { hasAccess } = useModules();

  const [openAssetSheet, setOpenAssetSheet] = useState(false);
  const [openAssetCategorySheet, setOpenAssetCategorySheet] = useState(false);
  const [openAssetTypeSheet, setOpenAssetTypeSheet] = useState(false);
  const [openAssetAllocationSheet, setOpenAssetAllocationSheet] =
    useState(false);
  const [openAssetMaintenanceSheet, setOpenAssetMaintenanceSheet] =
    useState(false);
  const [openAssetRevokedSheet, setOpenAssetRevokedSheet] = useState(false);
  const [openImportAssetSheet, setOpenImportAssetSheet] = useState(false);
  const [openImportAssetTypesSheet, setOpenImportAssetTypesSheet] =
    useState(false);

  // Handle success for asset operations - invalidate queries and refresh
  const handleAssetSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: assetKeys.lists() });
    router.refresh();
  }, [queryClient, router]);

  const handleAssetCategorySuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: assetCategoryKeys.lists() });
    router.refresh();
  }, [router, queryClient]);

  const handleAssetTypeSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: ["asset-types"] });
    router.refresh();
  }, [router, queryClient]);

  const handleAssetAllocationSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: assetAllocationKeys.lists() });
    router.refresh();
  }, [router, queryClient]);

  const handleAssetMaintenanceSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: assetMaintenanceKeys.lists() });
    router.refresh();
  }, [router, queryClient]);

  const handleAssetRevokedSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: assetRevokedKeys.lists() });
    router.refresh();
  }, [router, queryClient]);

  const handleImportAssetSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: assetKeys.lists() });
    router.refresh();
  }, [router, queryClient]);

  const handleImportAssetTypesSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: assetTypeKeys.list() });
    router.refresh();
  }, [router, queryClient]);

  const handleAddNew = () => {
    // For other routes, open the appropriate sheet
    if (pathname.includes("/assets-categories")) {
      setOpenAssetCategorySheet(true);
    } else if (pathname.includes("/assets-types")) {
      setOpenAssetTypeSheet(true);
      setOpenImportAssetTypesSheet(true);
    } else if (pathname.includes("/allocations")) {
      setOpenAssetAllocationSheet(true);
    } else if (pathname.includes("/maintenance")) {
      setOpenAssetMaintenanceSheet(true);
    } else if (pathname.includes("/revoked")) {
      setOpenAssetRevokedSheet(true);
    } else {
      // Default to assets sheet
      setOpenAssetSheet(true);
    }
  };

  const handleImport = useCallback(() => {
    // Placeholder for import functionality
    // Can be extended for specific asset import handlers
    if (pathname.includes("/assets-categories")) {
      setOpenImportAssetSheet(true);
    } else if (pathname.includes("/assets-types")) {
      setOpenImportAssetTypesSheet(true);
    }
  }, []);

  const getAddButtonText = () => {
    if (pathname.includes("/assets-categories"))
      return "Add New Asset Category";
    if (pathname.includes("/assets-types")) return "Add New Asset Type";
    if (pathname.includes("/allocations")) return "Add New Asset Allocation";
    if (pathname.includes("/maintenance")) return "Add New Asset Maintenance";
    if (pathname.includes("/revoked")) return "Add New Asset Revoked";
    return "Add New Asset";
  };

  const getImportButtonText = () => {
    if (pathname.includes("/categories")) return "Import Asset Categories";
    if (pathname.includes("/types")) return "Import Asset Types";
    if (pathname.includes("/allocations")) return "Import Asset Allocations";
    if (pathname.includes("/maintenance")) return "Import Asset Maintenance";
    if (pathname.includes("/revoked")) return "Import Asset Revoked";
    if (pathname.includes("/assets-types")) return "Import Assets Types";
    return "Import Assets";
  };

  return (
    <DashboardLayout
      isDemo={isDemo}
      title="Assets"
      categories={ALL_ASSET_CATEGORIES}
      hasAccess={hasAccess}
      addAction={{
        text: getAddButtonText(),
        onClick: handleAddNew,
      }}
      importAction={{
        text: getImportButtonText(),
        onClick: handleImport,
      }}
      sheets={
        <>
          <AssetCategorySheet
            category={null}
            open={openAssetCategorySheet}
            onOpenChange={setOpenAssetCategorySheet}
            isDemo={isDemo}
            onSuccess={handleAssetCategorySuccess}
          />
          <AssetCategorySheet
            category={null}
            open={openAssetTypeSheet}
            onOpenChange={setOpenAssetTypeSheet}
            isDemo={isDemo}
            onSuccess={handleAssetTypeSuccess}
          />
          <AssetSheet
            asset={null}
            open={openAssetSheet}
            onOpenChange={setOpenAssetSheet}
            isDemo={isDemo}
            onSuccess={handleAssetSuccess}
          />
          <ImportAssetsSheet
            open={openImportAssetSheet}
            onOpenChange={setOpenImportAssetSheet}
            isDemo={isDemo}
            onSuccess={handleImportAssetSuccess}
          />
          <ImportAssetCategoriesSheet
            open={openImportAssetTypesSheet}
            onOpenChange={setOpenImportAssetTypesSheet}
            isDemo={isDemo}
            onSuccess={handleImportAssetTypesSuccess}
          />
          {/* <AssetAllocationSheet
            allocation={null}
            open={openAssetAllocationSheet}
            onOpenChange={setOpenAssetAllocationSheet}
            isDemo={isDemo}
            onSuccess={handleAssetAllocationSuccess}
          />
          <AssetMaintenanceSheet
            maintenance={null}
            open={openAssetMaintenanceSheet}
            onOpenChange={setOpenAssetMaintenanceSheet}
            isDemo={isDemo}
            onSuccess={handleAssetMaintenanceSuccess}
          />
          <AssetRevokedSheet
            assetRevoked={null}
            open={openAssetRevokedSheet}
            onOpenChange={setOpenAssetRevokedSheet}
            isDemo={isDemo}
            onSuccess={handleAssetRevokedSuccess}
          /> */}
        </>
      }
    >
      {children}
    </DashboardLayout>
  );
}
