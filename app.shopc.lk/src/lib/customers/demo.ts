import {
  CustomerDto,
  CustomerSlimDto,
  CustomerTableData,
  CustomerStatus,
  CustomerPaginatedResponse,
  CustomerIdResponse,
  BulkDeleteCustomerResponse,
  CustomerNameAvailabilityResponse,
  CustomerEmailAvailabilityResponse,
  SimpleCustomerResponse,
  CustomerResponse,
  CreateCustomerDto,
  UpdateCustomerDto,
  CustomerAutocomplete,
  CustomerAutocompleteResponse,
  BlacklistCustomerDto,
  BlacklistResponse,
  UnblacklistResponse,
} from "@/types/customer";
import { ApiStatus } from "@/types/common";
import { GetCustomersSchema } from "./api";

// Demo customer data
const demoCustomers: CustomerDto[] = [
  {
    id: "550e8400-e29b-41d4-a716-446655440001",
    businessId: "business-1",
    title: "Mr.",
    firstName: "John",
    middleName: "<PERSON>",
    lastName: "Doe",
    suffix: "",
    customerDisplayName: "<PERSON>",
    companyName: "Acme Corporation",
    email: "<EMAIL>",
    phoneNumber: "******-123-4567",
    mobileNumber: "******-987-6543",
    fax: "******-123-4568",
    website: "https://acme.com",
    other: "VIP Customer",
    isSubCustomer: false,
    isAllocatedToAllLocations: true,
    billingAddress: {
      id: "addr-1",
      street: "123 Main Street",
      city: "New York",
      state: "NY",
      zipCode: "10001",
      country: "USA",
    },
    shippingAddress: {
      id: "addr-2",
      street: "456 Oak Avenue",
      city: "New York",
      state: "NY",
      zipCode: "10002",
      country: "USA",
    },
    customerGroup: {
      id: "group-1",
      name: "Premium Customers",
    },
    notes: "Long-term customer with excellent payment history",
    attachments: ["attachment-1", "attachment-2"],
    salesTaxRegistration: "TAX123456789",
    openingBalance: "1500.00",
    openingBalanceAsOf: "2023-01-01",
    status: CustomerStatus.ACTIVE,
    locations: [
      { id: "loc-1", name: "Main Store" },
      { id: "loc-2", name: "Branch Store" },
    ],
    createdBy: "John Admin",
    updatedBy: "Jane Manager",
    createdAt: new Date("2023-01-15T10:30:00Z"),
    updatedAt: new Date("2023-06-15T14:20:00Z"),
    totalPoints: 1250,
    pointsEarnedYtd: 2500,
    isBlacklisted: false,
  },
  {
    id: "550e8400-e29b-41d4-a716-446655440002",
    businessId: "business-1",
    title: "Ms.",
    firstName: "Jane",
    lastName: "Smith",
    customerDisplayName: "Jane Smith",
    companyName: "Tech Solutions Inc",
    email: "<EMAIL>",
    phoneNumber: "******-234-5678",
    mobileNumber: "******-876-5432",
    website: "https://techsolutions.com",
    isSubCustomer: false,
    isAllocatedToAllLocations: false,
    billingAddress: {
      id: "addr-3",
      street: "789 Business Blvd",
      city: "San Francisco",
      state: "CA",
      zipCode: "94105",
      country: "USA",
    },
    customerGroup: {
      id: "group-2",
      name: "Business Customers",
    },
    notes: "Regular business customer",
    openingBalance: "2500.00",
    openingBalanceAsOf: "2023-02-01",
    status: CustomerStatus.ACTIVE,
    locations: [{ id: "loc-1", name: "Main Store" }],
    createdBy: "John Admin",
    createdAt: new Date("2023-02-10T09:15:00Z"),
    updatedAt: new Date("2023-05-20T11:45:00Z"),
    totalPoints: 850,
    pointsEarnedYtd: 1200,
    isBlacklisted: false,
  },
  {
    id: "550e8400-e29b-41d4-a716-446655440003",
    businessId: "business-1",
    firstName: "Bob",
    lastName: "Johnson",
    customerDisplayName: "Bob Johnson",
    email: "<EMAIL>",
    mobileNumber: "******-345-6789",
    isSubCustomer: true,
    isAllocatedToAllLocations: true,
    status: CustomerStatus.INACTIVE,
    createdBy: "Jane Manager",
    createdAt: new Date("2023-03-05T16:20:00Z"),
    updatedAt: new Date("2023-04-10T13:30:00Z"),
    totalPoints: 0,
    pointsEarnedYtd: 0,
    isBlacklisted: false,
  },
];

// Helper function to apply filter operators (matching categories demo pattern)
function applyFilterOperator(
  value: any,
  filterValue: string | string[],
  operator: string
): boolean {
  if (value === undefined || value === null) {
    switch (operator) {
      case "isEmpty":
        return true;
      case "isNotEmpty":
        return false;
      default:
        return false;
    }
  }

  const stringValue = String(value).toLowerCase();
  const filterString = Array.isArray(filterValue)
    ? filterValue.map((v) => String(v).toLowerCase())
    : String(filterValue).toLowerCase();

  switch (operator) {
    case "iLike":
      return Array.isArray(filterString)
        ? filterString.some((f) => stringValue.includes(f))
        : stringValue.includes(filterString);
    case "notILike":
      return Array.isArray(filterString)
        ? !filterString.some((f) => stringValue.includes(f))
        : !stringValue.includes(filterString);
    case "eq":
      return Array.isArray(filterString)
        ? filterString.includes(stringValue)
        : stringValue === filterString;
    case "ne":
      return Array.isArray(filterString)
        ? !filterString.includes(stringValue)
        : stringValue !== filterString;
    case "isEmpty":
      return !value || value === "";
    case "isNotEmpty":
      return value && value !== "";
    default:
      return true;
  }
}

// Demo functions
export async function getDemoCustomersTableData(
  params: GetCustomersSchema
): Promise<CustomerPaginatedResponse> {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 300));

  const {
    page = 1,
    perPage = 10,
    customerDisplayName,
    email,
    companyName,
    phoneNumber,
    mobileNumber,
    status,
  } = params;

  // Filter out deleted and blacklisted customers (matches backend behavior)
  let filteredCustomers = demoCustomers.filter(
    (customer) => !customer.deletedAt && !customer.isBlacklisted
  );

  // Apply basic filters
  if (customerDisplayName) {
    filteredCustomers = filteredCustomers.filter((customer) =>
      customer.customerDisplayName
        .toLowerCase()
        .includes(customerDisplayName.toLowerCase())
    );
  }

  if (email) {
    filteredCustomers = filteredCustomers.filter((customer) =>
      customer.email?.toLowerCase().includes(email.toLowerCase())
    );
  }

  if (companyName) {
    filteredCustomers = filteredCustomers.filter((customer) =>
      customer.companyName?.toLowerCase().includes(companyName.toLowerCase())
    );
  }

  if (phoneNumber) {
    filteredCustomers = filteredCustomers.filter((customer) =>
      customer.phoneNumber?.toLowerCase().includes(phoneNumber.toLowerCase())
    );
  }

  if (mobileNumber) {
    filteredCustomers = filteredCustomers.filter((customer) =>
      customer.mobileNumber?.toLowerCase().includes(mobileNumber.toLowerCase())
    );
  }

  if (status) {
    filteredCustomers = filteredCustomers.filter(
      (customer) => customer.status === status
    );
  }

  // Apply date filters
  if (params.from) {
    const fromDate = new Date(params.from);
    filteredCustomers = filteredCustomers.filter(
      (customer) => customer.createdAt && customer.createdAt >= fromDate
    );
  }

  if (params.to) {
    const toDate = new Date(params.to);
    filteredCustomers = filteredCustomers.filter(
      (customer) => customer.createdAt && customer.createdAt <= toDate
    );
  }

  // Apply advanced filters (matching categories demo pattern)
  if (params.filters && params.filters.length > 0) {
    const joinOperator = params.joinOperator || "and";

    filteredCustomers = filteredCustomers.filter((customer) => {
      const results = params.filters!.map((filter) => {
        const { id, value, operator } = filter;

        switch (id) {
          case "customerDisplayName":
            return applyFilterOperator(
              customer.customerDisplayName,
              value,
              operator || "iLike"
            );
          case "email":
            return applyFilterOperator(
              customer.email,
              value,
              operator || "iLike"
            );
          case "companyName":
            return applyFilterOperator(
              customer.companyName,
              value,
              operator || "iLike"
            );
          case "phoneNumber":
            return applyFilterOperator(
              customer.phoneNumber,
              value,
              operator || "iLike"
            );
          case "mobileNumber":
            return applyFilterOperator(
              customer.mobileNumber,
              value,
              operator || "iLike"
            );
          case "status":
            return applyFilterOperator(
              customer.status,
              value,
              operator || "eq"
            );
          case "isAllocatedToAllLocations":
            const boolValue = value === "true" || value === true;
            return applyFilterOperator(
              customer.isAllocatedToAllLocations,
              boolValue,
              operator || "eq"
            );
          default:
            return true;
        }
      });

      return joinOperator === "and"
        ? results.every(Boolean)
        : results.some(Boolean);
    });
  }

  // Convert to table data format first
  const tableData: CustomerTableData[] = filteredCustomers.map((customer) => ({
    id: customer.id,
    customerDisplayName: customer.customerDisplayName,
    companyName: customer.companyName,
    email: customer.email,
    phoneNumber: customer.phoneNumber,
    mobileNumber: customer.mobileNumber,
    status: customer.status,
    isSubCustomer: customer.isSubCustomer,
    isAllocatedToAllLocations: customer.isAllocatedToAllLocations,
    customerGroupName: customer.customerGroup?.name,
    openingBalance: customer.openingBalance,
    salesTaxRegistration: customer.salesTaxRegistration,
    primaryAddress: customer.billingAddress
      ? `${customer.billingAddress.city}, ${customer.billingAddress.state}`
      : undefined,
    createdAt: customer.createdAt,
    updatedAt: customer.updatedAt,
  }));

  // Apply sorting (matching categories demo pattern)
  let sortedData = [...tableData];

  if (params.sort && params.sort.length > 0) {
    const sortField = params.sort[0];

    switch (sortField.id) {
      case "customerDisplayName":
        sortedData.sort((a, b) => {
          const comparison = a.customerDisplayName.localeCompare(
            b.customerDisplayName
          );
          return sortField.desc ? -comparison : comparison;
        });
        break;
      case "companyName":
        sortedData.sort((a, b) => {
          const aValue = a.companyName || "";
          const bValue = b.companyName || "";
          const comparison = aValue.localeCompare(bValue);
          return sortField.desc ? -comparison : comparison;
        });
        break;
      case "email":
        sortedData.sort((a, b) => {
          const aValue = a.email || "";
          const bValue = b.email || "";
          const comparison = aValue.localeCompare(bValue);
          return sortField.desc ? -comparison : comparison;
        });
        break;
      case "status":
        sortedData.sort((a, b) => {
          const comparison = a.status.localeCompare(b.status);
          return sortField.desc ? -comparison : comparison;
        });
        break;
      case "createdAt":
        sortedData.sort((a, b) => {
          const aTime = a.createdAt?.getTime() || 0;
          const bTime = b.createdAt?.getTime() || 0;
          const comparison = aTime - bTime;
          return sortField.desc ? -comparison : comparison;
        });
        break;
      case "updatedAt":
        sortedData.sort((a, b) => {
          const aTime = a.updatedAt?.getTime() || 0;
          const bTime = b.updatedAt?.getTime() || 0;
          const comparison = aTime - bTime;
          return sortField.desc ? -comparison : comparison;
        });
        break;
      case "openingBalance":
        sortedData.sort((a, b) => {
          const aValue = parseFloat(a.openingBalance || "0");
          const bValue = parseFloat(b.openingBalance || "0");
          const comparison = aValue - bValue;
          return sortField.desc ? -comparison : comparison;
        });
        break;
      default:
        // Default sorting: createdAt descending, then ID ascending (matches backend)
        sortedData.sort((a, b) => {
          const aTime = a.createdAt?.getTime() || 0;
          const bTime = b.createdAt?.getTime() || 0;
          const timeComparison = bTime - aTime; // Descending
          if (timeComparison === 0) {
            return a.id.localeCompare(b.id); // Ascending
          }
          return timeComparison;
        });
        break;
    }
  } else {
    // Default sorting when no sort parameter is provided: createdAt descending, then ID ascending (matches backend)
    sortedData.sort((a, b) => {
      const aTime = a.createdAt?.getTime() || 0;
      const bTime = b.createdAt?.getTime() || 0;
      const timeComparison = bTime - aTime; // Descending
      if (timeComparison === 0) {
        return a.id.localeCompare(b.id); // Ascending
      }
      return timeComparison;
    });
  }

  // Apply pagination
  const total = sortedData.length;
  const totalPages = Math.ceil(total / perPage);
  const startIndex = (page - 1) * perPage;
  const endIndex = startIndex + perPage;
  const paginatedData = sortedData.slice(startIndex, endIndex);

  return {
    status: ApiStatus.SUCCESS,
    message: "Demo customers retrieved successfully",
    data: {
      data: paginatedData,
      meta: {
        total,
        page,
        totalPages,
      },
    },
  };
}

export async function getDemoCustomersSlim(): Promise<SimpleCustomerResponse> {
  // Filter out deleted, blacklisted, and inactive customers (matches backend behavior)
  const activeCustomers = demoCustomers.filter(
    (customer) => !customer.deletedAt && !customer.isBlacklisted && customer.status === CustomerStatus.ACTIVE
  );

  const slimData: CustomerSlimDto[] = activeCustomers.map((customer) => ({
    id: customer.id,
    customerDisplayName: customer.customerDisplayName,
    companyName: customer.companyName,
    status: customer.status,
  }));

  return {
    status: ApiStatus.SUCCESS,
    message: "Demo customers retrieved successfully",
    data: slimData,
  };
}

export async function getDemoCustomer(id: string): Promise<CustomerResponse> {
  const customer = demoCustomers.find((c) => c.id === id);

  if (!customer) {
    return {
      status: ApiStatus.FAIL,
      message: "Customer not found",
      data: null,
    };
  }

  return {
    status: ApiStatus.SUCCESS,
    message: "Demo customer retrieved successfully",
    data: customer,
  };
}

export async function checkDemoCustomerNameAvailability(
  customerDisplayName: string,
  excludeId?: string
): Promise<CustomerNameAvailabilityResponse> {
  const existingCustomer = demoCustomers.find(
    (customer) =>
      customer.customerDisplayName.toLowerCase() ===
        customerDisplayName.toLowerCase() && customer.id !== excludeId
  );

  return {
    status: ApiStatus.SUCCESS,
    message: "Name availability checked",
    data: {
      available: !existingCustomer,
    },
  };
}

export async function checkDemoCustomerEmailAvailability(
  email: string,
  excludeId?: string
): Promise<CustomerEmailAvailabilityResponse> {
  const existingCustomer = demoCustomers.find(
    (customer) =>
      customer.email?.toLowerCase() === email.toLowerCase() &&
      customer.id !== excludeId
  );

  return {
    status: ApiStatus.SUCCESS,
    message: "Email availability checked",
    data: {
      available: !existingCustomer,
    },
  };
}

export async function createDemoCustomer(
  data: CreateCustomerDto
): Promise<CustomerIdResponse> {
  const newId = `demo-customer-${Date.now()}`;

  const newCustomer: CustomerDto = {
    id: newId,
    businessId: "business-1",
    title: data.title,
    firstName: data.firstName,
    middleName: data.middleName,
    lastName: data.lastName,
    suffix: data.suffix,
    customerDisplayName: data.customerDisplayName,
    companyName: data.companyName,
    email: data.email,
    phoneNumber: data.phoneNumber,
    mobileNumber: data.mobileNumber,
    fax: data.fax,
    website: data.website,
    other: data.other,
    isSubCustomer: data.isSubCustomer || false,
    isAllocatedToAllLocations: data.isAllocatedToAllLocations || false,
    billingAddress: data.billingAddress
      ? {
          id: `addr-${Date.now()}`,
          ...data.billingAddress,
        }
      : undefined,
    shippingAddress: data.shippingAddress
      ? {
          id: `addr-${Date.now() + 1}`,
          ...data.shippingAddress,
        }
      : undefined,
    customerGroup: data.customerGroupId
      ? {
          id: data.customerGroupId,
          name: "Demo Group",
        }
      : undefined,
    notes: data.notes,
    attachments: data.attachments,
    salesTaxRegistration: data.salesTaxRegistration,
    openingBalance: data.openingBalance,
    openingBalanceAsOf: data.openingBalanceAsOf,
    status: data.status || CustomerStatus.ACTIVE,
    locations: data.locationIds?.map((id) => ({ id, name: `Location ${id}` })),
    createdBy: "Demo User",
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  demoCustomers.push(newCustomer);

  return {
    status: ApiStatus.SUCCESS,
    message: "Demo customer created successfully",
    data: { id: newId },
  };
}

export async function updateDemoCustomer(
  id: string,
  data: UpdateCustomerDto
): Promise<CustomerIdResponse> {
  const customerIndex = demoCustomers.findIndex((c) => c.id === id);

  if (customerIndex === -1) {
    return {
      status: ApiStatus.FAIL,
      message: "Customer not found",
      data: null,
    };
  }

  // Update the customer
  const updatedCustomer = { ...demoCustomers[customerIndex] };

  // Update fields that are provided
  Object.keys(data).forEach((key) => {
    if (data[key as keyof UpdateCustomerDto] !== undefined) {
      (updatedCustomer as any)[key] = data[key as keyof UpdateCustomerDto];
    }
  });

  updatedCustomer.updatedAt = new Date();
  demoCustomers[customerIndex] = updatedCustomer;

  return {
    status: ApiStatus.SUCCESS,
    message: "Demo customer updated successfully",
    data: { id },
  };
}

export async function deleteDemoCustomer(
  id: string
): Promise<CustomerIdResponse> {
  const customerIndex = demoCustomers.findIndex((c) => c.id === id);

  if (customerIndex === -1) {
    return {
      status: ApiStatus.FAIL,
      message: "Customer not found",
      data: null,
    };
  }

  // Soft delete by setting deletedAt
  demoCustomers[customerIndex].deletedAt = new Date();

  return {
    status: ApiStatus.SUCCESS,
    message: "Demo customer deleted successfully",
    data: { id },
  };
}

export async function bulkDeleteDemoCustomers(
  customerIds: string[]
): Promise<BulkDeleteCustomerResponse> {
  let deletedCount = 0;

  customerIds.forEach((id) => {
    const customerIndex = demoCustomers.findIndex((c) => c.id === id);
    if (customerIndex !== -1) {
      demoCustomers[customerIndex].deletedAt = new Date();
      deletedCount++;
    }
  });

  return {
    status: ApiStatus.SUCCESS,
    message: "Demo customers deleted successfully",
    data: {
      deleted: deletedCount,
      message: `${deletedCount} customers deleted successfully`,
    },
  };
}

// Demo function for customer autocomplete search
export async function searchCustomersAutocompleteDemo(
  search?: string,
  limit: number = 10
): Promise<CustomerAutocompleteResponse> {
  try {
    // Filter active, non-deleted, non-blacklisted customers (matches backend behavior)
    let filteredCustomers = demoCustomers.filter(
      (customer) => customer.status === CustomerStatus.ACTIVE && !customer.deletedAt && !customer.isBlacklisted
    );

    // Apply search filter if provided
    if (search && search.trim()) {
      const searchTerm = search.trim().toLowerCase();
      filteredCustomers = filteredCustomers.filter((customer) =>
        customer.customerDisplayName.toLowerCase().includes(searchTerm)
      );
    }

    // Apply limit
    const limitedCustomers = filteredCustomers.slice(0, limit);

    // Map to autocomplete format
    const autocompleteData: CustomerAutocomplete[] = limitedCustomers.map(
      (customer) => ({
        id: customer.id,
        name: customer.customerDisplayName,
      })
    );

    return {
      status: ApiStatus.SUCCESS,
      message: "Customers retrieved successfully",
      data: autocompleteData,
    };
  } catch (error) {
    console.error("Error in searchCustomersAutocompleteDemo:", error);
    return {
      status: ApiStatus.FAIL,
      message: "Failed to search customers",
      data: null,
    };
  }
}

// Demo blacklist functions
export async function blacklistDemoCustomer(
  id: string,
  data: BlacklistCustomerDto
): Promise<BlacklistResponse> {
  const customerIndex = demoCustomers.findIndex((c) => c.id === id);

  if (customerIndex === -1) {
    return {
      status: ApiStatus.FAIL,
      message: "Customer not found",
      data: null,
    };
  }

  const customer = demoCustomers[customerIndex];

  if (customer.isBlacklisted) {
    return {
      status: ApiStatus.FAIL,
      message: "Customer is already blacklisted",
      data: null,
    };
  }

  // Blacklist the customer
  demoCustomers[customerIndex] = {
    ...customer,
    isBlacklisted: true,
    blacklistReason: data.reason,
    blacklistDate: new Date(),
    updatedAt: new Date(),
  };

  return {
    status: ApiStatus.SUCCESS,
    message: "Customer blacklisted successfully",
    data: { id, message: "Customer blacklisted successfully" },
  };
}

export async function unblacklistDemoCustomer(
  id: string
): Promise<UnblacklistResponse> {
  const customerIndex = demoCustomers.findIndex((c) => c.id === id);

  if (customerIndex === -1) {
    return {
      status: ApiStatus.FAIL,
      message: "Customer not found",
      data: null,
    };
  }

  const customer = demoCustomers[customerIndex];

  if (!customer.isBlacklisted) {
    return {
      status: ApiStatus.FAIL,
      message: "Customer is not blacklisted",
      data: null,
    };
  }

  // Unblacklist the customer
  demoCustomers[customerIndex] = {
    ...customer,
    isBlacklisted: false,
    blacklistReason: undefined,
    blacklistDate: undefined,
    updatedAt: new Date(),
  };

  return {
    status: ApiStatus.SUCCESS,
    message: "Customer unblacklisted successfully",
    data: { id, message: "Customer unblacklisted successfully" },
  };
}

export async function getDemoBlacklistedCustomers(
  params: GetCustomersSchema
): Promise<CustomerPaginatedResponse> {
  const page = params.page || 1;
  const perPage = params.limit || params.perPage || 10;

  // Filter only blacklisted customers
  let filteredCustomers = demoCustomers.filter(
    (customer) => customer.isBlacklisted && !customer.deletedAt
  );

  // Apply search filters
  if (params.customerDisplayName) {
    filteredCustomers = filteredCustomers.filter((customer) =>
      customer.customerDisplayName
        .toLowerCase()
        .includes(params.customerDisplayName!.toLowerCase())
    );
  }

  if (params.email) {
    filteredCustomers = filteredCustomers.filter((customer) =>
      customer.email?.toLowerCase().includes(params.email!.toLowerCase())
    );
  }

  if (params.companyName) {
    filteredCustomers = filteredCustomers.filter((customer) =>
      customer.companyName
        ?.toLowerCase()
        .includes(params.companyName!.toLowerCase())
    );
  }

  // Apply date range filters for blacklist date
  if (params.from) {
    const fromDate = new Date(params.from);
    filteredCustomers = filteredCustomers.filter(
      (customer) => customer.blacklistDate && customer.blacklistDate >= fromDate
    );
  }

  if (params.to) {
    const toDate = new Date(params.to);
    filteredCustomers = filteredCustomers.filter(
      (customer) => customer.blacklistDate && customer.blacklistDate <= toDate
    );
  }

  // Convert to table data format
  const tableData: CustomerTableData[] = filteredCustomers.map((customer) => ({
    id: customer.id,
    customerDisplayName: customer.customerDisplayName,
    companyName: customer.companyName,
    email: customer.email,
    phoneNumber: customer.phoneNumber,
    mobileNumber: customer.mobileNumber,
    status: customer.status,
    isSubCustomer: customer.isSubCustomer,
    isAllocatedToAllLocations: customer.isAllocatedToAllLocations,
    customerGroupName: customer.customerGroup?.name,
    openingBalance: customer.openingBalance,
    salesTaxRegistration: customer.salesTaxRegistration,
    primaryAddress: customer.billingAddress
      ? `${customer.billingAddress.city}, ${customer.billingAddress.state}`
      : undefined,
    isBlacklisted: customer.isBlacklisted,
    totalPoints: customer.totalPoints,
    pointsEarnedYtd: customer.pointsEarnedYtd,
    createdAt: customer.createdAt,
    updatedAt: customer.updatedAt,
  }));

  // Apply sorting
  let sortedData = [...tableData];

  if (params.sort && params.sort.length > 0) {
    const sortField = params.sort[0];

    switch (sortField.id) {
      case "customerDisplayName":
        sortedData.sort((a, b) => {
          const comparison = a.customerDisplayName.localeCompare(
            b.customerDisplayName
          );
          return sortField.desc ? -comparison : comparison;
        });
        break;
      case "companyName":
        sortedData.sort((a, b) => {
          const aValue = a.companyName || "";
          const bValue = b.companyName || "";
          const comparison = aValue.localeCompare(bValue);
          return sortField.desc ? -comparison : comparison;
        });
        break;
      case "email":
        sortedData.sort((a, b) => {
          const aValue = a.email || "";
          const bValue = b.email || "";
          const comparison = aValue.localeCompare(bValue);
          return sortField.desc ? -comparison : comparison;
        });
        break;
      case "blacklistDate":
        sortedData.sort((a, b) => {
          const aTime = demoCustomers.find(c => c.id === a.id)?.blacklistDate?.getTime() || 0;
          const bTime = demoCustomers.find(c => c.id === b.id)?.blacklistDate?.getTime() || 0;
          const comparison = aTime - bTime;
          return sortField.desc ? -comparison : comparison;
        });
        break;
      default:
        // Default sorting: blacklistDate descending
        sortedData.sort((a, b) => {
          const aTime = demoCustomers.find(c => c.id === a.id)?.blacklistDate?.getTime() || 0;
          const bTime = demoCustomers.find(c => c.id === b.id)?.blacklistDate?.getTime() || 0;
          const timeComparison = bTime - aTime; // Descending
          if (timeComparison === 0) {
            return a.id.localeCompare(b.id); // Ascending
          }
          return timeComparison;
        });
        break;
    }
  } else {
    // Default sorting: blacklistDate descending
    sortedData.sort((a, b) => {
      const aTime = demoCustomers.find(c => c.id === a.id)?.blacklistDate?.getTime() || 0;
      const bTime = demoCustomers.find(c => c.id === b.id)?.blacklistDate?.getTime() || 0;
      const timeComparison = bTime - aTime; // Descending
      if (timeComparison === 0) {
        return a.id.localeCompare(b.id); // Ascending
      }
      return timeComparison;
    });
  }

  // Apply pagination
  const total = sortedData.length;
  const totalPages = Math.ceil(total / perPage);
  const startIndex = (page - 1) * perPage;
  const endIndex = startIndex + perPage;
  const paginatedData = sortedData.slice(startIndex, endIndex);

  return {
    status: ApiStatus.SUCCESS,
    message: "Demo blacklisted customers retrieved successfully",
    data: {
      data: paginatedData,
      meta: {
        total,
        page,
        totalPages,
      },
    },
  };
}
