import React from "react";
import {
  useQuery,
  useMutation,
  useQueryClient,
  UseQueryResult,
} from "@tanstack/react-query";
import {
  CustomerPaginatedResponse,
  CustomerNameAvailabilityResponse,
  CustomerEmailAvailabilityResponse,
  SimpleCustomerResponse,
  CustomerResponse,
  CreateCustomerDto,
  UpdateCustomerDto,
  CustomerStatus,
  CustomerAutocompleteResponse,
} from "@/types/customer";
import {
  GetCustomersSchema,
  getCustomersTableData,
  getCustomersSlim,
  getCustomer,
  checkCustomerNameAvailability,
  checkCustomerEmailAvailability,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  bulkDeleteCustomers,
  bulkCreateCustomers,
  getBlacklistedCustomers,
} from "./api";
import { searchCustomersAutocomplete } from "./queries";

// Query keys factory
export const customerKeys = {
  all: ["customers"] as const,
  lists: () => [...customerKeys.all, "list"] as const,
  list: (filters: string) => [...customerKeys.lists(), { filters }] as const,
  details: () => [...customerKeys.all, "detail"] as const,
  detail: (id: string) => [...customerKeys.details(), id] as const,
  filtered: (params: GetCustomersSchema & { isDemo?: boolean }) =>
    [...customerKeys.lists(), "filtered", params] as const,
  slim: (isDemo?: boolean) =>
    [...customerKeys.all, "slim", { isDemo }] as const,
  blacklisted: () => [...customerKeys.all, "blacklisted"] as const,
  blacklistedFiltered: (params: GetCustomersSchema & { isDemo?: boolean }) =>
    [...customerKeys.blacklisted(), "filtered", params] as const,
  nameAvailability: (name: string, excludeId?: string, isDemo?: boolean) =>
    [
      ...customerKeys.all,
      "nameAvailability",
      { name, excludeId, isDemo },
    ] as const,
  emailAvailability: (email: string, excludeId?: string, isDemo?: boolean) =>
    [
      ...customerKeys.all,
      "emailAvailability",
      { email, excludeId, isDemo },
    ] as const,
};

// Hooks for customers data
export function useCustomersData(
  params: GetCustomersSchema,
  isDemo: boolean = false
): UseQueryResult<CustomerPaginatedResponse, Error> {
  return useQuery({
    queryKey: customerKeys.filtered({ ...params, isDemo }),
    queryFn: () => getCustomersTableData(params, isDemo),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useBlacklistedCustomersData(
  params: GetCustomersSchema,
  isDemo: boolean = false
): UseQueryResult<CustomerPaginatedResponse, Error> {
  return useQuery({
    queryKey: customerKeys.blacklistedFiltered({ ...params, isDemo }),
    queryFn: () => getBlacklistedCustomers(params, isDemo),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useCustomersSlim(
  isDemo: boolean = false
): UseQueryResult<SimpleCustomerResponse, Error> {
  return useQuery({
    queryKey: customerKeys.slim(isDemo),
    queryFn: () => getCustomersSlim(isDemo),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useCustomer(
  id: string,
  isDemo: boolean = false
): UseQueryResult<CustomerResponse, Error> {
  return useQuery({
    queryKey: customerKeys.detail(id),
    queryFn: () => getCustomer(id, isDemo),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useCustomerNameAvailability(
  customerDisplayName: string,
  excludeId?: string,
  isDemo: boolean = false
): UseQueryResult<CustomerNameAvailabilityResponse, Error> {
  return useQuery({
    queryKey: customerKeys.nameAvailability(
      customerDisplayName,
      excludeId,
      isDemo
    ),
    queryFn: () =>
      checkCustomerNameAvailability(customerDisplayName, excludeId, isDemo),
    enabled: !!customerDisplayName && customerDisplayName.length > 0,
    staleTime: 30 * 1000, // 30 seconds
  });
}

export function useCustomerEmailAvailability(
  email: string,
  excludeId?: string,
  isDemo: boolean = false
): UseQueryResult<CustomerEmailAvailabilityResponse, Error> {
  return useQuery({
    queryKey: customerKeys.emailAvailability(email, excludeId, isDemo),
    queryFn: () => checkCustomerEmailAvailability(email, excludeId, isDemo),
    enabled: !!email && email.length > 0,
    staleTime: 30 * 1000, // 30 seconds
  });
}

// Mutation hooks
export function useCreateCustomer(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateCustomerDto) => createCustomer(data, isDemo),
    onSuccess: () => {
      // Invalidate and refetch customers data
      queryClient.invalidateQueries({ queryKey: customerKeys.lists() });
      queryClient.invalidateQueries({ queryKey: customerKeys.slim(isDemo) });
    },
  });
}

export function useBulkCreateCustomers(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { customers: CreateCustomerDto[] }) =>
      bulkCreateCustomers(data, isDemo),
    onSuccess: () => {
      // Invalidate and refetch customers data
      queryClient.invalidateQueries({ queryKey: customerKeys.lists() });
      queryClient.invalidateQueries({ queryKey: customerKeys.slim(isDemo) });
    },
  });
}

export function useUpdateCustomer(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateCustomerDto }) =>
      updateCustomer(id, data, isDemo),
    onSuccess: (_, { id }) => {
      // Invalidate and refetch customers data
      queryClient.invalidateQueries({ queryKey: customerKeys.lists() });
      queryClient.invalidateQueries({ queryKey: customerKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: customerKeys.slim(isDemo) });
    },
  });
}

export function useDeleteCustomer(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deleteCustomer(id, isDemo),
    onSuccess: () => {
      // Invalidate and refetch customers data
      queryClient.invalidateQueries({ queryKey: customerKeys.lists() });
      queryClient.invalidateQueries({ queryKey: customerKeys.slim(isDemo) });
    },
  });
}

export function useBulkDeleteCustomers(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (customerIds: string[]) =>
      bulkDeleteCustomers({ customerIds }, isDemo),
    onSuccess: () => {
      // Invalidate and refetch customers data
      queryClient.invalidateQueries({ queryKey: customerKeys.lists() });
      queryClient.invalidateQueries({ queryKey: customerKeys.slim(isDemo) });
    },
  });
}

export function useUpdateCustomerStatus(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: CustomerStatus }) =>
      updateCustomer(id, { status }, isDemo),
    onSuccess: (_, { id }) => {
      // Invalidate and refetch customers data
      queryClient.invalidateQueries({ queryKey: customerKeys.lists() });
      queryClient.invalidateQueries({ queryKey: customerKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: customerKeys.slim(isDemo) });
    },
  });
}

export function useBulkUpdateCustomerStatus(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      customerIds,
      status,
    }: {
      customerIds: string[];
      status: CustomerStatus;
    }) =>
      Promise.all(
        customerIds.map((id) => updateCustomer(id, { status }, isDemo))
      ),
    onSuccess: () => {
      // Invalidate and refetch customers data
      queryClient.invalidateQueries({ queryKey: customerKeys.lists() });
      queryClient.invalidateQueries({ queryKey: customerKeys.slim(isDemo) });
    },
  });
}

// Compound hooks for common use cases
export function useCustomersTable(
  params: GetCustomersSchema,
  isDemo: boolean = false
) {
  const queryClient = useQueryClient();
  const query = useCustomersData(params, isDemo);

  const refresh = () => {
    queryClient.invalidateQueries({
      queryKey: customerKeys.filtered({ ...params, isDemo }),
    });
  };

  return {
    ...query,
    refresh,
  };
}

export function useCustomerForm(isDemo: boolean = false) {
  const createMutation = useCreateCustomer(isDemo);
  const updateMutation = useUpdateCustomer(isDemo);

  return {
    createCustomer: createMutation,
    updateCustomer: updateMutation,
    isLoading: createMutation.isPending || updateMutation.isPending,
  };
}

export function useCustomerActions(isDemo: boolean = false) {
  const deleteMutation = useDeleteCustomer(isDemo);
  const bulkDeleteMutation = useBulkDeleteCustomers(isDemo);
  const updateStatusMutation = useUpdateCustomerStatus(isDemo);

  return {
    deleteCustomer: deleteMutation,
    bulkDeleteCustomers: bulkDeleteMutation,
    updateCustomerStatus: updateStatusMutation,
    isLoading:
      deleteMutation.isPending ||
      bulkDeleteMutation.isPending ||
      updateStatusMutation.isPending,
  };
}

// Prefetch hooks for performance optimization
export function usePrefetchCustomer(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return (id: string) => {
    queryClient.prefetchQuery({
      queryKey: customerKeys.detail(id),
      queryFn: () => getCustomer(id, isDemo),
      staleTime: 5 * 60 * 1000, // 5 minutes
    });
  };
}

export function usePrefetchCustomersSlim(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return () => {
    queryClient.prefetchQuery({
      queryKey: customerKeys.slim(isDemo),
      queryFn: () => getCustomersSlim(isDemo),
      staleTime: 10 * 60 * 1000, // 10 minutes
    });
  };
}

// Optimistic update hooks
export function useOptimisticCustomerUpdate() {
  const queryClient = useQueryClient();

  return {
    updateCustomerOptimistically: (
      id: string,
      updates: Partial<UpdateCustomerDto>
    ) => {
      queryClient.setQueryData(
        customerKeys.detail(id),
        (old: CustomerResponse | undefined) => {
          if (!old?.data) return old;
          return {
            ...old,
            data: {
              ...old.data,
              ...updates,
            },
          };
        }
      );
    },
    revertCustomerUpdate: (id: string) => {
      queryClient.invalidateQueries({ queryKey: customerKeys.detail(id) });
    },
  };
}

// Custom hook for customer search with debouncing
export function useCustomerSearch(
  searchTerm: string,
  isDemo: boolean = false,
  debounceMs: number = 300
) {
  const [debouncedSearchTerm, setDebouncedSearchTerm] =
    React.useState(searchTerm);

  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [searchTerm, debounceMs]);

  return useCustomersData(
    {
      page: 1,
      limit: 10,
      customerDisplayName: debouncedSearchTerm,
    },
    isDemo
  );
}

// Hook for customer autocomplete search
export function useCustomersAutocomplete(
  search?: string,
  limit: number = 10,
  isDemo: boolean = false,
  enabled: boolean = true
): UseQueryResult<CustomerAutocompleteResponse, Error> {
  return useQuery({
    queryKey: [...customerKeys.all, "autocomplete", search, limit],
    queryFn: () => searchCustomersAutocomplete(search, limit, isDemo),
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}
