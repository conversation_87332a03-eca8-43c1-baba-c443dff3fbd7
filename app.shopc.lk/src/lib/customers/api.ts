import {
  CustomerResponse,
  CustomerPaginatedResponse,
  CustomerIdResponse,
  BulkCustomerIdsResponse,
  BulkDeleteCustomerDto,
  BulkDeleteCustomerResponse,
  CustomerNameAvailabilityResponse,
  CustomerEmailAvailabilityResponse,
  SimpleCustomerResponse,
  CustomerAutocompleteResponse,
  CreateCustomerDto,
  UpdateCustomerDto,
  BulkCreateCustomerDto,
  BlacklistCustomerDto,
  BlacklistResponse,
  UnblacklistResponse,
} from "@/types/customer";
import { ApiStatus } from "@/types/common";
import axios from "@/utils/axios";

// Define GetCustomersSchema interface for API parameters
export interface GetCustomersSchema {
  page?: number;
  limit?: number;
  perPage?: number;
  from?: string;
  to?: string;
  customerDisplayName?: string;
  email?: string;
  companyName?: string;
  phoneNumber?: string;
  mobileNumber?: string;
  status?: string;
  customerGroupId?: string;
  filters?: any[];
  joinOperator?: "and" | "or";
  sort?: any[];
}

// Create a new customer
export async function createCustomerApi(
  data: CreateCustomerDto
): Promise<CustomerIdResponse> {
  try {
    const res = await axios.post("/customers", data);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Bulk create customers
export async function bulkCreateCustomersApi(
  customers: BulkCreateCustomerDto
): Promise<BulkCustomerIdsResponse> {
  try {
    const res = await axios.post("/customers/bulk", customers);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Get customers with pagination and filters
export async function getCustomersApi(
  params: GetCustomersSchema
): Promise<CustomerPaginatedResponse> {
  try {
    const queryParams = new URLSearchParams();

    if (params.page) queryParams.append("page", params.page.toString());
    if (params.perPage) queryParams.append("limit", params.perPage.toString());
    if (params.from) queryParams.append("from", params.from);
    if (params.to) queryParams.append("to", params.to);
    if (params.customerDisplayName)
      queryParams.append("customerDisplayName", params.customerDisplayName);
    if (params.email) queryParams.append("email", params.email);
    if (params.companyName)
      queryParams.append("companyName", params.companyName);
    if (params.phoneNumber)
      queryParams.append("phoneNumber", params.phoneNumber);
    if (params.mobileNumber)
      queryParams.append("mobileNumber", params.mobileNumber);
    if (params.status) queryParams.append("status", params.status);
    if (params.customerGroupId)
      queryParams.append("customerGroupId", params.customerGroupId);
    if (params.filters && params.filters.length > 0) {
      queryParams.append("filters", JSON.stringify(params.filters));
    }
    if (params.joinOperator)
      queryParams.append("joinOperator", params.joinOperator);
    if (params.sort && params.sort.length > 0) {
      queryParams.append("sort", JSON.stringify(params.sort));
    }

    const res = await axios.get(`/customers?${queryParams.toString()}`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Get customers in slim format (for dropdowns/selects)
export async function getCustomersSlimApi(): Promise<SimpleCustomerResponse> {
  try {
    const res = await axios.get("/customers/slim");
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Get a single customer by ID
export async function getCustomerApi(id: string): Promise<CustomerResponse> {
  try {
    const res = await axios.get(`/customers/${id}`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Update a customer
export async function updateCustomerApi(
  id: string,
  data: UpdateCustomerDto
): Promise<CustomerIdResponse> {
  try {
    const res = await axios.patch(`/customers/${id}`, data);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Delete a customer
export async function deleteCustomerApi(
  id: string
): Promise<CustomerIdResponse> {
  try {
    const res = await axios.delete(`/customers/${id}`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Bulk delete customers
export async function bulkDeleteCustomersApi(
  data: BulkDeleteCustomerDto
): Promise<BulkDeleteCustomerResponse> {
  try {
    const res = await axios.delete("/customers/bulk", { data });
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Check customer name availability
export async function checkCustomerNameAvailabilityApi(
  customerDisplayName: string,
  excludeId?: string
): Promise<CustomerNameAvailabilityResponse> {
  try {
    const queryParams = new URLSearchParams();
    queryParams.append("customerDisplayName", customerDisplayName);
    if (excludeId) queryParams.append("excludeId", excludeId);

    const res = await axios.get(
      `/customers/check-name-availability?${queryParams.toString()}`
    );
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Check customer email availability
export async function checkCustomerEmailAvailabilityApi(
  email: string,
  excludeId?: string
): Promise<CustomerEmailAvailabilityResponse> {
  try {
    const queryParams = new URLSearchParams();
    queryParams.append("email", email);
    if (excludeId) queryParams.append("excludeId", excludeId);

    const res = await axios.get(
      `/customers/check-email-availability?${queryParams.toString()}`
    );
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Helper functions for demo/development
export async function getCustomersTableData(
  params: GetCustomersSchema,
  isDemo: boolean = false
): Promise<CustomerPaginatedResponse> {
  if (isDemo) {
    const { getDemoCustomersTableData } = await import("./demo");
    return getDemoCustomersTableData(params);
  }
  return getCustomersApi(params);
}

export async function getCustomersSlim(
  isDemo: boolean = false
): Promise<SimpleCustomerResponse> {
  if (isDemo) {
    const { getDemoCustomersSlim } = await import("./demo");
    return getDemoCustomersSlim();
  }
  return getCustomersSlimApi();
}

export async function getCustomer(
  id: string,
  isDemo: boolean = false
): Promise<CustomerResponse> {
  if (isDemo) {
    const { getDemoCustomer } = await import("./demo");
    return getDemoCustomer(id);
  }
  return getCustomerApi(id);
}

export async function checkCustomerNameAvailability(
  customerDisplayName: string,
  excludeId?: string,
  isDemo: boolean = false
): Promise<CustomerNameAvailabilityResponse> {
  if (isDemo) {
    const { checkDemoCustomerNameAvailability } = await import("./demo");
    return checkDemoCustomerNameAvailability(customerDisplayName, excludeId);
  }
  return checkCustomerNameAvailabilityApi(customerDisplayName, excludeId);
}

export async function checkCustomerEmailAvailability(
  email: string,
  excludeId?: string,
  isDemo: boolean = false
): Promise<CustomerEmailAvailabilityResponse> {
  if (isDemo) {
    const { checkDemoCustomerEmailAvailability } = await import("./demo");
    return checkDemoCustomerEmailAvailability(email, excludeId);
  }
  return checkCustomerEmailAvailabilityApi(email, excludeId);
}

export async function createCustomer(
  data: CreateCustomerDto,
  isDemo: boolean = false
): Promise<CustomerIdResponse> {
  if (isDemo) {
    const { createDemoCustomer } = await import("./demo");
    return createDemoCustomer(data);
  }
  return createCustomerApi(data);
}

export async function updateCustomer(
  id: string,
  data: UpdateCustomerDto,
  isDemo: boolean = false
): Promise<CustomerIdResponse> {
  if (isDemo) {
    const { updateDemoCustomer } = await import("./demo");
    return updateDemoCustomer(id, data);
  }
  return updateCustomerApi(id, data);
}

export async function deleteCustomer(
  id: string,
  isDemo: boolean = false
): Promise<CustomerIdResponse> {
  if (isDemo) {
    const { deleteDemoCustomer } = await import("./demo");
    return deleteDemoCustomer(id);
  }
  return deleteCustomerApi(id);
}

export async function bulkCreateCustomers(
  data: BulkCreateCustomerDto,
  isDemo: boolean = false
): Promise<BulkCustomerIdsResponse> {
  if (isDemo) {
    const { bulkCreateDemoCustomers } = await import("./demo");
    return bulkCreateDemoCustomers(data.customers);
  }
  return bulkCreateCustomersApi(data);
}

export async function bulkDeleteCustomers(
  data: BulkDeleteCustomerDto,
  isDemo: boolean = false
): Promise<BulkDeleteCustomerResponse> {
  if (isDemo) {
    const { bulkDeleteDemoCustomers } = await import("./demo");
    return bulkDeleteDemoCustomers(data.customerIds);
  }
  return bulkDeleteCustomersApi(data);
}

// Search customers for autocomplete
export async function searchCustomersAutocompleteApi(
  search?: string,
  limit: number = 10
): Promise<CustomerAutocompleteResponse> {
  try {
    const searchParams = new URLSearchParams();

    if (search) searchParams.append("search", search);
    searchParams.append("limit", String(limit));

    const res = await axios.get(
      `/customers/autocomplete?${searchParams.toString()}`
    );
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Blacklist a customer
export async function blacklistCustomerApi(
  id: string,
  data: BlacklistCustomerDto
): Promise<BlacklistResponse> {
  try {
    const res = await axios.post(`/customers/${id}/blacklist`, data);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Unblacklist a customer
export async function unblacklistCustomerApi(
  id: string
): Promise<UnblacklistResponse> {
  try {
    const res = await axios.delete(`/customers/${id}/blacklist`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Get blacklisted customers
export async function getBlacklistedCustomersApi(
  params: GetCustomersSchema
): Promise<CustomerPaginatedResponse> {
  try {
    const queryParams = new URLSearchParams();

    if (params.page) queryParams.append("page", params.page.toString());
    if (params.limit) queryParams.append("limit", params.limit.toString());
    if (params.from) queryParams.append("from", params.from);
    if (params.to) queryParams.append("to", params.to);
    if (params.customerDisplayName)
      queryParams.append("customerDisplayName", params.customerDisplayName);
    if (params.email) queryParams.append("email", params.email);
    if (params.companyName)
      queryParams.append("companyName", params.companyName);
    if (params.sort && params.sort.length > 0) {
      queryParams.append("sort", JSON.stringify(params.sort));
    }

    const res = await axios.get(`/customers/blacklisted?${queryParams.toString()}`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Helper functions with demo support
export async function blacklistCustomer(
  id: string,
  data: BlacklistCustomerDto,
  isDemo: boolean = false
): Promise<BlacklistResponse> {
  if (isDemo) {
    const { blacklistDemoCustomer } = await import("./demo");
    return blacklistDemoCustomer(id, data);
  }
  return blacklistCustomerApi(id, data);
}

export async function unblacklistCustomer(
  id: string,
  isDemo: boolean = false
): Promise<UnblacklistResponse> {
  if (isDemo) {
    const { unblacklistDemoCustomer } = await import("./demo");
    return unblacklistDemoCustomer(id);
  }
  return unblacklistCustomerApi(id);
}

export async function getBlacklistedCustomers(
  params: GetCustomersSchema,
  isDemo: boolean = false
): Promise<CustomerPaginatedResponse> {
  if (isDemo) {
    const { getDemoBlacklistedCustomers } = await import("./demo");
    return getDemoBlacklistedCustomers(params);
  }
  return getBlacklistedCustomersApi(params);
}
